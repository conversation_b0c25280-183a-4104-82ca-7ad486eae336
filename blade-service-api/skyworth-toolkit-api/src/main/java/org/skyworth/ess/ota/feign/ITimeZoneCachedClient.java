package org.skyworth.ess.ota.feign;

import org.skyworth.ess.ota.constant.ToolkitConstant;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;
import java.util.Set;

@FeignClient(
	value = ToolkitConstant.APPLICATION_TOOLKIT_NAME,
	fallback = TimeZoneCachedClientCallBack.class
)
public interface ITimeZoneCachedClient {
	String API_PREFIX = "/timeZoneCached";
	String API_PREFIX_PUT = API_PREFIX + "/put";
	String API_PREFIX_REMOVEALL = API_PREFIX + "/removeAll";
	String API_PREFIX_GET = API_PREFIX + "/get";
	String API_PREFIX_GETALL = API_PREFIX + "/getAll";
	//String API_PREFIX_GETKAFKA = API_PREFIX + "/getKafka";

	@PostMapping(value = API_PREFIX_PUT)
	void putCache(@RequestParam("plantId") String plantId , @RequestParam("timeZone") String timeZone);

	@PostMapping(value = API_PREFIX_GETALL)
	Map<String, String> getAllCache(@RequestParam("plantIdSet") Set<String> plantIdSet);

	@PostMapping(value = API_PREFIX_REMOVEALL)
	void removeAllCache(@RequestParam("plantIdSet") Set<String> plantIdSet);

	@PostMapping(value = API_PREFIX_GET)
	String getCache(@RequestParam("plantId") Long plantId);

	//@PostMapping(value = API_PREFIX_GETKAFKA)
	//String baseEnergyKafkaHandler(@RequestBody List<JSONObject> jsonObjectList);
}
