/*
 * Copyright (c) 2018-2023. Skyworth All rights reserved
 */
package org.skyworth.ess.ota.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * OTA升级包 视图实体类
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
@Data
@ApiModel(value = "OtaUpdatePack对象", description = "OTA升级包")
public class OtaUpdatePackVO implements Serializable {
	private static final long serialVersionUID = 1L;
	@ApiModelProperty(value = "主键id")
	private String id;
	@ApiModelProperty(value = "当前版本")
	private String currentVersionNumber;
	/**
	 * 大类型（device智能能量变换器、battery储能）
	 */
	@ApiModelProperty(value = "大类型（device智能能量变换器、battery储能、app）")
	private String bigType;
	/**
	 * 小类（智能能量变换器：master主机软件版本、slave从机固件版本、EMS固件版本、dc固件版本）
	 */
	@ApiModelProperty(value = "小类（智能能量变换器：master主机软件版本、slave从机固件版本、EMS固件版本、dc固件版本）")
	private String smallType;
	/**
	 * 版本号
	 */
	@ApiModelProperty(value = "下发版本号")
	private String versionNumber;
	/**
	 * 公司
	 */
	@ApiModelProperty(value = "公司")
	private String company;
	/**
	 * 升级包地址
	 */
	@ApiModelProperty(value = "升级包地址")
	private String packCdnUrl;
	/**
	 * 是否最新版本
	 */
	@ApiModelProperty(value = "是否最新版本")
	private String isNewVersion;
	/**
	 * 是否强制升级，默认为'否'
	 */
	@ApiModelProperty(value = "是否强制升级，默认为'否'")
	private Byte whetherToForceUpgrade;
}
