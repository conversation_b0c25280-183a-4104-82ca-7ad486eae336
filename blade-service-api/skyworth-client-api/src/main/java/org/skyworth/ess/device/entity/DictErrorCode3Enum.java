package org.skyworth.ess.device.entity;

public enum DictErrorCode3Enum {

	Reserved(0,"CY - RSD(Rapid Shutdown) abnormal"),
	EMSEEPROM(1,"Cv - EMS/CSB EEPROM fail"),
	Reserved2(2,"B9 - PV open warning"),
	deviceAbnormal(3,"BA - PID device abnormal"),
	AFC<PERSON>ost(4,"Bb - AFCI lost"),
	DataLogger(5,"CH - Data logger lost "),
	MeterLost(6,"CJ - Meter lost"),
	Reserved3(7,"Co - Inverter lost"),
	abnormal(8,"A8 - Grid N abnormal"),
	defective(9,"BC - Surge Protection Devices (SPD) defective"),
	warning(10,"P1 - Parallel ID warning"),
	signalWarning(11,"P2 - Parallel SYN signal warning"),
	BATAbnormal(12,"P3 - Parallel BAT abnormal"),
	GRIDAbnormal(13,"P4 - Parallel GRID abnormal"),
	voltageAbnormal (14,"Dd - Generator voltage abnormal"),
	Reserved4(15,"P5 - Phase sequence abnormal");

	private int code;
	private String message;

	private DictErrorCode3Enum(int code, String message) {
		this.code = code;
		this.message = message;
	}

	public int getCode() {
		return code;
	}

	public void setCode(int code) {
		this.code = code;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public static DictErrorCode3Enum match(int key) {

		DictErrorCode3Enum result = null;

		for (DictErrorCode3Enum s : values()) {
			if (s.getCode()==key) {
				result = s;
				break;
			}
		}

		return result;
	}

	public static DictErrorCode3Enum catchMessage(String msg) {

		DictErrorCode3Enum result = null;

		for (DictErrorCode3Enum s : values()) {
			if (s.getMessage().equals(msg)) {
				result = s;
				break;
			}
		}

		return result;
	}
}
