package org.skyworth.ess.device.client;

import org.springblade.common.constant.CommonConstant;
import org.springblade.core.tool.api.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

@FeignClient(
	value = CommonConstant.APPLICATION_CLIENT_NAME,
        fallback = IErrorInfoClientCallBack.class
)
public interface IErrorInfoClient {


    String API_PREFIX = "/event";
    String address = API_PREFIX + "/errorInfo";
	/**
	 * 告警信息
	 * @param error 异常信息map
	 */
    @PostMapping(address)
    R<String> errorInfo(@RequestBody Map<String, Map<String,Object>> error);
}
