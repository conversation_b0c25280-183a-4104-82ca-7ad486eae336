package org.skyworth.ess.device.client;

import com.alibaba.fastjson.JSONObject;
import org.springblade.core.tool.api.R;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public class DeviceIssueBizFallBack implements IDeviceIssueBiz {

    @Override
    public Map<String,String> dataIssueToDevice(JSONObject jsonObject) {
        return null;
    }

	@Override
	public Map<String, String> setting(JSONObject jsonObject) {
		return null;
	}

	@Override
	public Map<String, String> getDeviceTime(JSONObject jsonObject) {
		return null;
	}

	@Override
	public Map<String, String> settingLazzen(JSONObject jsonObject) {
		return Map.of();
	}

	@Override
	public R errorInfo(List<JSONObject> jsonObject) {
		return null;
	}

	@Override
	public R setDeviceTime(List<JSONObject> jsonObject) {
		return null;
	}

}
