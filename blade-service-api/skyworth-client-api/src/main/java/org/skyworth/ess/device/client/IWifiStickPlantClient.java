package org.skyworth.ess.device.client;

import org.springblade.common.constant.CommonConstant;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(
	value = CommonConstant.APPLICATION_CLIENT_NAME,
        fallback = WifiStickPlantClientFallBack.class
)
public interface IWifiStickPlantClient {

    String API_PREFIX = "/wifiInfo";
    String address = API_PREFIX + "/heartBeat/updateStatus";

    @PostMapping(address)
    void updateHeartStatus(@RequestBody List<String> wifiSn);
}
