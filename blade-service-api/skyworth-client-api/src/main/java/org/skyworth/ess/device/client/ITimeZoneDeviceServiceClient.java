package org.skyworth.ess.device.client;

import com.alibaba.fastjson.JSONObject;
import org.skyworth.ess.device.entity.TimeZoneDevice;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.tool.api.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> - xuehongjiang
 * @version 1.0
 * @project
 * @description
 * @create-time 2024年5月8日 14:16:50
 */
@FeignClient(
	value = CommonConstant.APPLICATION_CLIENT_NAME,
	fallback = ITimeZoneDeviceServiceCallBack.class
)
public interface ITimeZoneDeviceServiceClient {
	String API_PREFIX = "/timeZoneDevice";
	String address = API_PREFIX + "/getList";
	String getMapFromCacheByPlantIdList = API_PREFIX + "/getMapFromCacheByPlantIdList";
	String adjustEquipmentTime = API_PREFIX + "/adjustEquipmentTime";

	@PostMapping(address)
	List<TimeZoneDevice> getList(@RequestBody List<String> deviceSnList);

	@PostMapping(adjustEquipmentTime)
	R adjustEquipmentTime(@RequestBody JSONObject jsonObject);

	@PostMapping(getMapFromCacheByPlantIdList)
	R<Map<String, String>> getMapFromCacheByPlantIdList(@RequestBody List<Long> plantIdList);
}
