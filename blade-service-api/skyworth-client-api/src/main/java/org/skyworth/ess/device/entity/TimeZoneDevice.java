package org.skyworth.ess.device.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import java.io.Serializable;


/**
 * 智能能量变换器与时区绑定关系;
 * <AUTHOR> wxc
 * @date : 2023-12-11
 */
@ApiModel(value = "智能能量变换器与时区绑定关系",description = "")
@TableName("time_zone_device")
@Data
@EqualsAndHashCode(callSuper = true)
public class TimeZoneDevice extends TenantEntity implements Serializable,Cloneable{

	@ApiModelProperty(name = "智能能量变换器/设备SN",notes = "智能能量变换器/设备SN")
	private String deviceSerialNumber ;

	@ApiModelProperty(name = "智能能量变换器/设备SN",notes = "智能能量变换器/设备SN")
	private String timeZone ;


}
