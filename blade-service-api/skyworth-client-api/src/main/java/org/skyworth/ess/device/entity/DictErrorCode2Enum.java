package org.skyworth.ess.device.entity;

public enum DictErrorCode2Enum {

	GridVoltage(0,"A0 - Grid over voltage"),
	GridUnderVoltage(1,"A1 - Grid under voltage"),
	absent(2,"A2 - Grid absent"),
	overFrequency(3,"A3 - Grid over frequency"),
	underFrequency(4,"A4 - Grid under frequency"),
	overVoltage(5,"B0 - PV over voltage"),
	insulationAbnormal(6,"B1 - PV insulation abnormal"),
	currentAbnormal(7,"B2 - Leakage current abnormal "),
	limitState(8,"CL - Inverter in power limit state"),
	supplyAbnormal(9,"C0 - Internal power supply abnormal"),
	stringAbnormal(10,"B3 - PV string abnormal"),
	underVoltage(11,"B4 - PV under voltage"),
	irradiationWeak(12,"B5 - PV irradiation weak"),
	GridAbnormal(13,"A6 - Grid abnormal"),
	faultDetection (14,"C1 - Arc fault detection"),
	voltageHigh(15,"A7 - AC moving average voltage high");

	private int code;
	private String message;

	private DictErrorCode2Enum(int code, String message) {
		this.code = code;
		this.message = message;
	}

	public int getCode() {
		return code;
	}

	public void setCode(int code) {
		this.code = code;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public static DictErrorCode2Enum match(int key) {

		DictErrorCode2Enum result = null;

		for (DictErrorCode2Enum s : values()) {
			if (s.getCode()==key) {
				result = s;
				break;
			}
		}

		return result;
	}

	public static DictErrorCode2Enum catchMessage(String msg) {

		DictErrorCode2Enum result = null;

		for (DictErrorCode2Enum s : values()) {
			if (s.getMessage().equals(msg)) {
				result = s;
				break;
			}
		}

		return result;
	}
}
