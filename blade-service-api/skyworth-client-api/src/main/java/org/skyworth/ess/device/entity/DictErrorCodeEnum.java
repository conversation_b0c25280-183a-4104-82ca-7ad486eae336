package org.skyworth.ess.device.entity;

public enum DictErrorCodeEnum {

	DcBias(0,"C2 - Inverter over dc-bias current"),
	abnormal(1,"C3 - Inverter relay abnormal"),
	Remote(2,"Cn - Remote off"),
	temperature(3,"C5 - Inverter over temperature"),
	GFCI_abnormal(4,"C6 - GFCI abnormal"),
	PV_reverse(5,"B7 - PV string reverse "),
	Type_error(6,"C7 - System type error"),
	Fan_abnormal(7,"C8 - Fan abnormal"),
	under_voltage(8,"C9 - Dc-link unbalance or under voltage"),
	over_voltage(9,"CA - Dc-link over voltage"),
	communication_error(10,"Cb - Internal communication error"),
	Software_incompatibility(11,"CC - Software incompatibility"),
	storage_error(12,"Cd - Internal storage error"),
	Data_inconsistency(13,"CE - Data inconsistency"),
	Inverter_abnormal (14,"CF - Inverter abnormal "),
	Boost_abnormal(15,"CG - Boost abnormal");

	private int code;
	private String message;

	private DictErrorCodeEnum(int code, String message) {
		this.code = code;
		this.message = message;
	}

	public int getCode() {
		return code;
	}

	public void setCode(int code) {
		this.code = code;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public static DictErrorCodeEnum match(int key) {

		DictErrorCodeEnum result = null;

		for (DictErrorCodeEnum s : values()) {
			if (s.getCode()==key) {
				result = s;
				break;
			}
		}

		return result;
	}

	public static DictErrorCodeEnum catchMessage(String msg) {

		DictErrorCodeEnum result = null;

		for (DictErrorCodeEnum s : values()) {
			if (s.getMessage().equals(msg)) {
				result = s;
				break;
			}
		}

		return result;
	}
}
