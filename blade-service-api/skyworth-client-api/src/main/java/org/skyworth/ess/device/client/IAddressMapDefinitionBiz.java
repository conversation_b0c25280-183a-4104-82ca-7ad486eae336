package org.skyworth.ess.device.client;

import org.springblade.common.constant.CommonConstant;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

@FeignClient(
		value = CommonConstant.APPLICATION_CLIENT_NAME,
        fallback = IAddressMapDefinitionBizFallback.class
)
public interface IAddressMapDefinitionBiz {

    String API_PREFIX = "/deviceLog";
    String address = API_PREFIX + "/address/definition";

    @PostMapping(address)
    Map<String, Map<String, String>> getAllAddressMap(@RequestBody Map<String, Object> addressMapDefinition);
}
