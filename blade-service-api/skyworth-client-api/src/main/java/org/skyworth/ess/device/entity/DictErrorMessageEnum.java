package org.skyworth.ess.device.entity;

public enum DictErrorMessageEnum {

	absent(0,"D1 - Battery absent"),
	over_voltage(1,"D2 - Battery over voltage"),
	under_voltage(2,"D3 - Battery under voltage"),
	over_current(3,"D4 - Battery discharge over current"),
	over_temperature(4,"D5 - Battery over temperature"),
	under_temperature(5,"D6 - Battery under temperature"),
	wire_reversed(6,"A9 - Neutral live wire reversed "),
	voltage_abnormal(7,"D7 - Back up output voltage abnormal"),
	Inverter_BMS(8,"D8 - Communication error (Inverter-BMS)"),
	communication_loss(9,"D9 - Internal communication loss(E-M) "),
	M_D(10,"DA - Internal communication loss(M-D) "),
	DCDC(11,"CU - Dcdc abnormal"),
	bias_voltage(12,"CP - Back up over dc-bias voltage"),
	circuit(13,"Db - Back up short circuit"),
	load (14,"DC - Back up over load"),
	Reserved(15,"Dn - Battery reserved");

	private int code;
	private String message;

	private DictErrorMessageEnum(int code, String message) {
		this.code = code;
		this.message = message;
	}

	public int getCode() {
		return code;
	}

	public void setCode(int code) {
		this.code = code;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public static DictErrorMessageEnum match(int key) {

		DictErrorMessageEnum result = null;

		for (DictErrorMessageEnum s : values()) {
			if (s.getCode()==key) {
				result = s;
				break;
			}
		}

		return result;
	}

	public static DictErrorMessageEnum catchMessage(String msg) {

		DictErrorMessageEnum result = null;

		for (DictErrorMessageEnum s : values()) {
			if (s.getMessage().equals(msg)) {
				result = s;
				break;
			}
		}

		return result;
	}
}
