package org.skyworth.ess.device.client;

import com.alibaba.fastjson.JSONObject;
import org.springblade.common.constant.CommonConstant;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(
	value = CommonConstant.APPLICATION_CLIENT_NAME,
	fallback = SoftVersionBizClientCallBack.class
)
public interface ISoftVersionBizClient {

	String API_PREFIX = "/softVersion";
	String address = API_PREFIX + "/update";

	@PostMapping(address)
	void updateVersion(@RequestBody List<JSONObject> jsonObjects);

}
