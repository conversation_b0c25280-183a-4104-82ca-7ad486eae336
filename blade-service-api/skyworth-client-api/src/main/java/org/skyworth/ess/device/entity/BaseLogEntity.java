package org.skyworth.ess.device.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

public class BaseLogEntity {
	/** 站点ID */
	@ApiModelProperty(name = "站点ID",notes = "")
	private Integer plantId ;

	/** 设备时间，设备上报时时间 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@ApiModelProperty(name = "设备时间，设备上报时时间",notes = "")
	private Date deviceDateTime ;

	/** modbus协议版本 */
	@ApiModelProperty(name = "modbus协议版本",notes = "")
	private String modbusProtocolVersion ;


	/** 智能能量变换器/设备SN */
	@ApiModelProperty(name = "智能能量变换器/设备SN",notes = "")
	private String deviceSerialNumber ;

	/** 租户ID */
	@ApiModelProperty(name = "租户ID",notes = "")
	private String tenantId ;

	/** 创建人 */
	@ApiModelProperty(name = "创建人",notes = "")
	private Integer createUser ;
	/** 创建部门 */
	@ApiModelProperty(name = "创建部门",notes = "")
	private Integer createDept ;
	/** 创建时间 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@ApiModelProperty(name = "创建时间",notes = "")
	private Date createTime ;
	/** 修改人 */
	@ApiModelProperty(name = "修改人",notes = "")
	private Integer updateUser ;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")

	@ApiModelProperty(name = "修改时间",notes = "")
	private Date updateTime ;

	/** 创建人账号 */
	@ApiModelProperty(name = "创建人账号",notes = "")
	private String createUserAccount ;
	/** 更新人账号 */
	@ApiModelProperty(name = "更新人账号",notes = "")
	private String updateUserAccount ;

	public String getCreateUserAccount() {
		return createUserAccount;
	}

	public void setCreateUserAccount(String createUserAccount) {
		this.createUserAccount = createUserAccount;
	}

	public String getUpdateUserAccount() {
		return updateUserAccount;
	}

	public void setUpdateUserAccount(String updateUserAccount) {
		this.updateUserAccount = updateUserAccount;
	}

	/** 站点ID */
	public Integer getPlantId(){
		return this.plantId;
	}
	/** 站点ID */
	public void setPlantId(Integer plantId){
		this.plantId=plantId;
	}

	/** 设备时间，设备上报时时间 */
	public Date getDeviceDateTime(){
		return this.deviceDateTime;
	}
	/** 设备时间，设备上报时时间 */
	public void setDeviceDateTime(Date deviceDateTime){
		this.deviceDateTime=deviceDateTime;
	}

	/** modbus协议版本 */
	public String getModbusProtocolVersion(){
		return this.modbusProtocolVersion;
	}
	/** modbus协议版本 */
	public void setModbusProtocolVersion(String modbusProtocolVersion){
		this.modbusProtocolVersion=modbusProtocolVersion;
	}

	/** 智能能量变换器/设备SN */
	public String getDeviceSerialNumber(){
		return this.deviceSerialNumber;
	}
	/** 智能能量变换器/设备SN */
	public void setDeviceSerialNumber(String deviceSerialNumber){
		this.deviceSerialNumber=deviceSerialNumber;
	}

	public Integer getCreateUser() {
		return createUser;
	}

	public void setCreateUser(Integer createUser) {
		this.createUser = createUser;
	}

	public Integer getCreateDept() {
		return createDept;
	}

	public void setCreateDept(Integer createDept) {
		this.createDept = createDept;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Integer getUpdateUser() {
		return updateUser;
	}

	public void setUpdateUser(Integer updateUser) {
		this.updateUser = updateUser;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public String getTenantId() {
		return tenantId;
	}

	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}
}
