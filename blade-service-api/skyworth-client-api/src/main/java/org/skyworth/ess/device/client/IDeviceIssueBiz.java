package org.skyworth.ess.device.client;


import com.alibaba.fastjson.JSONObject;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.tool.api.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

@FeignClient(
	value = CommonConstant.APPLICATION_TOOLKIT_NAME,
        fallback = DeviceIssueBizFallBack.class
)
public interface IDeviceIssueBiz {

    String API_PREFIX = "/issue";

    String address = API_PREFIX + "/workMode";

	String getDevice=API_PREFIX+"/getDevice";

	String setting=API_PREFIX+"/setting";

	String SETTING_LAZZEN=API_PREFIX+"/setting/lazzen";

	String time=API_PREFIX+"/getDeviceTime";

	String errorInfo=API_PREFIX + "/errorInfo";

	String setDeviceTime=API_PREFIX+"/setDeviceTime";

    @PostMapping(address)
    Map<String,String> dataIssueToDevice(@RequestBody JSONObject jsonObject);

	@PostMapping(setting)
	Map<String,String> setting(@RequestBody JSONObject jsonObject);

	@PostMapping(time)
	Map<String,String> getDeviceTime(@RequestBody JSONObject jsonObject);

	@PostMapping(SETTING_LAZZEN)
	Map<String,String> settingLazzen(@RequestBody JSONObject jsonObject);

	@PostMapping(errorInfo)
	R errorInfo(@RequestBody List<JSONObject> jsonObject);

	@PostMapping(setDeviceTime)
	R setDeviceTime(@RequestBody List<JSONObject> jsonObject);

}
