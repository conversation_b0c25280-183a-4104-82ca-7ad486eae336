package org.skyworth.ess.device.client;

import com.alibaba.fastjson.JSONObject;
import org.skyworth.ess.device.entity.TimeZoneDevice;
import org.springblade.core.tool.api.R;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> - xuehongjiang
 * @version 1.0
 * @project
 * @description
 * @create-time 2024年5月8日 14:17:20
 */
@Component
public class ITimeZoneDeviceServiceCallBack  implements ITimeZoneDeviceServiceClient{
	@Override
	public List<TimeZoneDevice> getList(List<String> deviceSnList){
		return new ArrayList<>();
	}

	@Override
	public R adjustEquipmentTime(JSONObject jsonObject) {
		return null;
	}

	@Override
	public R<Map<String, String>> getMapFromCacheByPlantIdList(List<Long> plantIdList) {
		return null;
	}
}
