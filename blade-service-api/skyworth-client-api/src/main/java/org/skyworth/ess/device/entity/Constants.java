package org.skyworth.ess.device.entity;

public class Constants {

	public static final String ONE = "1";
	public static final String SECOND = "2";
	public static final String THIRD = "3";
	public static final String FOUR = "4";

	public static final Integer DIGITAL_ZERO = 0;
	public static final Integer DIGITAL_ONE = 1;
	public static final Integer DIGITAL_SECOND = 2;
	public static final Integer DIGITAL_THIRD = 3;

	public static final String PREFIX_DEVICE_LOG = "DeviceLog2_";

	public static final String REAL_TOPIC = "server_iot_realTime_data";  //mqtt 5分钟实时数据上报topic  并将数据转发至kafka（也是同一个topic）

	public static final String HEART_BEAT_TOPIC = "heart_beat_wifi";  //wifi棒的心跳信息

	public static final String SUBSCRIBE_MODE_TOPIC = "subscribe_mode_server";  //订阅指令下发结果, 模式更改指令

	public static final String PUBLIC_MODE_TOPIC = "public_mode_server:$Sn";  //发布模式指令，

	public static final String MSG_PUSH_APP = "success_distribution_net:$Sn";   //推送消息至app端

	public static final String abnormal_data_server = "error_info_realTime_server";   //异常消息

	public static final String SETTING_PARAM_TOPIC = "setting_param:$Sn";  //高级设置指令，
	public static final String LAZZEN_WRITE_DATA_TOPIC = "LN/DZCloud/NDB5E/WriteData/$Sn";  //高级设置指令，

	public static final String U16 = "U16";
	public static final String U32 = "U32";
	public static final String S16 = "S16";
	public static final String S32 = "S32";

	public static final String UINT16 = "uint16";
	public static final String UINT32 = "uint32";

	public static final String INT16 = "int16";

	public static final String STR = "String";

	public static final String ASCII = "ASCII";

	public static final String INVERTER_MODE = "101D";

	public static final String ERROR_CODE = "101E";

	public static final String ERROR_CODE2 = "101F";

	public static final String ERROR_CODE3 = "1020";

	public static final String ERROR_CODE_MESSAGE = "2013";

	public static final String ERROR_CODE_MESSAGE2 = "2213";

	public static final String MODBUS_PROTOCOL_VERSION = "1A18";

	public static final String MPPT_NUMBER = "1A3B";

	public static final String GRID_PHASE_NUMBER = "1A48";

	public static final String Hybrid_work_mode = "2100";

	public static final String Grid_charge = "2115";

	public static final String Battery_type_selection = "2110";

	public static final String off_grid_mode = "211C";


	public static final String OTA_DEVICE_UPGRADE = "ota_device_upgrade_$Sn";//OTA设备软件升级下发指令

	public static final String SUB_OTA_OBTAINING_RESULTS = "sub_ota_obtaining_results";

	public static final String SETTING_RESULTS = "setting_param_result";

	public static final String SETTING_ISSUE = "setting_param:$Sn";

	public static final String Device_Disconnected = "Device_Disconnected:$Sn";  //设备断开连接，

	public static final String DEVICE_GET_TIME = "device_get_time:$Sn";   //获取设备时间topic

	public static final String DEVICE_GET_TIME_RESULT = "device_get_time_result";  //设备返回时间topic


	public static final String PUBLIC_WIFI_CONNECTED = "public_wifi_connected"; //WiFi棒发送联网成功topic

	/**
	 * 配网成功后设备发送mqtt给后台的topic (kafka相同topic)
	 */
	public static final String SERVER_DISTRIBUTION_NETWORK = "server_distribution_network";

	/**
	 * 智能能量变换器与站点解绑topic
	 */
	public static final String UNBIND_INVERTER = "server_unbind_inverter:$Sn";

	/**
	 * 接受到硬件返回的ota结果以后，进行业务处理，再返回给app的升级结果
	 */
	public static final String PUB_OTA_OBTAINING_RESULTS_APP = "pub_ota_obtaining_results_app:$Sn";
	/**
	 * 设备下线通知
	 */
	public static final String PORTABLE_DEVICE_OFFLINE = "portable_device_offline_$Sn";
	/**
	 * 脱机升级异常回写topic
	 */
	public static final String OTA_OFFLINE_UPGRADE_EXCEPTION = "ota_offline_upgrade_exception";

	/**
	 * 逆变器主动获取服务器时间
	 */
	public static final String INVERTER_GET_SERVER_TIME = "inverter_get_server_time";

}
