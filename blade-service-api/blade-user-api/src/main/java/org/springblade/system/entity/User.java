/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.system.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import java.util.Date;
import java.util.List;

/**
 * 实体类
 *
 * <AUTHOR>
 */
@Data
@TableName("blade_user")
@EqualsAndHashCode(callSuper = true)
public class User extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 用户编号
	 */
	private String code;
	/**
	 * 用户平台
	 */
	private Integer userType;
	/**
	 * 账号
	 */
	private String account;
	/**
	 * 密码
	 */
	private String password;
	/**
	 * 昵称
	 */
	private String name;
	/**
	 * 真名
	 */
	private String realName;
	/**
	 * 头像
	 */
	private String avatar;
	/**
	 * 邮箱
	 */
	private String email;
	/**
	 * 手机
	 */
	private String phone;
	/**
	 * 生日
	 */
	private Date birthday;
	/**
	 * 性别
	 */
	private Integer sex;
	/**
	 * 角色id
	 */
	private String roleId;
	/**
	 * 角色code
	 */
	@TableField(exist = false)
	private String roleCode;
	/**
	 * 部门id
	 */
	private String deptId;
	/**
	 * 岗位id
	 */
	private String postId;
	/**
	 * 用户来源：注册register；分配allocation
	 */
	private String userFrom;
	/**
	 * 验证码
	 */
	@TableField(exist = false)
	private String verificationCode;
	/**
	 * 注册类别 手机号/邮件注册
	 */
	@TableField(exist = false)
	private String registerType;
	/**
	 * firstName
	 */
	private String firstName;
	/**
	 * lastName
	 */
	private String lastName;
	/**
	 * 电话区号
	 */
	private String phoneDiallingCode;
	/**
	 * 短信随机id
	 */
	@TableField(exist = false)
	private String smsId;

//	/**
//	 * 用户类别 代理商，安装商，电气工程师
//	 */
//	@TableField(exist = false)
//	private List<String> userRoleType;

	/**
	 * 代理商编码
	 */
	@TableField(exist = false)
	private String agentNumber;

	/**
	 * 角色名称
	 */
	@TableField(exist = false)
	private String roleName;


	/**
	 * 代理商部门id
	 */
	@TableField(exist = false)
	private String agencyDeptMsg;


	/**
	 * app类型
	 */
	@TableField(exist = false)
	private String appType;
	/**
	 * 代理商根据电话和名称下拉
	 */
	@TableField(exist = false)
	private String telephoneOrUserName;

	/**
	 * user扩展表 app 字段
	 */
	@TableField(exist = false)
	private String countryCode;
	@TableField(exist = false)
	private String provinceCode;
	@TableField(exist = false)
	private String cityCode;
	@TableField(exist = false)
	private String countyCode;
	@TableField(exist = false)
	private String countryName;
	@TableField(exist = false)
	private String provinceName;
	@TableField(exist = false)
	private String cityName;
	@TableField(exist = false)
	private String countyName;
	// 代理商id
	@TableField(exist = false)
	private Long agentId;
	// client端对应agent部门id
	@TableField(exist = false)
	private String agentDeptIdInClient;
	// client端对应agent角色id
	@TableField(exist = false)
	private String agentRoleIdInClient;

	/**
	 * 修改绑定类别 1 手机 2 邮箱
	 */
	@TableField(exist = false)
	private String updateType;
	/**
	 * true为解绑
	 */
	@TableField(exist = false)
	private Boolean unBind;
}
