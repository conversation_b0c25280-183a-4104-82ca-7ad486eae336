/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.system.feign;


import org.springblade.common.vo.UserBatchVO;
import org.springblade.core.launch.constant.AppConstant;
import org.springblade.core.tool.api.R;
import org.springblade.system.entity.User;
import org.springblade.system.entity.UserInfo;
import org.springblade.system.entity.UserOauth;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * User Feign接口类
 *
 * <AUTHOR>
 */
@FeignClient(
	value = AppConstant.APPLICATION_SYSTEM_NAME,
	fallbackFactory = UserClientFactory.class
)
public interface IUserClient {

	String API_PREFIX = "/client";
	String USER_INFO = API_PREFIX + "/user-info";
	String USER_INFO_BY_TYPE = API_PREFIX + "/user-info-by-type";
	String USER_INFO_BY_ID = API_PREFIX + "/user-info-by-id";
	String USER_INFO_BY_ACCOUNT = API_PREFIX + "/user-info-by-account";
	String USER_AUTH_INFO = API_PREFIX + "/user-auth-info";
	String SAVE_USER = API_PREFIX + "/save-user";
	String REMOVE_USER = API_PREFIX + "/remove-user";

	String USER_INFO_BY_LOGIN_NAME = API_PREFIX + "/user-info-by-login-name";
	String USER_DELETE_BY_USER_ID = API_PREFIX + "/user-delete-by-user-id";
	String USER_BY_USER_INFO = API_PREFIX + "/user-by-user-info";
	String ALL_USER_INFO = API_PREFIX + "/getAllUsers";
	String USER_LIST = API_PREFIX + "/user-list";
	String SYNCHRONIZED_USER_LIST = API_PREFIX + "synchronized-user-list";
	String USER_INFO_BY_WEB = API_PREFIX + "/user-info-web";

	/**
	 * 获取用户信息
	 *
	 * @param userId 用户id
	 * @return
	 */
	@GetMapping(USER_INFO_BY_ID)
	R<User> userInfoById(@RequestParam("userId") Long userId);


	/**
	 * 根据账号获取用户信息
	 *
	 * @param tenantId 租户id
	 * @param account  账号
	 * @return
	 */
	@GetMapping(USER_INFO_BY_ACCOUNT)
	R<User> userByAccount(@RequestParam("tenantId") String tenantId, @RequestParam("account") String account);

	/**
	 * 获取用户信息
	 *
	 * @param tenantId 租户ID
	 * @param account  账号
	 * @return
	 */
	@GetMapping(USER_INFO)
	R<UserInfo> userInfo(@RequestParam("tenantId") String tenantId, @RequestParam("account") String account);

	/**
	 * 获取用户信息
	 *
	 * @param tenantId 租户ID
	 * @param account  账号
	 * @param userType 用户平台
	 * @return
	 */
	@GetMapping(USER_INFO_BY_TYPE)
	R<UserInfo> userInfo(@RequestParam("tenantId") String tenantId, @RequestParam("account") String account, @RequestParam("userType") String userType
		,@RequestParam(name="phoneDiallingCode",required = false) String phoneDiallingCode);

	/**
	 * 获取第三方平台信息
	 *
	 * @param userOauth 第三方授权用户信息
	 * @return UserInfo
	 */
	@PostMapping(USER_AUTH_INFO)
	R<UserInfo> userAuthInfo(@RequestBody UserOauth userOauth);

	/**
	 * 新建用户
	 *
	 * @param user 用户实体
	 * @return
	 */
	@PostMapping(SAVE_USER)
	R<Boolean> saveUser(@RequestBody User user);

	/**
	 * 删除用户
	 *
	 * @param tenantIds 租户id集合
	 * @return
	 */
	@PostMapping(REMOVE_USER)
	R<Boolean> removeUser(@RequestParam("tenantIds") String tenantIds);

	/**
	 * 根据账号获取用户信息
	 *
	 * @param tenantId  租户id
	 * @param loginName 账号
	 * @return
	 */
	@GetMapping(USER_INFO_BY_LOGIN_NAME)
	R<UserInfo> userByLoginName(@RequestParam("tenantId") String tenantId, @RequestParam("loginName") String loginName
		, @RequestParam("loginType") String loginType, @RequestParam(name="phoneDiallingCode",required = false) String phoneDiallingCode);

	@GetMapping(USER_DELETE_BY_USER_ID)
	R deleteUserById(@RequestParam("userId") Long userId);

	@PostMapping(USER_BY_USER_INFO)
	R<User> userByUserInfo(@RequestBody User user);

	@PostMapping(ALL_USER_INFO)
	R<List<User>> getAllUsers();

	@PostMapping(USER_LIST)
	R<List<User>> getUserList(@RequestBody User user);
	/**
	 * 同步用户信息
	 * @param userUserBatchVO 入参
	 * @return R
	 * <AUTHOR>
	 * @since 2024/3/11 14:31
	 **/
	@PostMapping(SYNCHRONIZED_USER_LIST)
	R synchronousUserList(@RequestBody UserBatchVO<User> userUserBatchVO);

	@PostMapping(USER_INFO_BY_WEB)
	R<UserInfo> userInfoByWeb(@RequestParam("tenantId") String tenantId, @RequestParam("username") String username,
							  @RequestParam("name") String name, @RequestParam(name="phoneDiallingCode",required = false) String phoneDiallingCode);
}
