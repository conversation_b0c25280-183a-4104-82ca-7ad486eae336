/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.system.feign;


import com.alibaba.fastjson.JSONObject;
import org.springblade.core.launch.constant.AppConstant;
import org.springblade.core.tool.api.R;
import org.springblade.system.entity.User;
import org.springblade.system.entity.UserApp;
import org.springblade.system.vo.UserRegistrationVO;
import org.springblade.system.vo.UserStatisticsVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * User Search Feign接口类
 *
 * <AUTHOR>
 */
@FeignClient(
	value = AppConstant.APPLICATION_SYSTEM_NAME
)
public interface IUserSearchClient {

	String API_PREFIX = "/client";
	String LIST_BY_USER = API_PREFIX + "/user/list-by-user";
	String LIST_BY_DEPT = API_PREFIX + "/user/list-by-dept";
	String LIST_BY_POST = API_PREFIX + "/user/list-by-post";
	String LIST_BY_ROLE = API_PREFIX + "/user/list-by-role";
	String LIST_BY_REALNAME = API_PREFIX + "/user/list-by-realame";
	String ROLEID_BY_ROLENAME = API_PREFIX + "/user/roleid-by-rolename";
	String LIST_BY_USERID_LIST = API_PREFIX + "/user/list-by-ids";
	String LIST_ALL_BY_USERID_LIST = API_PREFIX + "/user/list-all-by-ids";
	String LIST_TENANT_USER_BY_DEPT = API_PREFIX + "/user/list-by-dept-tenant";
	String API_PORTABLE_PREFIX = API_PREFIX + "/portable/user";
	String API_PORTABLE_REGISTER_USER_INFO = API_PORTABLE_PREFIX + "/registerUserInfo";
	String API_PORTABLE_REGISTER_USER_EXCEL_INFO = API_PORTABLE_PREFIX + "/registerUserExcelInfo";
	String API_ACCUMULATED_REGISTRATION_DATA = API_PORTABLE_PREFIX + "/accumulatedRegistrationData";
	String LIST_BY_ACCOUNT_LIST = API_PREFIX + "/user/list-by-account";

	String LIST_BY_USERID_MAPPING = API_PREFIX + "/user/mapping/info";

	String LIST_BY_USERID_MAPPING_SOURCE = API_PREFIX + "/user/mapping/source/info";

	String LIST_BY_USER_BY_PHONE = API_PREFIX + "/user/phone/info";

	String API_PORTABLE_USER_AND_REGIN_INFO = API_PREFIX + "/portable/userAndReginInfo";

	String API_PORTABLE_USER_REGION_INFO = API_PREFIX + "/portable/userAppInfoList";

	String LIST_BY_ROLE_NAME = API_PREFIX + "/user/list-by-role-name";

	String LIST_BY_ACCOUNT_MATCH = API_PREFIX + "/user/list-by-account-match";
	/**
	 * 根据用户ID查询用户列表
	 *
	 * @param userId 用户ID
	 * @return 用户列表
	 */
	@GetMapping(LIST_BY_USER)
	R<List<User>> listByUser(@RequestParam("userId") String userId);

	/**
	 * 根据部门ID查询用户列表
	 *
	 * @param deptId 部门ID
	 * @return 用户列表
	 */
	@GetMapping(LIST_BY_DEPT)
	R<List<User>> listByDept(@RequestParam("deptId") String deptId);

	/**
	 * 根据部门ID和租户ID查询用户列表
	 *
	 * @param tenantId 租户ID
	 * @param deptId   部门ID
	 * @param plantId  站点id
	 * @return 用户列表
	 */
	@GetMapping(LIST_TENANT_USER_BY_DEPT)
	R<List<User>> listTenantUserByDept(@RequestParam("deptId") Long deptId, @RequestParam("plantId") Long plantId, @RequestParam("tenantId") String tenantId);

	/**
	 * 根据岗位ID查询用户列表
	 *
	 * @param postId 岗位ID
	 * @return 用户列表
	 */
	@GetMapping(LIST_BY_POST)
	R<List<User>> listByPost(@RequestParam("postId") String postId);

	/**
	 * 根据角色ID查询用户列表
	 *
	 * @param roleId 角色ID
	 * @return 用户列表
	 */
	@GetMapping(LIST_BY_ROLE)
	R<List<User>> listByRole(@RequestParam("roleId") String roleId);

	/**
	 * 根据角色真实姓名模糊查询用户列表
	 *
	 * @param realName 角色ID
	 * @return 用户列表
	 */
	@GetMapping(LIST_BY_REALNAME)
	R<List<User>> listByRealName(@RequestParam("realName") String realName);

	/**
	 * 根据角色名称查询角色id
	 */
	@GetMapping(ROLEID_BY_ROLENAME)
	R<Long> roleIdByRoleName(@RequestParam("roleName") String roleName);

	/**
	 * 根据userIds查询用户信息
	 *
	 * @param userIds 入参
	 * @return R<List < User>>
	 * <AUTHOR>
	 * @since 2023/11/28 11:11
	 **/
	@PostMapping(LIST_BY_USERID_LIST)
	R<List<User>> listByUserIds(@RequestBody List<Long> userIds);

	/**
	 * 根据userIds查询所有用户信息（包含删除的用户）
	 *
	 * @param userIds 入参
	 * @return R<List < User>>
	 * <AUTHOR>
	 * @since 2023/11/28 11:11
	 **/
	@PostMapping(LIST_ALL_BY_USERID_LIST)
	R<List<User>> listAllByUserIds(@RequestBody List<Long> userIds);

	/**
	 * 查询便携式app注册用户信息
	 *
	 * @param map 入参
	 * @return R<List < UserStatisticsVO>>
	 * <AUTHOR>
	 * @since 2024/1/27 9:44
	 **/
	@PostMapping(API_PORTABLE_REGISTER_USER_INFO)
	R<List<UserStatisticsVO>> singleDayRegisterInfo(@RequestBody Map<String, Object> map);

	/**
	 * 查询便携式app注册累计用户数
	 *
	 * @param map 入参
	 * @return R<List < UserStatisticsVO>>
	 * <AUTHOR>
	 * @since 2024/1/27 9:44
	 **/
	@PostMapping(API_ACCUMULATED_REGISTRATION_DATA)
	R<Long> accumulatedRegistrationData(@RequestBody Map<String, Object> map);

	/**
	 * 查询便携式app注册用户信息详情
	 *
	 * @param map 入参
	 * @return R<List < UserStatisticsVO>>
	 * <AUTHOR>
	 * @since 2024/1/27 9:44
	 **/
	@PostMapping(API_PORTABLE_REGISTER_USER_EXCEL_INFO)
	R<List<UserRegistrationVO>> singleDayRegisterExcelInfo(@RequestBody Map<String, Object> map);

	/**
	 * 根据account查询用户信息
	 *
	 * @param accounts 入参
	 * @return R<List < User>>
	 * <AUTHOR>
	 * @since 2023/03/06 11:11
	 **/
	@PostMapping(LIST_BY_ACCOUNT_LIST)
	R<List<User>> listByAccount(@RequestBody List<String> accounts);


	@PostMapping(LIST_BY_USERID_MAPPING)
	R<List<User>> listByMappingUser(@RequestBody List<Long> userIds);

	@PostMapping(LIST_BY_USERID_MAPPING_SOURCE)
	R<List<User>> listByMappingSourceUser(@RequestBody List<Long> userIds);

	@GetMapping(LIST_BY_USER_BY_PHONE)
	R<List<User>> listByPhone(@RequestParam("phone") String phone);

	/**
	 * 便携查询人员和区域信息
	 *
	 * @param userIds 入参
	 * @return R<List < JSONObject>>
	 * <AUTHOR>
	 * @since 2024/6/20 10:34
	 **/
	@PostMapping(API_PORTABLE_USER_AND_REGIN_INFO)
	R<Map<Long, JSONObject>> userAndReginInfo(@RequestBody List<Long> userIds);

	/**
	 * 便携-查询用户行政区域信息
	 *
	 * @param userIds 入参
	 * @return R<Map < Long, UserApp>>
	 * <AUTHOR>
	 * @since 2024/6/25 14:37
	 **/
	@PostMapping(API_PORTABLE_USER_REGION_INFO)
	R<Map<Long, UserApp>> userAppInfoList(@RequestBody List<Long> userIds);

	/**
	 * 根据角色名称查询用户信息
	 *
	 * @param roleName 入参
	 * @return R<List < User>>
	 * <AUTHOR>
	 * @since 2024/6/25 14:37
	 **/
	@GetMapping(LIST_BY_ROLE_NAME)
	R<List<User>> listByRoleName(@RequestParam("roleName") String roleName);

	/**
	 * 根据用户账号名称查询用户信息
	 *
	 * @param account 账号
	 * @return R<List < User>>
	 * <AUTHOR>
	 * @since 2024/6/25 14:37
	 **/
	@GetMapping(LIST_BY_ACCOUNT_MATCH)
	R<List<User>> listByAccountMatch(@RequestParam("account") String account);
}
