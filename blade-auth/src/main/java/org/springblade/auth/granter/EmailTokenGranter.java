package org.springblade.auth.granter;

import lombok.extern.slf4j.Slf4j;
import org.springblade.auth.constant.AuthConstant;
import org.springblade.auth.service.BladeUserDetails;
import org.springblade.auth.utils.TokenUtil;
import org.springblade.common.cache.CacheNames;
import org.springblade.common.cache.EmailCacheNamesEnum;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.utils.CommonUtil;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.support.Kv;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.core.tool.utils.WebUtil;
import org.springblade.resource.feign.ISmsClient;
import org.springblade.system.entity.User;
import org.springblade.system.entity.UserInfo;
import org.springblade.system.feign.IUserClient;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.oauth2.common.exceptions.UserDeniedAuthorizationException;
import org.springframework.security.oauth2.provider.*;
import org.springframework.security.oauth2.provider.token.AbstractTokenGranter;
import org.springframework.security.oauth2.provider.token.AuthorizationServerTokenServices;

import javax.servlet.http.HttpServletRequest;
import java.util.LinkedHashMap;
import java.util.Map;

@Slf4j
public class EmailTokenGranter extends AbstractTokenGranter {
	private static final String GRANT_TYPE = "email";

	private final AuthenticationManager authenticationManager;

	private final BladeRedis bladeRedis;
	private final IUserClient userClient;
	private final ISmsClient smsClient;

	protected EmailTokenGranter(AuthenticationManager authenticationManager, AuthorizationServerTokenServices tokenServices,
								ClientDetailsService clientDetailsService, OAuth2RequestFactory requestFactory, BladeRedis bladeRedis, IUserClient userClient, ISmsClient smsClient) {
		super(tokenServices, clientDetailsService, requestFactory, GRANT_TYPE);
		this.bladeRedis = bladeRedis;
		this.authenticationManager = authenticationManager;
		this.userClient = userClient;
		this.smsClient = smsClient;
	}

	@Override
	protected OAuth2Authentication getOAuth2Authentication(ClientDetails client, TokenRequest tokenRequest) {
		HttpServletRequest request = WebUtil.getRequest();
		String loginName = request.getParameter("login_name");

		// 增加验证码判断【仅在web登陆时，才会有验证码，此时需要校验验证码是否正确】
		String key = request.getHeader(TokenUtil.CAPTCHA_HEADER_KEY);
		String code = request.getHeader(TokenUtil.CAPTCHA_HEADER_CODE);
		if (StringUtil.isNotBlank(key) || StringUtil.isNotBlank(code)) {
			// 获取验证码
			String redisCode = bladeRedis.get(CacheNames.CAPTCHA_KEY + key);
			// 判断验证码
			if (code == null || !StringUtil.equalsIgnoreCase(redisCode, code)) {
				throw new UserDeniedAuthorizationException(TokenUtil.CAPTCHA_NOT_CORRECT);
			} else {
				bladeRedis.del(CacheNames.CAPTCHA_KEY + key);
			}
		}


		String emailVerifyCode = bladeRedis.get(CacheNames.CAPTCHA_KEY + EmailCacheNamesEnum.EMAIL_LOGIN_CAPTCHA_SUFFIX.getCacheNameSuffix() + loginName);
		String verificationCode = request.getParameter("verificationCode");
		if (verificationCode == null || !StringUtil.equalsIgnoreCase(emailVerifyCode, verificationCode)) {
			throw new UserDeniedAuthorizationException(TokenUtil.CAPTCHA_NOT_CORRECT);
		}

		Map<String, String> parameters = new LinkedHashMap<String, String>(tokenRequest.getRequestParameters());
		String tenantId = parameters.get("tenant_id");
		String loginType = request.getParameter("login_type");

		R<UserInfo> result = userClient.userByLoginName(tenantId, loginName, loginType, "");
		BladeUserDetails bladeUserDetails;
		if (result.getCode() == 200) {
			User user = result.getData().getUser();
			Kv detail = result.getData().getDetail();
			if (user == null || user.getId() == null || user.getAccount() == null) {
				throw new UserDeniedAuthorizationException(TokenUtil.TOKEN_NOT_PERMISSION);
			}
			bladeUserDetails = new BladeUserDetails(user.getId(),
				user.getTenantId(), result.getData().getOauthId(), user.getName(), user.getRealName(), user.getDeptId(), user.getPostId(), user.getRoleId(), Func.join(result.getData().getRoles()),
				Func.toStr(user.getAvatar(), TokenUtil.DEFAULT_AVATAR),
				user.getAccount(), AuthConstant.ENCRYPT + user.getPassword(), detail, true, true, true, true,
				AuthorityUtils.commaSeparatedStringToAuthorityList(Func.join(result.getData().getRoles())));
			//保存当前用户的当前语言
			bladeRedis.set("JPush:"+user.getId(),CommonUtil.getCurrentLanguage());
		} else {
			throw new UserDeniedAuthorizationException(String.valueOf(result.getCode()));
		}
		// 组装认证数据，关闭密码校验
		Authentication userAuth = new UsernamePasswordAuthenticationToken(bladeUserDetails, null, bladeUserDetails.getAuthorities());
		((AbstractAuthenticationToken) userAuth).setDetails(parameters);
		OAuth2Request storedOAuth2Request = getRequestFactory().createOAuth2Request(client, tokenRequest);
		bladeRedis.del(CacheNames.CAPTCHA_KEY + EmailCacheNamesEnum.EMAIL_LOGIN_CAPTCHA_SUFFIX.getCacheNameSuffix() + loginName);
		// 重新登录后清除修改记录
		bladeRedis.del(CommonConstant.BLADE_USER_CHANGE_PRE + bladeUserDetails.getUserId());
		// 返回 OAuth2Authentication
		return new OAuth2Authentication(storedOAuth2Request, userAuth);
	}
}
