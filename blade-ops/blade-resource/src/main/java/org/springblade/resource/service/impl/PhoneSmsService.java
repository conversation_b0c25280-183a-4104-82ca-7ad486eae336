package org.springblade.resource.service.impl;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.I18nMsgCode;
import org.springblade.common.constant.SmsConstant;
import org.springblade.common.constant.SmsEnum;
import org.springblade.common.utils.AESUtils;
import org.springblade.common.utils.CommonUtil;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.sms.model.SmsData;
import org.springblade.core.sms.model.SmsResponse;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.core.tool.utils.StringPool;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.resource.builder.sms.SmsBuilder;
import org.springblade.resource.service.IPhoneSmsService;
import org.springblade.resource.vo.SmsVO;
import org.springblade.system.entity.User;
import org.springblade.system.feign.IUserClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.Base64;
import java.util.Map;
import java.util.UUID;

import static org.springblade.resource.utils.SmsUtil.PARAM_KEY;
import static org.springblade.resource.utils.SmsUtil.getValidateParams;

/**
 * <AUTHOR>
 */
@Service
//@AllArgsConstructor
@Slf4j
public class PhoneSmsService implements IPhoneSmsService {
	@Resource
	private  SmsBuilder smsBuilder;
	@Resource
	private  BladeRedis bladeRedis;
	@Resource
	private  IUserClient userClient;
	@Value("${blade.aes.key}")
	private String aesKey;

	@Override
	public R getPhoneVerificationCode(SmsVO smsVO) {
		String currentLanguage = CommonUtil.getCurrentLanguage();
		R r = new R();

		if (StringUtil.isBlank(smsVO.getSignValue()) && StringUtil.isBlank(smsVO.getUnSignValue())){
			throw new BusinessException("resource.the.same.phone.number.exceeds.frequency.limit");
		}else {
			String signValue = smsVO.getSignValue();
			Object object = bladeRedis.get(CommonConstant.RESOURCE_ENCRYPTED_KEY+signValue);
			if (object != null){
				bladeRedis.del(CommonConstant.RESOURCE_ENCRYPTED_KEY+signValue);
				// 如果传过来的
				if (!signValue.equals(object)){
					throw new BusinessException("resource.the.same.phone.number.exceeds.frequency.limit");
				}
			}else {
				throw new BusinessException("resource.the.same.phone.number.exceeds.frequency.limit");
			}
		}


		if (StringUtil.isBlank(smsVO.getPhone()) || StringUtil.isBlank(smsVO.getPhoneDiallingCode())) {
			r.setCode(I18nMsgCode.SKYWORTH_RESOURCE_SMS_100103.getCode());
			r.setMsg(I18nMsgCode.SKYWORTH_RESOURCE_SMS_100103.autoGetMessage(currentLanguage));
			return r;
		}
		String getTemplateLanguage = CommonUtil.getCurrentLanguage();
		String code = SmsEnum.getSmsTemplate(smsVO.getSmsType(), getTemplateLanguage);

		if ("".equals(code)) {
			r.setCode(I18nMsgCode.SKYWORTH_RESOURCE_SMS_100102.getCode());
			r.setMsg(I18nMsgCode.SKYWORTH_RESOURCE_SMS_100102.autoGetMessage(currentLanguage));
			return r;
		}
		String phone = "";
		String trimmed = smsVO.getPhoneDiallingCode().trim();
		if (!trimmed.contains("+")) {
			smsVO.setPhoneDiallingCode("+" + trimmed);
		}
		phone =  smsVO.getPhoneDiallingCode() + smsVO.getPhone();
		//	 忘记密码、删除、登陆时  验证用户是否存在，不存在则提示用户注册账户
		User queryUser = new User();
		// 兼容设备端已经下载的旧app没有传入租户id
		if(StringUtil.isBlank(smsVO.getTenantId())) {
			queryUser.setTenantId(CommonConstant.CLIENT_TENANT_ID);
		} else {
			queryUser.setTenantId(smsVO.getTenantId());
		}
		// 获取手机验证码只需要校验手机号
		queryUser.setPhone(smsVO.getPhone());
		queryUser.setPhoneDiallingCode(smsVO.getPhoneDiallingCode());
		R<User> userR = userClient.userByUserInfo(queryUser);
		if (SmsConstant.SMS_TYPE_LOGGING.equals(smsVO.getSmsType())
			|| SmsConstant.SMS_TYPE_RETRIEVING_PASSWORD.equals(smsVO.getSmsType())
			|| SmsConstant.SMS_TYPE_DELETION.equals(smsVO.getSmsType())) {
			if (userR.getCode() != 200) {
				throw new BusinessException("resource.please.register.account.first");
			}
		}else if (SmsConstant.SMS_TYPE_REGISTERING.equals(smsVO.getSmsType())) {
			if (userR.getCode() == 200) {
				throw new BusinessException("resource.user.phone.is.exits");
			}
		} else if (SmsConstant.SMS_TYPE_UPDATE_PHONE.equals(smsVO.getSmsType())) {
			User userDb = userR.getData();
			if(ObjectUtil.isNotEmpty(userDb) && !AuthUtil.getUserId().equals(userDb.getId())) {
				throw new BusinessException("resource.user.phone.is.exits");
			}
		}

		Map<String, String> params = getValidateParams();
		SmsData smsData = new SmsData(params).setKey(PARAM_KEY);
		SmsResponse smsResponse = smsBuilder.template(code).sendSingleOverride(smsData, phone);
		log.info("send sms smsResponse : {}", smsResponse);
		if (smsResponse.getCode() == 0) {
			String value = smsData.getParams().get(smsData.getKey());
			bladeRedis.setEx(SmsConstant.CAPTCHA_KEY + code + StringPool.COLON + phone, value, Duration.ofMinutes(15));
			return R.data("操作成功");
		}
		if (smsResponse.getCode() == 2) {
			// 兼容旧app
			if(StringUtil.isBlank(smsVO.getTenantId()) || CommonConstant.CLIENT_TENANT_ID.equals(smsVO.getTenantId())) {
				r.setCode(I18nMsgCode.SKYWORTH_RESOURCE_SMS_100108.getCode());
				r.setMsg(I18nMsgCode.SKYWORTH_RESOURCE_SMS_100108.autoGetMessage(currentLanguage));
			} else {
				throw new BusinessException("resource.the.phone.is.wrong");
			}
		} else if (smsResponse.getCode() == 56) {
			if(StringUtil.isBlank(smsVO.getTenantId()) || CommonConstant.CLIENT_TENANT_ID.equals(smsVO.getTenantId())) {
				r.setCode(I18nMsgCode.SKYWORTH_RESOURCE_SMS_100112.getCode());
				r.setMsg(I18nMsgCode.SKYWORTH_RESOURCE_SMS_100112.autoGetMessage(currentLanguage));
			}else {
				throw new BusinessException("resource.phone.number.format.is.incorrect");
			}
		}  else if (smsResponse.getCode() == 53) {
//			r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100137.getCode());
//			r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100137.getMessage());
			throw new BusinessException("resource.the.same.phone.number.exceeds.frequency.limit");
		}  else if (smsResponse.getCode() == 5) {
			throw new BusinessException("resource.the.sms.template.is.not.exist");
		}else if(smsResponse.getCode() == 23){
			throw new BusinessException("resource.sms.phone.not.in.area");
		} else {
			r.setCode(I18nMsgCode.SKYWORTH_RESOURCE_SMS_100109.getCode());
			r.setMsg(smsResponse.getMsg());
			log.info("send sms error : {} ", smsResponse.getMsg());
		}
		return r;
	}

	@Override
	public R getEncryptedValue(JSONObject jsonObject) {
		// 前端传
		String unEncrypt = String.valueOf(UUID.randomUUID());
		String encryptedValue = null;
		try {
			// aes对称加密
			encryptedValue = AESUtils.encrypt(unEncrypt, aesKey);
		} catch (Exception e) {
			log.error(e.getMessage());
		}
		//放到响应体内，给前端进行加密
		String encodeToString = Base64.getEncoder().encodeToString(unEncrypt.getBytes());

		bladeRedis.setEx(CommonConstant.RESOURCE_ENCRYPTED_KEY + encryptedValue, encryptedValue, Duration.ofMinutes(10));
		return R.data(encodeToString);
	}
}
