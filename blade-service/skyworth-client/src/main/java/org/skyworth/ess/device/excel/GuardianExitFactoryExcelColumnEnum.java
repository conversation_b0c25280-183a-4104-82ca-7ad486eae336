package org.skyworth.ess.device.excel;

import lombok.Getter;
import org.springblade.common.constant.CommonConstant;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Getter
public enum GuardianExitFactoryExcelColumnEnum {

	securityGuardSerialNumber("securityGuard Serial Number","安全卫士SN"),
	deviceTypeG<PERSON>ian("device Type","设备型号"),
	simCardNumb("card Number","卡号"),
	companyGuardian("company","厂家"),
	qualityGuaranteeYearGuardian("quality Guarantee Year","质保年限"),
	exitFactoryDateGuardian("exit Factory Date","出厂日期"),
	;

    private String columnEn;
    private String columnCn;
    GuardianExitFactoryExcelColumnEnum(String columnEn, String columnCn) {
        this.columnEn = columnEn;
        this.columnCn = columnCn;
    }

    public static Set<String> getColumn(String language) {
        Set<String> result = new HashSet<>();
        for(GuardianExitFactoryExcelColumnEnum item : GuardianExitFactoryExcelColumnEnum.values()) {
            if(CommonConstant.CURRENT_LANGUAGE_ZH.equalsIgnoreCase(language)) {
                result.add(item.columnCn);
            } else {
                result.add(item.columnEn);
            }
        }
        return result;
    }
}
