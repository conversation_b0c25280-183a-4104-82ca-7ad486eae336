package org.skyworth.ess.app.service.impl;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.app.service.IWeatherService;
import org.skyworth.ess.app.vo.*;
import org.skyworth.ess.config.WeatherConfig;
import org.skyworth.ess.constant.WeatherResultCodeEnum;
import org.skyworth.ess.device.entity.DeviceCustomModeEntity;
import org.skyworth.ess.plant.entity.PlantEntity;
import org.skyworth.ess.plant.service.IPlantService;
import org.springblade.common.constant.DictBizCodeEnum;
import org.springblade.common.utils.CollectionUtils;
import org.springblade.common.utils.tool.StringUtils;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.tool.api.R;
import org.springblade.system.constant.DictConstant;
import org.springblade.system.entity.DictBiz;
import org.springblade.system.entity.Region;
import org.springblade.system.feign.IDictBizClient;
import org.springblade.system.feign.ISysClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class WeatherServiceImpl implements IWeatherService {


	@Override
	public WeatherResultVO getWeatherDetail(String plantId) {
		WeatherResultVO result = new WeatherResultVO();
		PlantEntity plantEntity = plantService.getById(Long.parseLong(plantId));
		if(plantEntity == null) {
			log.info("plantEntity is null");
			return result;
		}
		Map<String,String> regionNameMap = this.getRegionList(plantEntity);
		if(regionNameMap.isEmpty()) {
			throw new BusinessException("client.weather.plant.name.is.empty");
		}
		String key = weatherConfig.getKey();
		// 查询地理位置id
		String geoLocationId = this.getGeoLocationId(regionNameMap,key);
		if(StringUtils.isEmpty(geoLocationId)) {
			throw new BusinessException("client.weather.geo.id.empty");
		}
		WeatherResultVO weatherNowDetail = getWeatherNowDetail(key, geoLocationId);
		return this.getWeatherDetail(key, geoLocationId,weatherNowDetail);
	}

	@Override
	public void calHybridWorkMode(DeviceCustomModeEntity deviceCustomModeEntity) {
		if(!DictConstant.INVERTER_MODE_AI.equals(deviceCustomModeEntity.getHybridWorkMode())) {
			// 非智能模式，选择了什么，则直接下发什么给设备
			deviceCustomModeEntity.setSelectHybridWorkMode(deviceCustomModeEntity.getHybridWorkMode());
			log.info("issueSetupCommon calHybridWorkMode is not ai : {} ",deviceCustomModeEntity.getDeviceSerialNumber());
			return;
		}
		// 如果是智能模式，则根据天气计算使用哪种模式
		WeatherResultVO weatherResultVO = this.getWeatherDetail(String.valueOf(deviceCustomModeEntity.getPlantId()));
		// 获取晴天编码数据字典
		R<List<DictBiz>> dictResult = dictBizClient.getListAllLang(DictBizCodeEnum.CLIENT_GUARDIAN_WEATHER_SUNNY.getDictCode());
		if(!dictResult.isSuccess()) {
			log.info("issueSetupCommon calHybridWorkMode get dict client_guardian_weather_sunny is fail ");
			return;
		}
		if(CollectionUtils.isNullOrEmpty(dictResult.getData())) {
			log.info("issueSetupCommon calHybridWorkMode get dict client_guardian_weather_sunny is null ");
			return;
		}
		Set<String> weatherCodeDictSet = dictResult.getData().stream().map(DictBiz::getDictKey).collect(Collectors.toSet());
		// 如果天气为晴天，则使用绿色模式，否则使用 风暴 模式
		if(weatherResultVO.getNow() != null &&  weatherCodeDictSet.contains(weatherResultVO.getNow().getIcon())) {
			log.info("issueSetupCommon calHybridWorkMode is ai to green : {} ",deviceCustomModeEntity.getDeviceSerialNumber());
			// 模式设置表 记录原始设置的模式
			deviceCustomModeEntity.setSelectHybridWorkMode(DictConstant.INVERTER_MODE_AI);
			// 设备表使用 和 设备一致的模式
			deviceCustomModeEntity.setHybridWorkMode(DictConstant.INVERTER_MODE_GREEN);
		}else {
			log.info("issueSetupCommon calHybridWorkMode is ai to storm : {} ",deviceCustomModeEntity.getDeviceSerialNumber());
			deviceCustomModeEntity.setSelectHybridWorkMode(DictConstant.INVERTER_MODE_AI);
			deviceCustomModeEntity.setHybridWorkMode(DictConstant.INVERTER_MODE_STORM);
		}
	}
	private WeatherResultVO getWeatherNowDetail(String key, String geoLocationId) {
		Map<String, Object> queries = new HashMap<>();
		queries.put(WEATHER_KEY, key);
		queries.put(WEATHER_PARAMS_LOCATION, geoLocationId);
		String resultStr = (String) bladeRedis.get(WEATHER_NOW_DETAIL_REDIS_KEY + ":" + geoLocationId);
		if(resultStr == null) {
			resultStr = HttpUtil.get(weatherConfig.getWeatherNowUrl(), queries);
			// 天气详情设置1小时过期
			bladeRedis.setEx(WEATHER_NOW_DETAIL_REDIS_KEY + ":" + geoLocationId, resultStr, 60L * 60);
			// 天气详情设置1小时过期
//			bladeRedis.expire(WEATHER_NOW_DETAIL_REDIS_KEY + ":" + geoLocationId, 60 * 60);
		}
		WeatherResultVO weatherResultVO = JSON.parseObject(resultStr, new TypeReference<WeatherResultVO>() {
		});
		log.info("weather getWeatherNowDetail:"+resultStr);
		if (WeatherResultCodeEnum.SUCCESS.getCode().equals(weatherResultVO.getCode())) {
			return weatherResultVO;
		}
		log.info("weather getWeatherNowDetail error:" + weatherResultVO.getCode());
		throw new BusinessException("client.weather.detail.error",weatherResultVO.getCode());
	}
	private WeatherResultVO getWeatherDetail(String key, String geoLocationId,WeatherResultVO weatherNowDetail) {
		Map<String, Object> queries = new HashMap<>();
		queries.put(WEATHER_KEY, key);
		queries.put(WEATHER_PARAMS_LOCATION, geoLocationId);
		String resultStr = (String) bladeRedis.get(WEATHER_DETAIL_REDIS_KEY + ":" + geoLocationId);
		WeatherResultVO weatherResultVO ;
		if(resultStr == null) {
			resultStr = HttpUtil.get(weatherConfig.getWeatherUrl(), queries);
			weatherResultVO = JSON.parseObject(resultStr, new TypeReference<WeatherResultVO>() {
			});
			WeatherNowDetailVO now = weatherNowDetail.getNow();
			if(Objects.nonNull(now)) {
				weatherResultVO.setNow(now);
			}
			resultStr = JSON.toJSONString(weatherResultVO);
			// 天气详情设置1小时过期
			bladeRedis.setEx(WEATHER_DETAIL_REDIS_KEY + ":" + geoLocationId, resultStr,60L * 60L);
			// 天气详情设置1小时过期
//			bladeRedis.expire(WEATHER_DETAIL_REDIS_KEY + ":" + geoLocationId, 60 * 60);
		}
		weatherResultVO = JSON.parseObject(resultStr, new TypeReference<WeatherResultVO>() {
			});
		weatherResultVO.setNow(weatherNowDetail.getNow());
		log.info("weather getWeather:"+resultStr);
		if (WeatherResultCodeEnum.SUCCESS.getCode().equals(weatherResultVO.getCode())) {
			return weatherResultVO;
		}
		log.info("weather getWeatherDetail error:" + weatherResultVO.getCode());
		throw new BusinessException("client.weather.detail.error",weatherResultVO.getCode());
	}

	private String getGeoLocationId(Map<String,String> regionNameMap, String key) {
		String countyName = regionNameMap.get(COUNTY_KEY);
		String cityName = regionNameMap.get(CITY_KEY);
		String provinceName = regionNameMap.get(PROVINCE_KEY);
		Map<String, Object> queries = new HashMap<>();
		queries.put(WEATHER_KEY,key);
		StringBuilder addressName = new StringBuilder();
		if(StringUtils.isNotEmpty(countyName)) {
			queries.put(WEATHER_PARAMS_LOCATION,countyName);
			queries.put(WEATHER_PARAMS_ADM,cityName);
			addressName.append(cityName).append(":").append(countyName);
		} else {
			queries.put(WEATHER_PARAMS_LOCATION,cityName);
			queries.put(WEATHER_PARAMS_ADM,provinceName);
			addressName.append(provinceName).append(":").append(cityName);
		}
		String addressId = (String)bladeRedis.get(WEATHER_ADDRESS_REDIS_KEY + ":" + addressName.toString());
		log.info("weather getGeoLocationId addressName: {} , addressId: {} ",  addressName, addressId);
		if(StringUtils.isEmpty(addressId)) {
			addressId = HttpUtil.get(weatherConfig.getGeoUrl(), queries);
			// 地理位置设置7天过期
			bladeRedis.setEx(WEATHER_ADDRESS_REDIS_KEY + ":" + addressName.toString(),addressId,60L * 60 * 24 * 7);
			// 地理位置设置7天过期
//			bladeRedis.expire(WEATHER_ADDRESS_REDIS_KEY, 60 * 60 * 24 * 7);
		}
		log.info("weather getGeoLocationId:" + addressId);
		WeatherGeoResultVO weatherGeoResultVO = JSON.parseObject(addressId, new TypeReference<WeatherGeoResultVO>() {
		});
		if (WeatherResultCodeEnum.SUCCESS.getCode().equals(weatherGeoResultVO.getCode())) {
			// 不为空，则取第一个最相似的
			if (!CollectionUtils.isNullOrEmpty(weatherGeoResultVO.getLocation())) {
				WeatherGeoVO weatherGeoVO = weatherGeoResultVO.getLocation().get(0);
				return weatherGeoVO.getId();
			}
		}
		log.info("weather getGeoLocationId error:" + weatherGeoResultVO.getCode());
		throw new BusinessException("client.weather.geo.error", weatherGeoResultVO.getCode());
	}
	private Map<String,String> getRegionList(PlantEntity plantEntity) {
		List<String> regionCodeList = new ArrayList<>();
		this.setRegionCode(plantEntity.getProvinceCode(),regionCodeList);
		this.setRegionCode(plantEntity.getCityCode(),regionCodeList);
		this.setRegionCode(plantEntity.getCountyCode(),regionCodeList);
		Map<String,String> regionNameMap = new HashMap<>();
		if(!CollectionUtils.isNullOrEmpty(regionCodeList)) {
			R<List<Region>> regionResult = sysClient.getRegionList(regionCodeList);
			List<Region> regionList = regionResult.getData();
			for(Region region : regionList) {
				if(region.getCode().equals(plantEntity.getProvinceCode())) {
					regionNameMap.put(PROVINCE_KEY,region.getName());
				}
				if(region.getCode().equals(plantEntity.getCityCode())) {
					regionNameMap.put(CITY_KEY,region.getName());
				}
				if(region.getCode().equals(plantEntity.getCountyCode())) {
					regionNameMap.put(COUNTY_KEY,region.getName());
				}
			}
			return regionNameMap;
		}
		log.info("regionCodeList is null");
		return regionNameMap;
	}

	private void setRegionCode(String regionCode,List<String> regionCodeList) {
		if(StringUtils.isNotEmpty(regionCode)) {
			regionCodeList.add(regionCode);
		}
	}

	@Resource
	private IPlantService plantService;
	@Resource
	private ISysClient sysClient;
	@Autowired
	WeatherConfig weatherConfig;
	@Resource
	private BladeRedis bladeRedis;
	@Resource
	private IDictBizClient dictBizClient;
	// 天气代码: 发绿电优先模式
	private final Set<String> weatherCodeSet = Set.of("100","101","102","103","300","305","350","399","400","408");
	private static final String COUNTY_KEY = "county";
	private static final String CITY_KEY = "city";
	private static final String PROVINCE_KEY = "province";
	private static final String WEATHER_KEY = "key";
	private static final String WEATHER_PARAMS_LOCATION = "location";
	private static final String WEATHER_PARAMS_ADM = "adm";
	private static final String WEATHER_ADDRESS_REDIS_KEY = "guardian:weather:address:redis:key";
	// 未来3天天气
	private static final String WEATHER_DETAIL_REDIS_KEY = "guardian:weather:detail:redis:key";
	// 实时天气
	private static final String WEATHER_NOW_DETAIL_REDIS_KEY = "guardian:weather:now:detail:redis:key";

	public static void main(String[] args) {
		Map<String, Object> queries = new HashMap<>();
		queries.put("location","南山区");
		queries.put("adm","深圳市");
		queries.put("key","12d904fbd0034cc3922603bbfbc8e388");
		String uuuu = "https://geoapi.qweather.com/v2/city/lookup?key=12d904fbd0034cc3922603bbfbc8e388&location=南山区&adm=深圳市";
		String test = "https://geoapi.qweather.com/v2/city/lookup";
		RestTemplate restTemplate = new RestTemplate();
		String forEntity = restTemplate.getForObject(uuuu, String.class);
		System.out.println(forEntity);
//		String s = HttpUtil.get(uuuu, queries);
//		WeatherGeoResultVO weatherGeoResultVO = JSON.parseObject(s, new TypeReference<WeatherGeoResultVO>() {
//		});
//		System.out.println(s);
	}
}
