
package org.skyworth.ess.app;
import org.skyworth.ess.util.LazzenDataCrcUtil;
import org.springblade.common.utils.BinaryToHexUtils;

import java.io.*;
import java.util.*;
import java.util.regex.*;
public class Main {

		public static void main(String[] args) {
			// 校验和 字符串
			String sdf = "fd01004A98D863B915A400000003010600010001654235454C000000000000005000500001019000000A300E1E420000003D00000000000000000000000000000000240202142114";
			System.out.println(LazzenDataCrcUtil.calculateSum(sdf));
			String dataValue = "654235454C000000000000005000500001019000000A300E1E420000003D00000000000000000000000000000000240202142114";
			System.out.println(dataValue.substring(0,2));
			String substring = dataValue.substring(42);
			String startAddress = substring.substring(0, 4);
			String addressNumber = substring.substring(4, 6);
			String data = substring.substring(6);
			System.out.println(startAddress);
			System.out.println(addressNumber);
			System.out.println(data);
			int decimalValue = BinaryToHexUtils.hexToDecimal("0001");

			System.out.println(BinaryToHexUtils.hexAddLengthToHex(startAddress,16));
//			String folderPath = "E:\\test\\device\\device"; // 替换为你的文件夹路径
//			File folder = new File(folderPath);
//
//			if (!folder.exists() || !folder.isDirectory()) {
//				System.out.println("指定的路径不是一个有效的文件夹");
//				return;
//			}
//
//			File[] vueFiles = folder.listFiles((dir, name) -> name.toLowerCase().endsWith(".vue"));
//
//			if (vueFiles == null || vueFiles.length == 0) {
//				System.out.println("文件夹中没有 .vue 文件");
//				return;
//			}
//
//			for (File file : vueFiles) {
//				try {
//					List<String> chineseTexts = extractChineseText(file);
//					printChineseTexts(chineseTexts);
//				} catch (IOException e) {
//					System.err.println("读取文件时发生错误: " + file.getAbsolutePath());
//					e.printStackTrace();
//				}
//			}
		}

		private static List<String> extractChineseText(File file) throws IOException {
			List<String> chineseTexts = new ArrayList<>();
			Pattern pattern = Pattern.compile("[\\u4e00-\\u9fa5]+");

			try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
				String line;
				while ((line = reader.readLine()) != null) {
					Matcher matcher = pattern.matcher(line);
					while (matcher.find()) {
						chineseTexts.add(matcher.group());
					}
				}
			}

			return chineseTexts;
		}

		private static void printChineseTexts(List<String> chineseTexts) {
			for (String text : chineseTexts) {
				System.out.println(text);
			}
		}
}
