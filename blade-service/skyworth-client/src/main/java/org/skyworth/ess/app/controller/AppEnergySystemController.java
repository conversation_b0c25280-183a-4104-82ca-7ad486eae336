package org.skyworth.ess.app.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.apache.logging.log4j.util.Strings;
import org.skyworth.ess.app.service.IAppEnergySystemService;
import org.skyworth.ess.balconypv.entity.EnergySystemEntity;
import org.skyworth.ess.balconypv.vo.EnergySystemVO;
import org.skyworth.ess.balconypv.wrapper.EnergySystemWrapper;
import org.skyworth.ess.plant.entity.PlantEntity;
import org.skyworth.ess.plant.service.IPlantService;
import org.skyworth.ess.util.SnowFlakeGeneratorUtil;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.utils.CommonUtil;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.system.cache.RegionCache;
import org.springblade.system.entity.Region;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 能源系统控制器
 *
 * @ClassName AppEnergySystemController
 * @Description 能源系统控制器
 * <AUTHOR>
 * @Date 2025/7/30 9:26
 */
@RestController
@RequestMapping("/app/energySystem")
@Api(value = "app能源系统接口", tags = "app能源系统接口")
@AllArgsConstructor
public class AppEnergySystemController extends BladeController {

	private final IAppEnergySystemService energySystemService;

	private final IPlantService plantService;

	/**
	 * 能源系统 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入energySystem")
	public R<EnergySystemVO> detail(EnergySystemEntity energySystem) {
		EnergySystemEntity detail = energySystemService.getOne(Condition.getQueryWrapper(energySystem));
		String language = CommonUtil.getCurrentLanguage();
		if (StringUtil.isNotBlank(detail.getCountryCode())) {
			detail.setCountryName(queryRegionName(detail.getCountryCode(), language));
		}
		if (StringUtil.isNotBlank(detail.getProvinceCode())) {
			detail.setProvinceName(queryRegionName(detail.getProvinceCode(), language));
		}
		if (StringUtil.isNotBlank(detail.getCityCode())) {
			detail.setCityName(queryRegionName(detail.getCityCode(), language));
		}
		return R.data(EnergySystemWrapper.build().entityVO(detail));
	}

	/**
	 * 查询区域名称
	 *
	 * @param code     区域编码
	 * @param language 语言
	 * @return 区域名称
	 */
	private String queryRegionName(String code, String language) {
		Region region = RegionCache.getByCode(code);
		if (region == null) {
			return Strings.EMPTY;
		}
		return CommonConstant.CURRENT_LANGUAGE_EN.equalsIgnoreCase(language) ? region.getNameEn() : region.getName();
	}

	/**
	 * 能源系统 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入energySystem")
	public R<IPage<EnergySystemVO>> page(EnergySystemVO energySystem, Query query) {
		IPage<EnergySystemVO> pages = energySystemService.page(Condition.getPage(query), energySystem);
		return R.data(pages);
	}

	/**
	 * 能源系统 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入energySystem")
	public R<Long> save(@Valid @RequestBody EnergySystemEntity energySystem) {
		Long snowFlakeId = SnowFlakeGeneratorUtil.getSnowFlakeId();
		energySystem.setId(snowFlakeId);
		energySystemService.save(energySystem);
		return R.data(snowFlakeId);
	}

	/**
	 * 能源系统 新增
	 */
	@PostMapping("/customizeSave")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "新增", notes = "传入energySystem")
	public R<EnergySystemVO> customizeSave(@Valid @RequestBody EnergySystemEntity energySystem) {
		return R.data(EnergySystemWrapper.build().entityVO(energySystemService.customizeSave(energySystem)));
	}

	/**
	 * 能源系统 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "修改", notes = "传入energySystem")
	public R update(@Valid @RequestBody EnergySystemEntity energySystem) {
		boolean result = energySystemService.updateById(energySystem);
		// 更新站点区域信息
		plantService.update(Wrappers.lambdaUpdate(PlantEntity.class).set(PlantEntity::getCountryCode,
			energySystem.getCountryCode()).set(PlantEntity::getProvinceCode, energySystem.getProvinceCode()).set(PlantEntity::getCityCode, energySystem.getCityCode()).set(PlantEntity::getCountyCode, energySystem.getCountyCode()).set(PlantEntity::getDetailAddress, energySystem.getDetailAddress()).eq(PlantEntity::getEnergySystemId, energySystem.getId()));
		return R.status(result);
	}

	@DeleteMapping("/delete")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "删除", notes = "批量删除能源系统")
	public R delete(@ApiParam(value = "主键集合", required = true) @RequestParam Long id) {
		if (id == null) {
			throw new BusinessException("client.parameter.error.empty");
		}
		return R.status(energySystemService.delete(id));
	}
}
