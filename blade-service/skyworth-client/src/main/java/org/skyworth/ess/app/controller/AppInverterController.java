package org.skyworth.ess.app.controller;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.app.service.IAppService;
import org.skyworth.ess.app.service.IAppSetupService;
import org.skyworth.ess.app.vo.AppSetRequestVO;
import org.skyworth.ess.app.vo.AppVO;
import org.skyworth.ess.app.vo.InverterModeVO;
import org.springblade.common.constant.EntityFieldConstant;
import org.springblade.common.utils.CommonUtil;
import org.springblade.core.log.annotation.ApiLog;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/app/inverter")
@Api(value = "app安全卫士接口", tags = "app安全卫士接口")
@Slf4j
public class AppInverterController {
	@Resource
	private IAppService appService;
	@Resource
	private IAppSetupService appSetupService;

	@PostMapping("")
	@ApiOperation(value = "查询智能能量变换器sn号", notes = "查询智能能量变换器sn号")
	@ApiLog("app查询智能能量变换器sn号")
	@Deprecated
	public R<AppVO> inverterByWifiStickSn(@RequestBody AppVO appVO) {
		return R.data(appService.inverterByWifiStickSn(appVO));
	}


	@PostMapping("/match")
	@ApiOperation(value = "扫描储能查询智能能量变换器和储能匹配", notes = "扫描储能查询智能能量变换器和储能匹配")
	@ApiLog("app扫描储能查询智能能量变换器和储能匹配")
	public R<AppVO> inverterMatch(@RequestBody AppVO appVO) {
		AppVO vo = new AppVO();
		vo.setMatchOrNot(appService.inverterMatch(appVO));
		return R.data(vo);
	}

	/**
	 * inverterModeSetup 分时设置查询
	 * @param appSetRequestVO
	 * @return
	 */
	@GetMapping("/mode")
	@ApiOperation(value = "查询当前模式", notes = "查询当前模式")
	@ApiLog("app查询当前模式")
	public R<JSONObject> queryInverterMode(@ApiIgnore @RequestParam Map<String, String> appSetRequestVO) {
		AppSetRequestVO requestVO = new AppSetRequestVO();
		requestVO.setPlantId(Long.parseLong(appSetRequestVO.get(EntityFieldConstant.PLANT_ID)));
		requestVO.setDeviceSerialNumber(appSetRequestVO.get(EntityFieldConstant.DEVICE_SERIAL_NUMBER));
		//默认查询智能能量变换器类型
		requestVO.setDeviceType(appSetRequestVO.get(EntityFieldConstant.DEVICE_TYPE) == null ? 0 : Integer.parseInt(appSetRequestVO.get(EntityFieldConstant.DEVICE_TYPE)));
		//2：分时设置
		requestVO.setSetCategory(2);
		String currentLanguage = CommonUtil.getCurrentLanguage();
		requestVO.setLanguage(currentLanguage);
		return R.data(appSetupService.getSetupItem(requestVO));
	}

	@PostMapping("/list")
	@ApiOperation(value = "根据站点获取不同公司模式", notes = "根据站点获取不同公司模式")
	@ApiLog("app根据站点获取不同公司模式")
	public R<List<InverterModeVO>> inverterList(@RequestBody AppVO appVO) {
		return R.data(appService.inverterList(appVO));
	}

	@PostMapping("/parallel/enable")
	@ApiOperation(value = "查询当前站点的智能能量变换器是否可以进行并机", notes = "查询当前站点的智能能量变换器是否可以进行并机")
	@ApiLog("查询当前站点的智能能量变换器是否可以进行并机")
	public R<JSONObject> inverterParallelEnable(@RequestBody AppVO appVO) {
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("inverterParallelEnable", appService.inverterParallelEnable(appVO));
		return R.data(jsonObject);
	}
}
