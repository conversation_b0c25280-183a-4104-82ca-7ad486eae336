package org.skyworth.ess.app.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.app.service.IWeatherService;
import org.skyworth.ess.app.vo.WeatherResultVO;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/app/weather")
@Api(value = "app天气接口", tags = "app天气接口")
@Slf4j
public class WeatherController extends BladeController {
	@Resource
	IWeatherService iWeatherService;
	@GetMapping("/detail")
	@ApiOperation(value = "获取天气详情", notes = "获取天气详情")
	public R<WeatherResultVO> getWeatherDetail(@ApiParam(value = "站点id", required = true) @RequestParam String plantId) {
		return R.data(iWeatherService.getWeatherDetail(plantId));
	}
}
