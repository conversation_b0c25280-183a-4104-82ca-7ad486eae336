package org.skyworth.ess.app.service.impl;

import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.app.vo.*;
import org.skyworth.ess.battery.entity.BatteryCurrentStatusEntity;
import org.skyworth.ess.battery.entity.BatteryEverydayTotalEntity;
import org.skyworth.ess.battery.entity.QueryCondition;
import org.skyworth.ess.battery.service.IBatteryCurrentStatusService;
import org.skyworth.ess.battery.service.IBatteryEverydayTotalService;
import org.skyworth.ess.battery.vo.BatteryEverydayTotalVO;
import org.skyworth.ess.dailyStatistics.entity.QueryDeviceLog22Condition;
import org.skyworth.ess.dailyStatistics.service.DeviceLog22ByDorisService;
import org.skyworth.ess.dailyStatistics.vo.DeviceLog22VO;
import org.skyworth.ess.device.entity.*;
import org.skyworth.ess.device.service.*;
import org.skyworth.ess.device.vo.DeviceEverydayTotalVO;
import org.springblade.common.constant.BizConstant;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.DictBizCodeEnum;
import org.springblade.common.constant.FunctionSetName;
import org.springblade.common.utils.CommonUtil;
import org.springblade.common.utils.DataUnitConversionUtil;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.system.constant.DictConstant;
import org.springblade.system.entity.DictBiz;
import org.springblade.system.feign.IDictBizClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class AppReportServiceImpl {
	@Resource
	IBatteryCurrentStatusService batteryCurrentStatusService;
	@Resource
	private IDeviceCurrentStatusService deviceCurrentStatusService;
	@Resource

	private DeviceLog22ByDorisService deviceLog22ByDorisService;
	@Resource
	private IBatteryEverydayTotalService batteryEverydayTotalService;
	@Resource
	private IDevice23Service device23Service;
	@Resource
	private IDictBizClient dictBizClient;
	@Resource
	private AppReportParallelServiceImpl appReportParallelServiceImpl;
	@Resource
	private IDeviceCustomModeService deviceCustomModeService;
	@Resource
	private IDeviceExitFactoryInfoService deviceExitFactoryInfoService;
	@Resource
	private BusinessServiceUtil businessServiceUtil;

	Function<BatteryEverydayTotalVO,BigDecimal> pvDailyEnergyOrParallelFun = BatteryEverydayTotalVO::getAppTodayEnergy;
	Function<BatteryEverydayTotalVO,BigDecimal> batteryDailyDischargeEnergyOrParallelFun = BatteryEverydayTotalVO::getAppBatteryDailyDischargeEnergy;
	Function<BatteryEverydayTotalVO,BigDecimal> loadAddOrParallelFun = BatteryEverydayTotalVO::getAppLoadAddEps;
	Function<BatteryEverydayTotalVO,BigDecimal> todayImportEnergyOrParallelFun = BatteryEverydayTotalVO::getAppTodayImportEnergy;
	Function<BatteryEverydayTotalVO,BigDecimal> todayExportEnergyOrParallelFun = BatteryEverydayTotalVO::getAppTodayExportEnergy;
	Function<BatteryEverydayTotalVO,BigDecimal> batteryDailyChargeEnergyOrParallelFun = BatteryEverydayTotalVO::getAppBatteryDailyChargeEnergy;
	// 电池2 充电量 220B
	Function<BatteryEverydayTotalVO,BigDecimal> batteryTodayChargeEnergy2Fun = BatteryEverydayTotalVO::getAppBatteryTodayChargeEnergy2;
	// 电池2 放电量  220F
	Function<BatteryEverydayTotalVO,BigDecimal> batteryTodayDischargeEnergy2Fun = BatteryEverydayTotalVO::getAppBatteryTodayDischargeEnergy2;
	public AppReportHeaderVO queryPlantRunningStateHeaderV2(AppVO appVO) {
		AppReportHeaderVO resultAppReportHeaderVO = new AppReportHeaderVO();
		try {
			// 并机查询并机数据
			if(Constants.ONE.equals(appVO.getIsParallelMode())) {
				appReportParallelServiceImpl.getParallelHeaderCardData(appVO,resultAppReportHeaderVO);
			} else {
				this.setHeadCardData(appVO, resultAppReportHeaderVO);
			}
		} catch (Exception e) {
			log.error("error ParseException :{} ", e);
		}
		return resultAppReportHeaderVO;
	}

	public AppReportHeaderVO queryPlantRunningStateV2(AppVO appVO) {
		AppReportHeaderVO resultAppReportHeaderVO = new AppReportHeaderVO();
		try {
			// 并机查询并机数据
			if (Constants.ONE.equals(appVO.getIsParallelMode())) {
				resultAppReportHeaderVO = appReportParallelServiceImpl.getParallelReportData(appVO);
			} else {
				resultAppReportHeaderVO = this.setReportHeaderData(appVO);
				// 查询日月年报表数据
				if (0 == appVO.getType()) {
					AppReportDetailVO hourReport = this.getHoursReportV2(appVO);
	//				List<AppReportDataVO> pv = hourReport.getPvGenerationList();
	//				List<AppReportDataVO> battery = hourReport.getBatteryOutputList();
	//				List<AppReportDataVO> power = hourReport.getPowerConsumptionList();
					// 将后一个时间段总量减去前一个总量，为当前时间段累计的
	//				this.subtractBeforeData(pv);
	//				this.subtractBeforeData(battery);
	//				this.subtractBeforeData(power);
					resultAppReportHeaderVO.setDailyReport(hourReport);
				} else if (1 == appVO.getType()) {
					// 周报表，过去7天
					SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
					Date parse = simpleDateFormat.parse(appVO.getDataScope());
					Instant instant = parse.toInstant();
					LocalDate endLocalDate = instant.atZone(ZoneId.systemDefault()).toLocalDate();
					LocalDate beginLocalDate = endLocalDate.plusDays(-7L);
					Function<QueryCondition, List<BatteryEverydayTotalVO>> dayFun = batteryEverydayTotalService::weekEstimateV2;
					// 查询数据库
					List<BatteryEverydayTotalVO> dailyReportList = ReportUtil.getBatteryEverydayTotalDbData(appVO,beginLocalDate,endLocalDate,dayFun);
					// 将数据库中的值转成 map的key,value形式，非并机的周月年取值字段一样，但是和并机不同，两边定义不同的函数取值
					AppReportDetailVO dailyReport = ReportUtil.setWeekMonthAnnualToKeyValue(appVO,dailyReportList,pvDailyEnergyOrParallelFun,batteryDailyDischargeEnergyOrParallelFun,loadAddOrParallelFun,todayImportEnergyOrParallelFun,
							todayExportEnergyOrParallelFun,batteryDailyChargeEnergyOrParallelFun);
					// 补全全部天数，集合为无序的
					ReportUtil.completionDay(dailyReport, beginLocalDate, endLocalDate, "MM-dd");
//					AppReportDetailVO dailyReport = this.getAppReportDetailVO(appVO, beginLocalDate, endLocalDate, simpleDateFormat, dayFun);
//					// 补全全部天数，集合为无序的
//					ReportUtil.completionDay(dailyReport, beginLocalDate, endLocalDate, "MM-dd");
					resultAppReportHeaderVO.setWeekReport(dailyReport);
				} else if (2 == appVO.getType()) {
					// 月报表
					SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM");
					Date dataScope = simpleDateFormat.parse(appVO.getDataScope());
					Instant instant = dataScope.toInstant();
					LocalDate month = instant.atZone(ZoneId.systemDefault()).toLocalDate();
					LocalDate beginLocalDate = month.with(TemporalAdjusters.firstDayOfMonth());
					LocalDate endLocalDate = month.with(TemporalAdjusters.lastDayOfMonth());
					Function<QueryCondition, List<BatteryEverydayTotalVO>> monthFun = batteryEverydayTotalService::monthEstimateV2;
					// 查询数据库
					List<BatteryEverydayTotalVO> monthReportList = ReportUtil.getBatteryEverydayTotalDbData(appVO,beginLocalDate,endLocalDate,monthFun);
					// 将数据库中的值转成 map的key,value形式，非并机的周月年取值字段一样，但是和并机不同，两边定义不同的函数取值
					AppReportDetailVO monthReport = ReportUtil.setWeekMonthAnnualToKeyValue(appVO,monthReportList,pvDailyEnergyOrParallelFun,batteryDailyDischargeEnergyOrParallelFun,loadAddOrParallelFun,todayImportEnergyOrParallelFun,
							todayExportEnergyOrParallelFun,batteryDailyChargeEnergyOrParallelFun);
					// 补齐数据库中不存在的 天值
					ReportUtil.completionDay(monthReport, beginLocalDate, endLocalDate, "dd");
//					AppReportDetailVO monthReport = this.getAppReportDetailVO(appVO, beginLocalDate, endLocalDate, simpleDateFormat, monthFun);
//					ReportUtil.completionDay(monthReport, beginLocalDate, endLocalDate, "dd");
					resultAppReportHeaderVO.setMonthlyReport(monthReport);
				} else if (3 == appVO.getType()) {
					// 年报表
					SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy");
					Date dataScope = simpleDateFormat.parse(appVO.getDataScope());
					Instant instant = dataScope.toInstant();
					LocalDate year = instant.atZone(ZoneId.systemDefault()).toLocalDate();
					LocalDate endLocalDate = year.with(TemporalAdjusters.lastDayOfYear());
					LocalDate beginLocalDate = year.with(TemporalAdjusters.firstDayOfYear());
					Function<QueryCondition, List<BatteryEverydayTotalVO>> annualFun = batteryEverydayTotalService::annualEstimateV2;
					// 查询数据库
					List<BatteryEverydayTotalVO> annualReportList = ReportUtil.getBatteryEverydayTotalDbData(appVO,beginLocalDate,endLocalDate,annualFun);
					// 将数据库中的值转成 map的key,value形式，非并机的周月年取值字段一样，但是和并机不同，两边定义不同的函数取值
					AppReportDetailVO annualReport = ReportUtil.setWeekMonthAnnualToKeyValue(appVO,annualReportList,pvDailyEnergyOrParallelFun,batteryDailyDischargeEnergyOrParallelFun,loadAddOrParallelFun,todayImportEnergyOrParallelFun,
							todayExportEnergyOrParallelFun,batteryDailyChargeEnergyOrParallelFun);
					// 补齐数据库中不存在的 月份值
					ReportUtil.completionMonth(annualReport);
//					AppReportDetailVO annualReport = this.getAppReportDetailVO(appVO, beginLocalDate, endLocalDate, simpleDateFormat, annualFun);
//					ReportUtil.completionMonth(annualReport);
					resultAppReportHeaderVO.setAnnualReport(annualReport);
				}
			}
		} catch (Exception e) {
			log.error("error ParseException :{} ", e);
		}

		return resultAppReportHeaderVO;
	}


	// 将数据库中值转化为 key value
	private AppReportDetailVO getAppReportDetailVO(AppVO appVO, LocalDate beginLocalDate, LocalDate endLocalDate, SimpleDateFormat simpleDateFormat,
												   Function<QueryCondition, List<BatteryEverydayTotalVO>> fun
	) {
		QueryCondition queryCondition = new QueryCondition();
		queryCondition.setStartDateTime(Date.from(beginLocalDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
		queryCondition.setEndDateTime(Date.from(endLocalDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
		queryCondition.setPlantId(appVO.getPlantId());
		queryCondition.setDeviceSerialNumber(appVO.getDeviceSerialNumber());
		List<BatteryEverydayTotalVO> batteryEverydayTotalList = fun.apply(queryCondition);
		log.info("db report type : {} ", appVO.getType());
		AppReportDetailVO dailyReport = new AppReportDetailVO();
		List<AppReportDataVO> pvGenerationList = new ArrayList<>();
		List<AppReportDataVO> batteryOutputList = new ArrayList<>();
		List<AppReportDataVO> powerConsumptionList = new ArrayList<>();
		List<AppReportDataVO> gridConsumptionList = new ArrayList<>();
		List<AppReportDataVO> feedInGridList = new ArrayList<>();
		List<AppReportDataVO> batteryInputList = new ArrayList<>();
		for (BatteryEverydayTotalVO entity : batteryEverydayTotalList) {
			AppReportDataVO pv = new AppReportDataVO();
			AppReportDataVO batteryOutput = new AppReportDataVO();
			AppReportDataVO power = new AppReportDataVO();
			AppReportDataVO gridConsumption = new AppReportDataVO();
			AppReportDataVO feedInGrid = new AppReportDataVO();
			AppReportDataVO batteryInput = new AppReportDataVO();
			// 周报表
			if (1 == appVO.getType()) {
				String subTotalDate = entity.getAppTotalDate().substring(5);
				this.setKeyValue(subTotalDate, pv::setKey);
				this.setKeyValue(subTotalDate, batteryOutput::setKey);
				this.setKeyValue(subTotalDate, power::setKey);
				this.setKeyValue(subTotalDate, gridConsumption::setKey);
				this.setKeyValue(subTotalDate, feedInGrid::setKey);
				this.setKeyValue(subTotalDate, batteryInput::setKey);
			} else if (2 == appVO.getType() || 3 == appVO.getType()) {
				// 月、年报表
				String appTotalDate = entity.getAppTotalDate();
				this.setKeyValue(appTotalDate, pv::setKey);
				this.setKeyValue(appTotalDate, batteryOutput::setKey);
				this.setKeyValue(appTotalDate, power::setKey);
				this.setKeyValue(appTotalDate, gridConsumption::setKey);
				this.setKeyValue(appTotalDate, feedInGrid::setKey);
				this.setKeyValue(appTotalDate, batteryInput::setKey);
			} else {
				pv.setKey(simpleDateFormat.format(entity.getAppTotalDate()));
				batteryOutput.setKey(simpleDateFormat.format(entity.getAppTotalDate()));
				power.setKey(simpleDateFormat.format(entity.getAppTotalDate()));
			}
			pv.setValue(this.divideThousand(entity.getAppTodayEnergy()));
			pvGenerationList.add(pv);
			batteryOutput.setValue(this.divideThousand(entity.getAppBatteryDailyDischargeEnergy()));
			batteryOutputList.add(batteryOutput);
			power.setValue(this.divideThousand(entity.getAppLoadAddEps()));
			powerConsumptionList.add(power);
			gridConsumption.setValue(this.divideThousand(entity.getAppTodayImportEnergy()));
			gridConsumptionList.add(gridConsumption);
			feedInGrid.setValue(this.divideThousand(entity.getAppTodayExportEnergy()));
			feedInGridList.add(feedInGrid);
			batteryInput.setValue(this.divideThousand(entity.getAppBatteryDailyChargeEnergy()));
			batteryInputList.add(batteryInput);

		}

		dailyReport.setPvGenerationList(pvGenerationList);
		dailyReport.setBatteryOutputList(batteryOutputList);
		dailyReport.setPowerConsumptionList(powerConsumptionList);
		dailyReport.setGridConsumptionList(gridConsumptionList);
		dailyReport.setFeedInGridList(feedInGridList);
		dailyReport.setBatteryInputList(batteryInputList);
		return dailyReport;
	}

	private void setKeyValue(String value, FunctionSetName<String> function) {
		function.setName(value);
	}

	private BigDecimal divideThousand(BigDecimal value) {
		if (value == null) {
			return new BigDecimal(0);
		}
		return value.divide(BizConstant.THOUSAND, BizConstant.NUMBER_TWO, RoundingMode.HALF_UP);
	}

	private AppReportDetailVO getHoursReportV2(AppVO appVO) throws ParseException {
		// 天报表，每5分钟累计统计
		QueryDeviceLog22Condition query = ReportUtil.getQueryDeviceLog22Condition(appVO);
		// 查询结果按升序排列，同一时间段，后面的覆盖前面的
		log.info("queryPlantRunningStateV2 getHoursReportV2  query log22 begin");
		List<DeviceLog22VO> deviceLog22VoList = deviceLog22ByDorisService.appReportEstimateV2(query);
		log.info("queryPlantRunningStateV2 getHoursReportV2  query log22 end");
		// mysql 测试数据
//		List<DeviceLog22VO> deviceLog22VoList = batteryEverydayTotalService.appReportEstimate(query);
		Function<DeviceLog22VO,BigDecimal> pvFun = DeviceLog22VO::getAppPvTotalInputPower;
		Function<DeviceLog22VO,BigDecimal> batteryPowerSumFun = DeviceLog22VO::getAppBatteryPower;
		Function<DeviceLog22VO,BigDecimal> loadAddEpsFun = DeviceLog22VO::getAppLoadAddEps;
		Function<DeviceLog22VO,BigDecimal> phaseL1WattOfGridSumFun = DeviceLog22VO::getAppPhaserWattOfGrid;
		Function<DeviceLog22VO,BigDecimal> phaseL2WattOfGridSumFun = DeviceLog22VO::getAppPhasesWattOfGrid;
		Function<DeviceLog22VO,BigDecimal> phaseL3WattOfGridSumFun = DeviceLog22VO::getAppPhasetWattOfGrid;
		Function<DeviceLog22VO,BigDecimal> batterySocFun = DeviceLog22VO::getAppBatterySoc;
		AppReportDetailVO appReportDetailVO = ReportUtil.calHoursReportData(deviceLog22VoList, pvFun, batteryPowerSumFun, loadAddEpsFun,
			phaseL1WattOfGridSumFun, phaseL2WattOfGridSumFun, phaseL3WattOfGridSumFun, batterySocFun);
		this.getHourChartEnergyStorage2ByDeviceModelData(appVO,deviceLog22VoList,appReportDetailVO);
		return appReportDetailVO;
		// 初始化每5分钟数据
//		Map<String, BigDecimal> pvTotalInputPowerMap = ReportUtil.initHoursMap();
//		Map<String, BigDecimal> batteryOutputMap = ReportUtil.initHoursMap();
//		Map<String, BigDecimal> powerConsumption4LoadAddEpsMap = ReportUtil.initHoursMap();
//		Map<String, BigDecimal> gridConsumptionMap = ReportUtil.initHoursMap();
//		Map<String, BigDecimal> feedInGridMap = ReportUtil.initHoursMap();
//		Map<String, BigDecimal> batteryInputMap = ReportUtil.initHoursMap();
//		Map<String, BigDecimal> batterySocMap = ReportUtil.initHoursMap();
//		for (DeviceLog22VO deviceLog22VO : deviceLog22VoList) {
//			String appTotalDate = deviceLog22VO.getAppTotalDate();
//			BigDecimal appPvTotalInputPower = deviceLog22VO.getAppPvTotalInputPower();
//			BigDecimal appBatteryPower = deviceLog22VO.getAppBatteryPower() == null ? BigDecimal.ZERO : deviceLog22VO.getAppBatteryPower();
//			BigDecimal loadAddEps = deviceLog22VO.getAppLoadAddEps();
//			BigDecimal appPhaserWattOfGrid = deviceLog22VO.getAppPhaserWattOfGrid() == null ? BigDecimal.ZERO : deviceLog22VO.getAppPhaserWattOfGrid();
//			BigDecimal appPhasesWattOfGrid = deviceLog22VO.getAppPhasesWattOfGrid() == null ? BigDecimal.ZERO : deviceLog22VO.getAppPhasesWattOfGrid();
//			BigDecimal appPhasetWattOfGrid = deviceLog22VO.getAppPhasetWattOfGrid() == null ? BigDecimal.ZERO : deviceLog22VO.getAppPhasetWattOfGrid();
//			BigDecimal appBatterySoc = deviceLog22VO.getAppBatterySoc();
//
//			BigDecimal appBatteryOutput = BigDecimal.ZERO;
//			BigDecimal appBatteryInput = BigDecimal.ZERO;
//			if (appBatteryPower.compareTo(BigDecimal.ZERO) > 0) {
//				// 少于0的时候记录为0,大于0为放电
//				appBatteryOutput = appBatteryPower;
//			} else if (appBatteryPower.compareTo(BigDecimal.ZERO) < 0) {
//				// 正数和0记录为0，负数则取绝对值，小于0则为充电
//				appBatteryInput = appBatteryPower.abs();
//			}
//			BigDecimal positivePhaserWattOfGrid = BigDecimal.ZERO;
//			BigDecimal negativePhaserWattOfGrid = BigDecimal.ZERO;
//			if (appPhaserWattOfGrid.compareTo(BigDecimal.ZERO) > 0) {
//				positivePhaserWattOfGrid = appPhaserWattOfGrid;
//			} else if (appPhaserWattOfGrid.compareTo(BigDecimal.ZERO) < 0) {
//				negativePhaserWattOfGrid = appPhaserWattOfGrid.abs();
//			}
//			BigDecimal positivePhasesWattOfGrid = BigDecimal.ZERO;
//			BigDecimal negativePhasesWattOfGrid = BigDecimal.ZERO;
//			if (appPhasesWattOfGrid.compareTo(BigDecimal.ZERO) > 0) {
//				positivePhasesWattOfGrid = appPhasesWattOfGrid;
//			} else if (appPhasesWattOfGrid.compareTo(BigDecimal.ZERO) < 0) {
//				negativePhasesWattOfGrid = appPhasesWattOfGrid.abs();
//			}
//			BigDecimal positivePhasetWattOfGrid = BigDecimal.ZERO;
//			BigDecimal negativePhasetWattOfGrid = BigDecimal.ZERO;
//			if (appPhasetWattOfGrid.compareTo(BigDecimal.ZERO) > 0) {
//				positivePhasetWattOfGrid = appPhasetWattOfGrid;
//			} else if (appPhasetWattOfGrid.compareTo(BigDecimal.ZERO) < 0) {
//				negativePhasetWattOfGrid = appPhasetWattOfGrid.abs();
//			}
//			BigDecimal gridConsumption = positivePhaserWattOfGrid.add(positivePhasesWattOfGrid).add(positivePhasetWattOfGrid);
//			BigDecimal feedInGrid = negativePhaserWattOfGrid.add(negativePhasesWattOfGrid).add(negativePhasetWattOfGrid);
//			int count = 0;
//			String startDateKey = null;
//			// 先取 第一个，然后将 第一个 和 第二个作为一个区间， 如果数据不在此区间，将 第二个作为第一个，开始下一个区间
//			// 如果相等，则赋值 到 value上，跳出循环
//			// 00:00  00:05 -> 00:05  00:10
//			for (Map.Entry<String, BigDecimal> entry : pvTotalInputPowerMap.entrySet()) {
//				if (count == 0) {
//					startDateKey = entry.getKey();
//					count++;
//					continue;
//				}
//				String endDateKey = entry.getKey();
//				if (appTotalDate.compareTo(startDateKey) > 0 && appTotalDate.compareTo(endDateKey) <= 0) {
//					ReportUtil.setMapValue(pvTotalInputPowerMap, endDateKey, appPvTotalInputPower);
//					ReportUtil.setMapValue(batteryOutputMap, endDateKey, appBatteryOutput);
//					ReportUtil.setMapValue(powerConsumption4LoadAddEpsMap, endDateKey, loadAddEps);
//					ReportUtil.setMapValue(gridConsumptionMap, endDateKey, gridConsumption);
//					ReportUtil.setMapValue(feedInGridMap, endDateKey, feedInGrid);
//					ReportUtil.setMapValue(batteryInputMap, endDateKey, appBatteryInput);
//					ReportUtil.setMapValue(batterySocMap, endDateKey, appBatterySoc);
//					break;
//				} else {
//					startDateKey = endDateKey;
//				}
//			}
//
//		}
//		AppReportDetailVO appHoursReportDetailVO = ReportUtil.getAppHoursReportDetailVO(pvTotalInputPowerMap, batteryOutputMap, powerConsumption4LoadAddEpsMap);
//		ReportUtil.setOtherList(appHoursReportDetailVO, gridConsumptionMap, feedInGridMap, batteryInputMap, batterySocMap);
//		return appHoursReportDetailVO;

	}

	private void getHourChartEnergyStorage2ByDeviceModelData(AppVO appVO,List<DeviceLog22VO> deviceLog22VoList,AppReportDetailVO appReportDetailVO) {
		if(!businessServiceUtil.needEnergyStorage2ByDeviceModel(appVO.getDeviceSerialNumber())) {
			return;
		}
		ReportUtil.getChartEnergyStorage2ByDeviceModelData(deviceLog22VoList, appReportDetailVO);
	}

	private void subtractBeforeData(List<AppReportDataVO> list) {
		BigDecimal beforeCharge = new BigDecimal(0);
		for (AppReportDataVO vo : list) {
			BigDecimal value = vo.getValue();
			if (value.doubleValue() != 0) {
				if (beforeCharge.compareTo(value) > 0) {
					continue;
				}
				BigDecimal result = value.subtract(beforeCharge);
				vo.setValue(result);
				beforeCharge = value;
			}
		}
	}


	private static Map<String, BigDecimal> initHoursMap(LocalDateTime startTime, LocalDateTime endTime) {
		DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("HH:mm");
		LinkedHashMap<String, BigDecimal> map = new LinkedHashMap<>();
		LocalDateTime currentDateTime = startTime;
		while (!currentDateTime.isAfter(endTime)) {
			String format = dateTimeFormatter.format(currentDateTime);
			map.put(format, new BigDecimal("0.0"));
			currentDateTime = currentDateTime.plusMinutes(5);
		}
		map.put(dateTimeFormatter.format(endTime), new BigDecimal("0.0"));
		return map;
	}

	public static void main(String[] args) {
		// 调用示例
		LocalDateTime startTime = LocalDateTime.of(2023, Month.JANUARY, 1, 0, 0);
		LocalDateTime endTime = LocalDateTime.of(2023, Month.JANUARY, 2, 0, 0);
		Map<String, BigDecimal> hoursMap = initHoursMap(startTime, endTime);
		System.out.println(hoursMap);
	}

	private AppReportHeaderVO setReportHeaderData(AppVO appVO) throws ParseException {
		AppReportHeaderVO appReportHeaderVO = new AppReportHeaderVO();
		setHeadCardData(appVO, appReportHeaderVO);
		setChartCardData(appVO, appReportHeaderVO);
		return appReportHeaderVO;
	}


	private void setHeadCardData(AppVO appVO, AppReportHeaderVO appReportHeaderVO) {
		// 查询报表头部数据，注意不要调换顺序
		List<BatteryCurrentStatusEntity> dbBatteryList = this.getBatteryData(appVO, appReportHeaderVO);
		// 查询电网数据
		this.getDeviceData(appVO, appReportHeaderVO);
		// CVGNSW-30KW3P 时增加储能2相关数据
		this.getHeaderEnergyStorage2ByDeviceModelData(appVO, appReportHeaderVO,dbBatteryList);
	}

	private void getHeaderEnergyStorage2ByDeviceModelData(AppVO appVO, AppReportHeaderVO appReportHeaderV2VO, List<BatteryCurrentStatusEntity> dbBatteryList) {
		if(!businessServiceUtil.needEnergyStorage2ByDeviceModel(appVO.getDeviceSerialNumber())) {
			return;
		}
		if(CollectionUtil.isEmpty(dbBatteryList)) {
			log.info("dbBatteryList is null : {}" , appVO.getDeviceSerialNumber());
			return;
		}
		BatteryCurrentStatusEntity dbBatteryEntity = dbBatteryList.get(0);
		// 计算当日储电量
		BigDecimal batteryDailyChargeEnergy = dbBatteryEntity.getBatteryDailyChargeEnergy() == null ?
			BigDecimal.ZERO : dbBatteryEntity.getBatteryDailyChargeEnergy();
		BigDecimal batteryTodayChargeEnergy2 = dbBatteryEntity.getBatteryTodayChargeEnergy2() == null ? BigDecimal.ZERO : dbBatteryEntity.getBatteryTodayChargeEnergy2();
		appReportHeaderV2VO.setBatteryDailyChargeEnergy(DataUnitConversionUtil.getChangeEnergyResult(batteryDailyChargeEnergy.add(batteryTodayChargeEnergy2), 1));
		// 计算当日放电量
		BigDecimal batteryDailyDischargeEnergy = dbBatteryEntity.getBatteryDailyDischargeEnergy() == null ?
			BigDecimal.ZERO : dbBatteryEntity.getBatteryDailyDischargeEnergy();
		BigDecimal batteryTodayDischargeEnergy2 = dbBatteryEntity.getBatteryTodayDischargeEnergy2() == null ? BigDecimal.ZERO : dbBatteryEntity.getBatteryTodayDischargeEnergy2();
		appReportHeaderV2VO.setBatteryTodayDischargeEnergy(DataUnitConversionUtil.getChangeEnergyResult(batteryDailyDischargeEnergy.add(batteryTodayDischargeEnergy2), 1));
		// 计算总 储电量
		// 200d 电池1
		BigDecimal batteryAccumulatedChargeEnergy = dbBatteryEntity.getBatteryAccumulatedChargeEnergy() == null ?
			BigDecimal.ZERO : dbBatteryEntity.getBatteryAccumulatedChargeEnergy();
		// 220d 电池2
		BigDecimal batteryAccumulatedChargeEnergy2 = dbBatteryEntity.getBatteryAccumulatedChargeEnergy2() == null ?
			BigDecimal.ZERO : dbBatteryEntity.getBatteryAccumulatedChargeEnergy2();
		BigDecimal addResult = batteryAccumulatedChargeEnergy.add(batteryAccumulatedChargeEnergy2);
		// app显示  储能总储电量
		appReportHeaderV2VO.setBatteryAccumulatedChargeEnergy(DataUnitConversionUtil.getChangeEnergyResult(addResult, 1));
		appReportHeaderV2VO.setOriginalBatteryAccumulatedChargeEnergy2(batteryAccumulatedChargeEnergy2);

		// 计算总放电量
		// 2011
		BigDecimal batteryAccumulatedDischargeEnergy = dbBatteryEntity.getBatteryAccumulatedDischargeEnergy() == null ?
			BigDecimal.ZERO : dbBatteryEntity.getBatteryAccumulatedDischargeEnergy();
		BigDecimal batteryAccumulatedDischargeEnergy2 =dbBatteryEntity.getBatteryAccumulatedDischargeEnergy2() == null ?
			BigDecimal.ZERO : dbBatteryEntity.getBatteryAccumulatedDischargeEnergy2();
		BigDecimal addResultDischargeEnergy = batteryAccumulatedDischargeEnergy.add(batteryAccumulatedDischargeEnergy2);
		// app显示 储能总放电量
		appReportHeaderV2VO.setBatteryAccumulatedDischargeEnergy(DataUnitConversionUtil.getChangeEnergyResult(addResultDischargeEnergy, 1));
		appReportHeaderV2VO.setOriginalBatteryAccumulatedDischargeEnergy2(batteryAccumulatedDischargeEnergy2);
	}


	private void setChartCardData(AppVO appVO, AppReportHeaderVO appReportHeaderVO) throws ParseException {
		// 获取pv、grid数据
		this.getFromPvAndGrid(appVO, appReportHeaderVO);
	}

	private DeviceEverydayTotalVO setDeviceData(Supplier<BigDecimal> todayEnergy, Supplier<BigDecimal> todayExportEnergy
		, Supplier<BigDecimal> todayLoadEnergy, Supplier<BigDecimal> dailyEnergyToEps, Supplier<BigDecimal> batteryDailyChargeEnergy
		, Supplier<BigDecimal> todayImportEnergy) {
		DeviceEverydayTotalVO deviceEverydayTotalVO = new DeviceEverydayTotalVO();
		deviceEverydayTotalVO.setSumTodayEnergy(todayEnergy.get());
		deviceEverydayTotalVO.setSumTodayExportEnergy(todayExportEnergy.get());
		deviceEverydayTotalVO.setSumTodayLoadEnergy(todayLoadEnergy.get());
		deviceEverydayTotalVO.setSumDailyEnergyToEps(dailyEnergyToEps.get());
		deviceEverydayTotalVO.setSumBatteryDailyChargeEnergy(batteryDailyChargeEnergy.get());
		deviceEverydayTotalVO.setSumTodayImportEnergy(todayImportEnergy.get());
		return deviceEverydayTotalVO;
	}

	private void getFromPvAndGrid(AppVO appVO, AppReportHeaderVO appReportHeaderVO) throws ParseException {
		log.info("queryPlantRunningStateV2 getFromPvAndGrid  begin");
		// 查询日月年报表数据
		if (0 == appVO.getType()) {
			SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
			Date parse = formatter.parse(appVO.getDataScope());
			String appSelectDate = formatter.format(parse);
			Date now = new Date();
			log.info("app appSelectDate date : {} ", appSelectDate);
			log.info("app appSelectDate now date : {} ", formatter.format(now));
			// 判断是否为今天,则取储能当前状态 数据  ,当天的不从当前状态表取，因为昨天有数据，今天设备未连接，那饼图仍显示有数据
//			if (formatter.format(now).equalsIgnoreCase(appSelectDate)) {
////				this.setFromPvAndGrid(appReportHeaderVO.getOriginalTodayEnergy(), appReportHeaderVO.getOriginalTodayImportEnergy(), appReportHeaderVO);
//				DeviceEverydayTotalVO deviceEverydayTotalVO = this.setDeviceData(appReportHeaderVO::getOriginalTodayEnergy, appReportHeaderVO::getOriginalTodayExportEnergy
//					, appReportHeaderVO::getOriginalTodayLoadEnergy, appReportHeaderVO::getOriginalDailyEnergyToEps
//					, appReportHeaderVO::getOriginalBatteryDailyChargeEnergy, appReportHeaderVO::getOriginalTodayImportEnergy);
//				this.setWeekMonthAnnualPieData(appReportHeaderVO, deviceEverydayTotalVO);
//			} else {
			QueryDeviceLog22Condition query = ReportUtil.getQueryDeviceLog22Condition(appVO);
			// 当天报表
			DeviceLog22VO deviceLog22VOS = deviceLog22ByDorisService.appDailyFromPvAndGrid(query);
			if (deviceLog22VOS == null) {
				deviceLog22VOS = new DeviceLog22VO();
			}
//				this.setFromPvAndGrid(deviceLog22VOS.getFromPv(), deviceLog22VOS.getFromGrid(), appReportHeaderVO);
			DeviceEverydayTotalVO deviceEverydayTotalVO = this.setDeviceData(deviceLog22VOS::getOriginalTodayEnergy, deviceLog22VOS::getOriginalTodayExportEnergy
				, deviceLog22VOS::getOriginalTodayLoadEnergy, deviceLog22VOS::getOriginalDailyEnergyToEps
				, deviceLog22VOS::getOriginalBatteryDailyChargeEnergy, deviceLog22VOS::getOriginalTodayImportEnergy);
			this.setWeekMonthAnnualPieData(appReportHeaderVO, deviceEverydayTotalVO);
//			}
		} else if (1 == appVO.getType()) {
			// 周报表，过去7天
			SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
			Date parse = simpleDateFormat.parse(appVO.getDataScope());
			Instant instant = parse.toInstant();
			LocalDate endLocalDate = instant.atZone(ZoneId.systemDefault()).toLocalDate().plusDays(-1L);
			LocalDate beginLocalDate = instant.atZone(ZoneId.systemDefault()).toLocalDate().plusDays(-7L);
			BatteryEverydayTotalEntity batteryEverydayTotalEntity = batteryEverydayTotalService.pieReport(
					ReportUtil.getBatteryQueryCondition(appVO, beginLocalDate, endLocalDate));
			log.info("fromPvAndGrid week db: {} ", batteryEverydayTotalEntity);
			this.setWeekMonthAnnualPieDataV2(appReportHeaderVO, batteryEverydayTotalEntity);
//			DeviceEverydayTotalVO deviceEverydayTotalVO = deviceEverydayTotalService.appFromPvAndGrid(this.getQueryCondition(appVO, beginLocalDate, endLocalDate));
//			if (deviceEverydayTotalVO == null) {
//				deviceEverydayTotalVO = new DeviceEverydayTotalVO();
//			}
//			this.setFromPvAndGrid(deviceEverydayTotalVO.getFromPv(), deviceEverydayTotalVO.getFromGrid(), appReportHeaderVO);
//			this.setWeekMonthAnnualPieData(appReportHeaderVO, deviceEverydayTotalVO);
		} else if (2 == appVO.getType()) {
			// 月报表
			SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM");
			Date dataScope = simpleDateFormat.parse(appVO.getDataScope());
			Instant instant = dataScope.toInstant();
			LocalDate month = instant.atZone(ZoneId.systemDefault()).toLocalDate();
			LocalDate beginLocalDate = month.with(TemporalAdjusters.firstDayOfMonth());
			LocalDate endLocalDate = month.with(TemporalAdjusters.lastDayOfMonth());
			BatteryEverydayTotalEntity batteryEverydayTotalEntity = batteryEverydayTotalService.pieReport(
					ReportUtil.getBatteryQueryCondition(appVO, beginLocalDate, endLocalDate));
			log.info("fromPvAndGrid month db: {} ", batteryEverydayTotalEntity);
			this.setWeekMonthAnnualPieDataV2(appReportHeaderVO, batteryEverydayTotalEntity);
//			DeviceEverydayTotalVO deviceEverydayTotalVO = deviceEverydayTotalService.appFromPvAndGrid(this.getQueryCondition(appVO, beginLocalDate, endLocalDate));
//			log.info("fromPvAndGrid month db: {} ", deviceEverydayTotalVO);
//			if (deviceEverydayTotalVO == null) {
//				deviceEverydayTotalVO = new DeviceEverydayTotalVO();
//			}
//			this.setFromPvAndGrid(deviceEverydayTotalVO.getFromPv(), deviceEverydayTotalVO.getFromGrid(), appReportHeaderVO);
//			this.setWeekMonthAnnualPieData(appReportHeaderVO, deviceEverydayTotalVO);
		} else if (3 == appVO.getType()) {
			// 年报表
			SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy");
			Date dataScope = simpleDateFormat.parse(appVO.getDataScope());
			Instant instant = dataScope.toInstant();
			LocalDate year = instant.atZone(ZoneId.systemDefault()).toLocalDate();
			LocalDate endLocalDate = year.with(TemporalAdjusters.lastDayOfYear());
			LocalDate beginLocalDate = year.with(TemporalAdjusters.firstDayOfYear());
			BatteryEverydayTotalEntity batteryEverydayTotalEntity = batteryEverydayTotalService.pieReport(
					ReportUtil.getBatteryQueryCondition(appVO, beginLocalDate, endLocalDate));
			log.info("fromPvAndGrid year db: {} ", batteryEverydayTotalEntity);
			this.setWeekMonthAnnualPieDataV2(appReportHeaderVO, batteryEverydayTotalEntity);
//			DeviceEverydayTotalVO deviceEverydayTotalVO = deviceEverydayTotalService.appFromPvAndGrid(this.getQueryCondition(appVO, beginLocalDate, endLocalDate));
//			log.info("fromPvAndGrid year db: {} ", deviceEverydayTotalVO);
//			if (deviceEverydayTotalVO == null) {
//				deviceEverydayTotalVO = new DeviceEverydayTotalVO();
//			}
//			this.setFromPvAndGrid(deviceEverydayTotalVO.getFromPv(), deviceEverydayTotalVO.getFromGrid(), appReportHeaderVO);
//			this.setWeekMonthAnnualPieData(appReportHeaderVO, deviceEverydayTotalVO);
		}
		log.info("queryPlantRunningStateV2 getFromPvAndGrid  end");
	}

	private void setWeekMonthAnnualPieDataV2(AppReportHeaderVO appReportHeaderVO, BatteryEverydayTotalEntity batteryEverydayTotalEntity) {
		if (batteryEverydayTotalEntity == null) {
			batteryEverydayTotalEntity = new BatteryEverydayTotalEntity();
		}
		BigDecimal selfConsumed = batteryEverydayTotalEntity.getSelfConsumed() == null ? BigDecimal.ZERO : batteryEverydayTotalEntity.getSelfConsumed();
		BigDecimal fedToGrid = batteryEverydayTotalEntity.getFedToGrid() == null ? BigDecimal.ZERO : batteryEverydayTotalEntity.getFedToGrid();
		BigDecimal selfSufficiency = batteryEverydayTotalEntity.getSelfSufficiency() == null ? BigDecimal.ZERO : batteryEverydayTotalEntity.getSelfSufficiency();
		BigDecimal fromGrid = batteryEverydayTotalEntity.getFromGrid() == null ? BigDecimal.ZERO : batteryEverydayTotalEntity.getFromGrid();
		ReportUtil.setWeekMonthAnnualPieData(appReportHeaderVO,selfConsumed,fedToGrid,selfSufficiency,fromGrid);
//		appReportHeaderVO.setSelfConsumed(DataUnitConversionUtil.getChangeEnergyResult(selfConsumed, 1));
//		appReportHeaderVO.setFedToGrid(DataUnitConversionUtil.getChangeEnergyResult(fedToGrid, 1));
//		BigDecimal firstPieAddResult = selfConsumed.add(fedToGrid);
//		if (BigDecimal.ZERO.compareTo(firstPieAddResult) == 0) {
//			appReportHeaderVO.setSelfConsumedRatio(BigDecimal.ZERO);
//			appReportHeaderVO.setFedToGridRatio(BigDecimal.ZERO);
//			appReportHeaderVO.setSelfConsumedAndFedToGridTotal(DataUnitConversionUtil.getChangeEnergyResult(BigDecimal.ZERO, 1));
//		} else {
//			BigDecimal selfConsumedRatio = selfConsumed.divide(firstPieAddResult, BizConstant.NUMBER_TWO, RoundingMode.HALF_UP)
//				.multiply(new BigDecimal(100)).setScale(0, RoundingMode.HALF_UP);
//			BigDecimal fedToGridRatio = new BigDecimal(100).subtract(selfConsumedRatio);
//			appReportHeaderVO.setSelfConsumedRatio(selfConsumedRatio);
//			appReportHeaderVO.setFedToGridRatio(fedToGridRatio);
//			appReportHeaderVO.setSelfConsumedAndFedToGridTotal(DataUnitConversionUtil.getChangeEnergyAddResult(1, selfConsumed, fedToGrid));
//		}
//
//		appReportHeaderVO.setSelfSufficiency(DataUnitConversionUtil.getChangeEnergyResult(selfSufficiency, 1));
//		appReportHeaderVO.setFromGrid(DataUnitConversionUtil.getChangeEnergyResult(fromGrid, 1));
//		BigDecimal secondPieAddResult = selfSufficiency.add(fromGrid);
//		if (BigDecimal.ZERO.compareTo(secondPieAddResult) == 0) {
//			appReportHeaderVO.setSelfSufficiencyRatio(BigDecimal.ZERO);
//			appReportHeaderVO.setFromGridRatio(BigDecimal.ZERO);
//			appReportHeaderVO.setSelfSufficiencyAndFromGridTotal(DataUnitConversionUtil.getChangeEnergyResult(BigDecimal.ZERO, 1));
//		} else {
//			BigDecimal selfSufficiencyRatio = selfSufficiency.divide(secondPieAddResult, BizConstant.NUMBER_TWO, RoundingMode.HALF_UP)
//				.multiply(new BigDecimal(100)).setScale(0, RoundingMode.HALF_UP);
//			BigDecimal fromGridRatio = new BigDecimal(100).subtract(selfSufficiencyRatio);
//			appReportHeaderVO.setSelfSufficiencyRatio(selfSufficiencyRatio);
//			appReportHeaderVO.setFromGridRatio(fromGridRatio);
//			appReportHeaderVO.setSelfSufficiencyAndFromGridTotal(DataUnitConversionUtil.getChangeEnergyAddResult(1, selfSufficiency, fromGrid));
//		}

	}

	private void setWeekMonthAnnualPieData(AppReportHeaderVO appReportHeaderVO, DeviceEverydayTotalVO deviceEverydayTotalVO) {
		BigDecimal sumTodayEnergy = deviceEverydayTotalVO.getSumTodayEnergy() == null ? BigDecimal.ZERO : deviceEverydayTotalVO.getSumTodayEnergy();
		BigDecimal sumTodayExportEnergy = deviceEverydayTotalVO.getSumTodayExportEnergy() == null ? BigDecimal.ZERO : deviceEverydayTotalVO.getSumTodayExportEnergy();
		BigDecimal sumTodayLoadEnergy = deviceEverydayTotalVO.getSumTodayLoadEnergy() == null ? BigDecimal.ZERO : deviceEverydayTotalVO.getSumTodayLoadEnergy();
		BigDecimal sumDailyEnergyToEps = deviceEverydayTotalVO.getSumDailyEnergyToEps() == null ? BigDecimal.ZERO : deviceEverydayTotalVO.getSumDailyEnergyToEps();
		BigDecimal sumBatteryDailyChargeEnergy = deviceEverydayTotalVO.getSumBatteryDailyChargeEnergy() == null ? BigDecimal.ZERO : deviceEverydayTotalVO.getSumBatteryDailyChargeEnergy();
		BigDecimal sumTodayImportEnergy = deviceEverydayTotalVO.getSumTodayImportEnergy() == null ? BigDecimal.ZERO : deviceEverydayTotalVO.getSumTodayImportEnergy();
		ReportUtil.setDailyPidData(appReportHeaderVO,sumTodayEnergy,sumTodayExportEnergy,sumTodayLoadEnergy,
				sumDailyEnergyToEps,sumBatteryDailyChargeEnergy,sumTodayImportEnergy);
//		// 设置第一排饼图数据
//		if (sumTodayEnergy.compareTo(sumTodayExportEnergy) >= 0) {
//			BigDecimal selfConsumed = sumTodayEnergy.subtract(sumTodayExportEnergy);
//			appReportHeaderVO.setSelfConsumed(DataUnitConversionUtil.getChangeEnergyResult(selfConsumed, 1));
//			appReportHeaderVO.setFedToGrid(DataUnitConversionUtil.getChangeEnergyResult(sumTodayExportEnergy, 1));
//			// 两边的总数
//			BigDecimal addResult = selfConsumed.add(sumTodayExportEnergy);
//			if (BigDecimal.ZERO.compareTo(addResult) == 0) {
//				appReportHeaderVO.setSelfConsumedRatio(BigDecimal.ZERO);
//				appReportHeaderVO.setFedToGridRatio(BigDecimal.ZERO);
//				appReportHeaderVO.setSelfConsumedAndFedToGridTotal(DataUnitConversionUtil.getChangeEnergyResult(BigDecimal.ZERO, 1));
//			} else {
//				BigDecimal selfConsumedRatio = selfConsumed.divide(addResult, BizConstant.NUMBER_TWO, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(0);
//				BigDecimal fedToGridRatio = new BigDecimal(100).subtract(selfConsumedRatio);
//				appReportHeaderVO.setSelfConsumedRatio(selfConsumedRatio);
//				appReportHeaderVO.setFedToGridRatio(fedToGridRatio);
//				appReportHeaderVO.setSelfConsumedAndFedToGridTotal(DataUnitConversionUtil.getChangeEnergyAddResult(1, selfConsumed, sumTodayExportEnergy));
//			}
//		} else {
//			appReportHeaderVO.setSelfConsumed(DataUnitConversionUtil.getChangeEnergyResult(sumTodayEnergy, 1));
//			appReportHeaderVO.setFedToGrid(DataUnitConversionUtil.getChangeEnergyResult(BigDecimal.ZERO, 1));
//			if (sumTodayEnergy.compareTo(BigDecimal.ZERO) == 0) {
//				appReportHeaderVO.setSelfConsumedRatio(BigDecimal.ZERO);
//			} else {
//				appReportHeaderVO.setSelfConsumedRatio(new BigDecimal(100));
//			}
//			appReportHeaderVO.setFedToGridRatio(BigDecimal.ZERO);
//			appReportHeaderVO.setSelfConsumedAndFedToGridTotal(DataUnitConversionUtil.getChangeEnergyAddResult(1, sumTodayEnergy, BigDecimal.ZERO));
//		}
//
//		// 设置第二排饼图数据
//		BigDecimal addResult = sumTodayLoadEnergy.add(sumDailyEnergyToEps).add(sumBatteryDailyChargeEnergy);
//		BigDecimal selfSufficiency = addResult.subtract(sumTodayImportEnergy);
//		appReportHeaderVO.setSelfSufficiency(DataUnitConversionUtil.getChangeEnergyResult(selfSufficiency, 1));
//		appReportHeaderVO.setFromGrid(DataUnitConversionUtil.getChangeEnergyResult(sumTodayImportEnergy, 1));
//		appReportHeaderVO.setSelfSufficiencyAndFromGridTotal(DataUnitConversionUtil.getChangeEnergyAddResult(1, selfSufficiency, sumTodayImportEnergy));
//		if (addResult.compareTo(BigDecimal.ZERO) > 0 && sumTodayImportEnergy.compareTo(BigDecimal.ZERO) > 0) {
//			// 如果相减为负数，则取 1027 地址值
//			if (selfSufficiency.compareTo(BigDecimal.ZERO) < 0) {
//				log.info("addResult and sumTodayImportEnergy both more than zero, but selfSufficiency is negative");
//				ReportUtil.rightIsNegative(appReportHeaderVO, sumTodayEnergy, sumTodayImportEnergy);
//			} else {
//				// 两边的总数
//				BigDecimal add = selfSufficiency.add(sumTodayImportEnergy);
//				BigDecimal selfSufficiencyRatio = selfSufficiency.divide(add, BizConstant.NUMBER_TWO, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(0);
//				appReportHeaderVO.setSelfSufficiencyRatio(selfSufficiencyRatio);
//				appReportHeaderVO.setFromGridRatio(new BigDecimal(100).subtract(selfSufficiencyRatio));
//			}
//		} else if (addResult.compareTo(BigDecimal.ZERO) == 0 && sumTodayImportEnergy.compareTo(BigDecimal.ZERO) == 0) {
//			appReportHeaderVO.setSelfSufficiencyRatio(BigDecimal.ZERO);
//			appReportHeaderVO.setFromGridRatio(BigDecimal.ZERO);
//			log.info("addResult and sumTodayImportEnergy both is zero");
//		} else { // 2边上报数据有一边为负数场景 或者 为0的场景
//			if (selfSufficiency.compareTo(BigDecimal.ZERO) < 0) {
//				log.info("addResult and sumTodayImportEnergy subtract less zero");
//				ReportUtil.rightIsNegative(appReportHeaderVO, sumTodayEnergy, sumTodayImportEnergy);
//			} else {
//				BigDecimal add = selfSufficiency.add(sumTodayImportEnergy);
//				if (add.compareTo(BigDecimal.ZERO) != 0) {
//					log.info("addResult and sumTodayImportEnergy subtract is not zero");
//					BigDecimal selfSufficiencyRatio = selfSufficiency.divide(add, BizConstant.NUMBER_TWO, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(0);
//					appReportHeaderVO.setSelfSufficiencyRatio(selfSufficiencyRatio);
//					appReportHeaderVO.setFromGridRatio(new BigDecimal(100).subtract(selfSufficiencyRatio));
//				} else {
//					log.info("addResult and sumTodayImportEnergy subtract add is zero");
//					appReportHeaderVO.setSelfSufficiencyRatio(BigDecimal.ZERO);
//					appReportHeaderVO.setFromGridRatio(BigDecimal.ZERO);
//				}
//			}
//		}
	}



	private void setFromPvAndGrid(BigDecimal fromPv, BigDecimal fromGrid, AppReportHeaderVO appReportHeaderVO) {
		BigDecimal totalPvGrid = new BigDecimal("0");
		if (fromGrid == null) {
			fromGrid = new BigDecimal("0");
		}
		if (fromPv != null) {
			totalPvGrid = fromPv.add(fromGrid);
		}
		BigDecimal fromPvRatio = new BigDecimal("0");
		BigDecimal fromGridRatio = new BigDecimal("0");
		if (totalPvGrid.compareTo(new BigDecimal("0")) != 0) {
			fromPvRatio = fromPv.divide(totalPvGrid, BizConstant.NUMBER_TWO, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(0);
			fromGridRatio = new BigDecimal(100).subtract(fromPvRatio);//fromGrid.divide(totalPvGrid, BizConstant.NUMBER_TWO, RoundingMode.HALF_UP);
		}

		appReportHeaderVO.setFromPv(DataUnitConversionUtil.getChangeEnergyResult(fromPv, 1));
		appReportHeaderVO.setFromGrid(DataUnitConversionUtil.getChangeEnergyResult(fromGrid, 1));
		appReportHeaderVO.setPvGridTotal(DataUnitConversionUtil.getChangeEnergyResult(totalPvGrid, 1));
		appReportHeaderVO.setFromPvRatio(fromPvRatio);
		appReportHeaderVO.setFromGridRatio(fromGridRatio);
	}

	private List<BatteryCurrentStatusEntity> getBatteryData(AppVO appVO, AppReportHeaderVO appReportHeaderV2VO) {
		BatteryCurrentStatusEntity queryBatteryCurrentStatusEntity = new BatteryCurrentStatusEntity();
		queryBatteryCurrentStatusEntity.setPlantId(appVO.getPlantId());
		queryBatteryCurrentStatusEntity.setDeviceSerialNumber(appVO.getDeviceSerialNumber());
		log.info("queryPlantRunningStateV2 getBatteryData query current status begin");
		List<BatteryCurrentStatusEntity> dbBatteryCurrentStatuList = batteryCurrentStatusService.list(Condition.getQueryWrapper(queryBatteryCurrentStatusEntity));
		log.info("queryPlantRunningStateV2 query current status end");
		if (CollectionUtil.isNotEmpty(dbBatteryCurrentStatuList)) {
			BatteryCurrentStatusEntity dbBatteryCurrentStatus = dbBatteryCurrentStatuList.get(0);
			log.info("before getBatteryData deviceSerialNumber : {}", appVO.getDeviceSerialNumber());
			// 数据库中为瓦
			BigDecimal todayEnergyTemp = dbBatteryCurrentStatus.getTodayEnergy();
			log.info("before getBatteryData todayEnergy : {}", todayEnergyTemp);
			appReportHeaderV2VO.setTodayEnergy(DataUnitConversionUtil.getChangeEnergyResult(todayEnergyTemp, 1));
			appReportHeaderV2VO.setOriginalTodayEnergy(todayEnergyTemp);// 2024.3月版本
			BigDecimal total = dbBatteryCurrentStatus.getTotalEnergy() == null ? new BigDecimal(0) : dbBatteryCurrentStatus.getTotalEnergy();
			BigDecimal multiplyTotal = total.multiply(BizConstant.THOUSAND);
			log.info("before getBatteryData TotalEnergy : {}", multiplyTotal);
			// 数据库中本来为千瓦
			appReportHeaderV2VO.setTotalEnergy(DataUnitConversionUtil.getChangeEnergyResult(multiplyTotal, 1));
			appReportHeaderV2VO.setOriginalTotalEnergy(multiplyTotal);
			BigDecimal batteryDailyDischargeEnergy = dbBatteryCurrentStatus.getBatteryDailyDischargeEnergy();
			log.info("before getBatteryData batteryDailyDischarge : {}", batteryDailyDischargeEnergy);
			appReportHeaderV2VO.setBatteryTodayDischargeEnergy(DataUnitConversionUtil.getChangeEnergyResult(batteryDailyDischargeEnergy, 1));
			BigDecimal batteryTotalDischargeEnergy = dbBatteryCurrentStatus.getBatteryAccumulatedDischargeEnergy();
			log.info("before getBatteryData batteryTotalDischarge : {}", batteryTotalDischargeEnergy);
			appReportHeaderV2VO.setBatteryAccumulatedDischargeEnergy(DataUnitConversionUtil.getChangeEnergyResult(batteryTotalDischargeEnergy, 1));
			appReportHeaderV2VO.setOriginalBatteryAccumulatedDischargeEnergy(batteryTotalDischargeEnergy);
			// 总储电量
			BigDecimal batteryAccumulatedChargeEnergy = dbBatteryCurrentStatus.getBatteryAccumulatedChargeEnergy();
			appReportHeaderV2VO.setBatteryAccumulatedChargeEnergy(DataUnitConversionUtil.getChangeEnergyResult(batteryAccumulatedChargeEnergy, 1));
			appReportHeaderV2VO.setOriginalBatteryAccumulatedChargeEnergy(batteryAccumulatedChargeEnergy);
			SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
			SimpleDateFormat formatter1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			String formatStr = formatter1.format(dbBatteryCurrentStatus.getDeviceDateTime());
			log.info("app header battery db date before : {} ", formatStr);
			Date deviceDateTime = null;
			try {
				deviceDateTime = formatter.parse(formatStr);
				Date now = new Date();
				log.info("app header battery db date : {} ", formatter1.format(deviceDateTime));
				log.info("app header battery now date : {} ", formatter.format(now));
				// 判断是否为今天,不为今天，则天数据为0
				if (!formatter.format(now).equalsIgnoreCase(formatter.format(deviceDateTime))) {
					// 方块数据  from pv 第一列第2个
					appReportHeaderV2VO.setTodayEnergy(BizConstant.BIGDECIMAL_DEFAULT_VALUE_ONE + BizConstant.UNIT_WH);
					// 方块数据  from battery 第一列第1个
					appReportHeaderV2VO.setBatteryTodayDischargeEnergy(BizConstant.BIGDECIMAL_DEFAULT_VALUE_ONE + BizConstant.UNIT_WH);
					appReportHeaderV2VO.setOriginalTodayEnergy(new BigDecimal(0));
				}
			} catch (ParseException e) {
				throw new RuntimeException(e);
			}
			// 计算节约电费
			this.calPrice(appReportHeaderV2VO,total);
			// 计算节能
			this.calConserve(appReportHeaderV2VO,total);
			return dbBatteryCurrentStatuList;
		}

		log.info("queryPlantRunningStateV2 getBatteryData end");
		return new ArrayList<>();
	}

	private void calConserve(AppReportHeaderVO appReportHeaderV2VO,BigDecimal total) {
		List<DictBiz> conserveList = dictBizClient.getListByLang(DictBizCodeEnum.CLIENT_GUARDIAN_CONSERVE.getDictCode(), CommonUtil.getCurrentLanguage()).getData();
		if(CollectionUtil.isEmpty(conserveList)) {
			log.info("queryPlantRunningStateV2 calConserve dict is null");
			return;
		}
		for(DictBiz dictBiz : conserveList) {
			if(DictBizCodeEnum.CLIENT_GUARDIAN_CONSERVE_COAL.getDictCode().equalsIgnoreCase(dictBiz.getDictKey())) {
				appReportHeaderV2VO.setConserveCoal(total.multiply(new BigDecimal(dictBiz.getAttribute1())).setScale(BizConstant.NUMBER_TWO, RoundingMode.HALF_UP));
			} else if(DictBizCodeEnum.CLIENT_GUARDIAN_CONSERVE_TREE.getDictCode().equalsIgnoreCase(dictBiz.getDictKey())) {
				appReportHeaderV2VO.setConserveTree(total.multiply(new BigDecimal(dictBiz.getAttribute1())).setScale(BizConstant.NUMBER_ZERO, RoundingMode.HALF_UP));
			} else if(DictBizCodeEnum.CLIENT_GUARDIAN_CONSERVE_CO2.getDictCode().equalsIgnoreCase(dictBiz.getDictKey())) {
				appReportHeaderV2VO.setConserveCo2(total.multiply(new BigDecimal(dictBiz.getAttribute1())).setScale(BizConstant.NUMBER_TWO, RoundingMode.HALF_UP));
			}
		}
		log.info("queryPlantRunningStateV2 calConserve dict end");
	}

	private void calPrice(AppReportHeaderVO appReportHeaderV2VO,BigDecimal total) {
		BigDecimal todayEnergy = appReportHeaderV2VO.getOriginalTodayEnergy() == null ? new BigDecimal(0) : appReportHeaderV2VO.getOriginalTodayEnergy();
		// 瓦时转换为千瓦时
		BigDecimal todayPrice = CommonConstant.ONE_DEGREE_ELECTRIC_CHARGE_PRICE.multiply(todayEnergy.divide(BizConstant.THOUSAND, RoundingMode.HALF_UP))
				.setScale(BizConstant.NUMBER_TWO, RoundingMode.HALF_UP);
		BigDecimal totalEnergy = total == null ? new BigDecimal(0) :total;
		BigDecimal totalPrice = CommonConstant.ONE_DEGREE_ELECTRIC_CHARGE_PRICE.multiply(totalEnergy).setScale(BizConstant.NUMBER_TWO, RoundingMode.HALF_UP);
		appReportHeaderV2VO.setTodayEnergyPrice(todayPrice);
		appReportHeaderV2VO.setTotalEnergyPrice(totalPrice);
		log.info("calPrice start");
	}
	private void getDeviceData(AppVO appVO, AppReportHeaderVO appReportHeaderV2VO) {
		DeviceCurrentStatusEntity queryDeviceCurrentStatusEntity = new DeviceCurrentStatusEntity();
		queryDeviceCurrentStatusEntity.setPlantId(appVO.getPlantId());
		queryDeviceCurrentStatusEntity.setDeviceSerialNumber(appVO.getDeviceSerialNumber());
		log.info("queryPlantRunningStateV2 getDeviceData  device current status begin");
		List<DeviceCurrentStatusEntity> dbDeviceCurrentStatusList = deviceCurrentStatusService.list(Condition.getQueryWrapper(queryDeviceCurrentStatusEntity));
		log.info("queryPlantRunningStateV2 getDeviceData  device current status end");
		if (CollectionUtil.isNotEmpty(dbDeviceCurrentStatusList)) {
			DeviceCurrentStatusEntity dbDeviceCurrentStatus = dbDeviceCurrentStatusList.get(0);
			BigDecimal todayImportEnergy = dbDeviceCurrentStatus.getTodayImportEnergy();
			BigDecimal accumulatedEnergyOfPositive = dbDeviceCurrentStatus.getAccumulatedEnergyOfPositive();
			// 当日电网输出
			BigDecimal todayExportEnergy = dbDeviceCurrentStatus.getTodayExportEnergy();
			log.info("before getDeviceImportData DeviceSerialNumber : {} ; todayImportEnergy : {} ; totalImportEnergy ： {} ", appVO.getDeviceSerialNumber(),
				todayImportEnergy, accumulatedEnergyOfPositive);
			appReportHeaderV2VO.setTodayImportEnergy(DataUnitConversionUtil.getChangeEnergyResult(todayImportEnergy, 1));
			appReportHeaderV2VO.setOriginalTodayImportEnergy(todayImportEnergy);
			appReportHeaderV2VO.setTotalImportEnergy(DataUnitConversionUtil.getChangeEnergyResult(accumulatedEnergyOfPositive, 1));
//			BigDecimal fromPvBigdecimal = this.subtraction(appReportHeaderV2VO.getOriginalTodayEnergy(), dbDeviceCurrentStatus.getTodayExportEnergy());
			appReportHeaderV2VO.setOriginalTodayExportEnergy(dbDeviceCurrentStatus.getTodayExportEnergy());// 2024.3月版本
			appReportHeaderV2VO.setOriginalTodayLoadEnergy(dbDeviceCurrentStatus.getTodayLoadEnergy());// 2024.3月版本
			appReportHeaderV2VO.setOriginalDailyEnergyToEps(dbDeviceCurrentStatus.getDailyEnergyToEps());// 2024.3月版本
			appReportHeaderV2VO.setOriginalBatteryDailyChargeEnergy(dbDeviceCurrentStatus.getBatteryDailyChargeEnergy());// 2024.3月版本
			appReportHeaderV2VO.setBatteryDailyChargeEnergy(DataUnitConversionUtil.getChangeEnergyResult(dbDeviceCurrentStatus.getBatteryDailyChargeEnergy(),1));
			appReportHeaderV2VO.setOriginalTodayImportEnergy(dbDeviceCurrentStatus.getTodayImportEnergy());// 2024.3月版本
//			appReportHeaderV2VO.setFromPv(DataUnitConversionUtil.getChangeEnergyResult(fromPvBigdecimal, 1));
//			appReportHeaderV2VO.setFromGrid(appReportHeaderV2VO.getTodayImportEnergy());
			SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
			SimpleDateFormat formatter1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			String formatStr = formatter1.format(dbDeviceCurrentStatus.getDeviceDateTime());
			log.info("app header device db date before: {} ", formatStr);
			Date deviceDateTime = null;
			try {
				deviceDateTime = formatter1.parse(formatStr);
				Date now = new Date();
				log.info("app header device db date : {} ", formatter.format(deviceDateTime));
				log.info("app header device now date : {} ", formatter.format(now));
				// 判断是否为今天,不为今天，则天数据为0
				if (!formatter.format(now).equalsIgnoreCase(formatter.format(deviceDateTime))) {
					// 方块数据  from grid 第一列第3个 ，totalImportEnergy为 第2列第3个
					appReportHeaderV2VO.setTodayImportEnergy(BizConstant.BIGDECIMAL_DEFAULT_VALUE_ONE + BizConstant.UNIT_WH);
					appReportHeaderV2VO.setOriginalBatteryDailyChargeEnergy(new BigDecimal("0.0"));
					appReportHeaderV2VO.setBatteryDailyChargeEnergy(BizConstant.BIGDECIMAL_DEFAULT_VALUE_ONE + BizConstant.UNIT_WH);
//					appReportHeaderV2VO.setFromPv(BizConstant.BIGDECIMAL_DEFAULT_VALUE_ONE + BizConstant.UNIT_WH);
//					appReportHeaderV2VO.setFromGrid(BizConstant.BIGDECIMAL_DEFAULT_VALUE_ONE + BizConstant.UNIT_WH);
				}
			} catch (ParseException e) {
				throw new RuntimeException(e);
			}
			log.info("queryPlantRunningStateV2 getDeviceData  end");
		}
		LambdaQueryWrapper<Device23Entity> queryWrapper = Wrappers.<Device23Entity>query().lambda()
			.eq(Device23Entity::getDeviceSerialNumber,appVO.getDeviceSerialNumber())
			.eq(Device23Entity::getPlantId, appVO.getPlantId());
		Device23Entity device23 = device23Service.getOne(queryWrapper);
		appReportHeaderV2VO.setHybridWorkMode("");
		if (ObjUtil.isNotNull(device23) && ValidationUtil.isNotEmpty(device23.getHybridWorkMode())) {
			List<DictBiz> inverterMode = dictBizClient.getListByLang(DictConstant.INVERTER_MODE, CommonUtil.getCurrentLanguage()).getData();
			DictBiz snj = inverterMode.stream()
				.filter(dict -> Func.equals(dict.getDictKey(), device23.getHybridWorkMode()))
				.filter(dict -> Func.equals(dict.getLanguage(), CommonUtil.getCurrentLanguage()))
				.filter(dict -> Func.equals(dict.getAttribute2(), "snj"))
				.findFirst()
				.orElse(null);

			if (ObjUtil.isNotNull(snj)) {
				appReportHeaderV2VO.setHybridWorkMode(snj.getDictValue());
			}
			// 查询模式设置表，如果是智能模式，则不显示设备上报的
			LambdaQueryWrapper<DeviceCustomModeEntity> deviceCustomModeWrapper = new LambdaQueryWrapper<>();
			deviceCustomModeWrapper.eq(DeviceCustomModeEntity::getPlantId, appVO.getPlantId())
				.eq(DeviceCustomModeEntity::getDeviceSerialNumber, appVO.getDeviceSerialNumber())
				.eq(DeviceCustomModeEntity::getHybridWorkMode, DictConstant.INVERTER_MODE_AI);
			List<DeviceCustomModeEntity> deviceCustomModeEntities = deviceCustomModeService.list(deviceCustomModeWrapper);
			if(CollectionUtil.isNotEmpty(deviceCustomModeEntities)) {
				DictBiz dictBiz = inverterMode.stream().filter(p -> DictConstant.INVERTER_MODE_AI.equals(p.getDictKey())).findAny().orElse(new DictBiz());
				appReportHeaderV2VO.setHybridWorkMode(dictBiz.getDictValue());
			}
		}
	}

	public AppReportHeaderVO queryRunningStateChartV2(AppVO appVO) {
		AppReportHeaderVO resultAppReportHeaderVO = new AppReportHeaderVO();
		try {
			// 并机查询并机数据
			if (Constants.ONE.equals(appVO.getIsParallelMode())) {
				resultAppReportHeaderVO = appReportParallelServiceImpl.getParallelReportData(appVO);
			} else {
				this.setChartCardData(appVO, resultAppReportHeaderVO);
				// 查询日月年报表数据
				if (0 == appVO.getType()) {
					AppReportDetailVO hourReport = this.getHoursReportV2(appVO);
//				List<AppReportDataVO> pv = hourReport.getPvGenerationList();
//				List<AppReportDataVO> battery = hourReport.getBatteryOutputList();
//				List<AppReportDataVO> power = hourReport.getPowerConsumptionList();
					// 将后一个时间段总量减去前一个总量，为当前时间段累计的
//				this.subtractBeforeData(pv);
//				this.subtractBeforeData(battery);
//				this.subtractBeforeData(power);
					resultAppReportHeaderVO.setDailyReport(hourReport);
				} else if (1 == appVO.getType()) {
					// 周报表，过去7天
					SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
					Date parse = simpleDateFormat.parse(appVO.getDataScope());
					Instant instant = parse.toInstant();
					LocalDate endLocalDate = instant.atZone(ZoneId.systemDefault()).toLocalDate();
					LocalDate beginLocalDate = endLocalDate.plusDays(-7L);
					Function<QueryCondition, List<BatteryEverydayTotalVO>> dayFun = batteryEverydayTotalService::weekEstimateV2;
					// 查询数据库
					List<BatteryEverydayTotalVO> dailyReportList = ReportUtil.getBatteryEverydayTotalDbData(appVO, beginLocalDate, endLocalDate, dayFun);
					// 将数据库中的值转成 map的key,value形式，非并机的周月年取值字段一样，但是和并机不同，两边定义不同的函数取值
					AppReportDetailVO dailyReport = ReportUtil.setWeekMonthAnnualToKeyValue(appVO, dailyReportList, pvDailyEnergyOrParallelFun, batteryDailyDischargeEnergyOrParallelFun, loadAddOrParallelFun, todayImportEnergyOrParallelFun,
							todayExportEnergyOrParallelFun, batteryDailyChargeEnergyOrParallelFun);
					// 补全全部天数，集合为无序的
					ReportUtil.completionDay(dailyReport, beginLocalDate, endLocalDate, "MM-dd");
					this.getWeekMonthAnnualChartEnergyStorage2ByDeviceModelData(appVO, dailyReportList,
						dailyReport, beginLocalDate, endLocalDate, "MM-dd");
//				AppReportDetailVO dailyReport = this.getAppReportDetailVO(appVO, beginLocalDate, endLocalDate, simpleDateFormat, dayFun);
//				// 补全全部天数，集合为无序的
//				ReportUtil.completionDay(dailyReport, beginLocalDate, endLocalDate, "MM-dd");
					resultAppReportHeaderVO.setWeekReport(dailyReport);
				} else if (2 == appVO.getType()) {
					// 月报表
					SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM");
					Date dataScope = simpleDateFormat.parse(appVO.getDataScope());
					Instant instant = dataScope.toInstant();
					LocalDate month = instant.atZone(ZoneId.systemDefault()).toLocalDate();
					LocalDate beginLocalDate = month.with(TemporalAdjusters.firstDayOfMonth());
					LocalDate endLocalDate = month.with(TemporalAdjusters.lastDayOfMonth());
					Function<QueryCondition, List<BatteryEverydayTotalVO>> monthFun = batteryEverydayTotalService::monthEstimateV2;
					// 查询数据库
					List<BatteryEverydayTotalVO> monthReportList = ReportUtil.getBatteryEverydayTotalDbData(appVO, beginLocalDate, endLocalDate, monthFun);
					// 将数据库中的值转成 map的key,value形式，非并机的周月年取值字段一样，但是和并机不同，两边定义不同的函数取值
					AppReportDetailVO monthReport = ReportUtil.setWeekMonthAnnualToKeyValue(appVO, monthReportList, pvDailyEnergyOrParallelFun, batteryDailyDischargeEnergyOrParallelFun, loadAddOrParallelFun, todayImportEnergyOrParallelFun,
							todayExportEnergyOrParallelFun, batteryDailyChargeEnergyOrParallelFun);
					// 补齐数据库中不存在的 天值
					ReportUtil.completionDay(monthReport, beginLocalDate, endLocalDate, "dd");
					this.getWeekMonthAnnualChartEnergyStorage2ByDeviceModelData(appVO, monthReportList,
							monthReport, beginLocalDate, endLocalDate, "dd");
//				AppReportDetailVO monthReport = this.getAppReportDetailVO(appVO, beginLocalDate, endLocalDate, simpleDateFormat, monthFun);
//				ReportUtil.completionDay(monthReport, beginLocalDate, endLocalDate, "dd");
					resultAppReportHeaderVO.setMonthlyReport(monthReport);
				} else if (3 == appVO.getType()) {
					// 年报表
					SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy");
					Date dataScope = simpleDateFormat.parse(appVO.getDataScope());
					Instant instant = dataScope.toInstant();
					LocalDate year = instant.atZone(ZoneId.systemDefault()).toLocalDate();
					LocalDate endLocalDate = year.with(TemporalAdjusters.lastDayOfYear());
					LocalDate beginLocalDate = year.with(TemporalAdjusters.firstDayOfYear());
					Function<QueryCondition, List<BatteryEverydayTotalVO>> annualFun = batteryEverydayTotalService::annualEstimateV2;
					// 查询数据库
					List<BatteryEverydayTotalVO> annualReportList = ReportUtil.getBatteryEverydayTotalDbData(appVO, beginLocalDate, endLocalDate, annualFun);
					// 将数据库中的值转成 map的key,value形式，非并机的周月年取值字段一样，但是和并机不同，两边定义不同的函数取值
					AppReportDetailVO annualReport = ReportUtil.setWeekMonthAnnualToKeyValue(appVO, annualReportList, pvDailyEnergyOrParallelFun, batteryDailyDischargeEnergyOrParallelFun, loadAddOrParallelFun, todayImportEnergyOrParallelFun,
							todayExportEnergyOrParallelFun, batteryDailyChargeEnergyOrParallelFun);
					// 补齐数据库中不存在的 月份值
					ReportUtil.completionMonth(annualReport);
					// 年的补齐月份，不需要传最后一个参数，方法中出来了
					this.getWeekMonthAnnualChartEnergyStorage2ByDeviceModelData(appVO, annualReportList,
							annualReport, beginLocalDate, endLocalDate, null);
//				AppReportDetailVO annualReport = this.getAppReportDetailVO(appVO, beginLocalDate, endLocalDate, simpleDateFormat, annualFun);
//				ReportUtil.completionMonth(annualReport);
					resultAppReportHeaderVO.setAnnualReport(annualReport);
				}
			}
		} catch (Exception e) {
			log.error("error ParseException ： ", e);
		}
		return resultAppReportHeaderVO;
	}

	private void getWeekMonthAnnualChartEnergyStorage2ByDeviceModelData(AppVO appVO, List<BatteryEverydayTotalVO> dbBatteryEverydayTotalList,
																		AppReportDetailVO report, LocalDate beginLocalDate, LocalDate endLocalDate, String format) throws ParseException {
		if(!businessServiceUtil.needEnergyStorage2ByDeviceModel(appVO.getDeviceSerialNumber())) {
			return;
		}
		// 将数据库中的值 组装成集合
		ReportUtil.setWeekMonthAnnualToKeyValueChartEnergyStorage2ByDeviceModel(appVO,dbBatteryEverydayTotalList, batteryTodayChargeEnergy2Fun,batteryTodayDischargeEnergy2Fun,report);
		if (1 == appVO.getType() || 2 == appVO.getType() ) {
			// 补齐数据库中不存在的 天 数据
			ReportUtil.completionDayChartEnergyStorage2ByDeviceModelData(report, beginLocalDate, endLocalDate, format);
		} else if (3 == appVO.getType()) {
			// 补齐数据库中不存在的 月 数据
			ReportUtil.completionMonthChartEnergyStorage2ByDeviceModelData(report);
		}
	}
}

