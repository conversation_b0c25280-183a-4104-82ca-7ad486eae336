/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.plant.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import lombok.AllArgsConstructor;
import org.apache.ibatis.annotations.Param;
import org.apache.logging.log4j.util.Strings;
import org.skyworth.ess.app.service.impl.AppServiceImpl;
import org.skyworth.ess.app.vo.AppBatteryExitFactoryInfoVO;
import org.skyworth.ess.app.vo.AppPlantCountVO;
import org.skyworth.ess.battery.entity.BatteryExitFactoryInfoEntity;
import org.skyworth.ess.battery.entity.BatteryMapDeviceEntity;
import org.skyworth.ess.battery.entity.QueryCondition;
import org.skyworth.ess.battery.service.IBatteryCurrentStatusService;
import org.skyworth.ess.battery.service.IBatteryExitFactoryInfoService;
import org.skyworth.ess.battery.service.IBatteryMapDeviceService;
import org.skyworth.ess.device.client.IDeviceIssueBiz;
import org.skyworth.ess.device.entity.*;
import org.skyworth.ess.device.service.*;
import org.skyworth.ess.device.vo.DeviceExitFactoryInfoVO;
import org.skyworth.ess.event.entity.ImportantEventEntity;
import org.skyworth.ess.event.service.IImportantEventService;
import org.skyworth.ess.feign.IAgentClient;
import org.skyworth.ess.guardian.guardianplant.service.IGuardianPlantService;
import org.skyworth.ess.lazzen.gatewayplant.entity.GatewayPlantEntity;
import org.skyworth.ess.lazzen.gatewayplant.service.IGatewayPlantService;
import org.skyworth.ess.permissions.service.IAgentUnauthorizedUserService;
import org.skyworth.ess.plant.entity.PlantEntity;
import org.skyworth.ess.plant.entity.WifiStickPlantEntity;
import org.skyworth.ess.plant.excel.PlantExcel;
import org.skyworth.ess.plant.mapper.PlantMapper;
import org.skyworth.ess.plant.service.IPlantService;
import org.skyworth.ess.plant.service.IWifiStickPlantService;
import org.skyworth.ess.plant.vo.PlantInstallInfoVO;
import org.skyworth.ess.plant.vo.PlantVO;
import org.skyworth.ess.plant.wrapper.PlantWrapper;
import org.skyworth.ess.vo.AgentCompanyVO;
import org.skyworth.ess.vo.AgentUserVo;
import org.springblade.common.constant.BizConstant;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.I18nMsgCode;
import org.springblade.common.utils.CollectionUtils;
import org.springblade.common.utils.CommonUtil;
import org.springblade.common.utils.DataUnitConversionUtil;
import org.springblade.common.utils.StatusDisplayUtil;
import org.springblade.common.utils.tool.BeanUtils;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tenant.annotation.TenantIgnore;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.*;
import org.springblade.system.cache.DictBizCache;
import org.springblade.system.cache.RegionCache;
import org.springblade.system.entity.Region;
import org.springblade.system.entity.User;
import org.springblade.system.feign.ISysClient;
import org.springblade.system.feign.IUserClient;
import org.springblade.system.feign.IUserSearchClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 站点信息表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
@Service
@AllArgsConstructor
public class PlantServiceImpl extends BaseServiceImpl<PlantMapper, PlantEntity> implements IPlantService {

	private final IImportantEventService importantEventService;
	private final IDeviceIssueBiz deviceIssueBiz;
	private final IDevice21Service device21Service;
	private final IBatteryCurrentStatusService batteryCurrentStatusService;

	private final IDeviceCurrentStatusService deviceCurrentStatusService;
	private final IDeviceEverydayTotalService deviceEverydayTotalService;
	private final IDeviceExitFactoryInfoService deviceExitFactoryInfoService;
	private final IBatteryExitFactoryInfoService batteryExitFactoryInfoService;
	private final IBatteryMapDeviceService batteryMapDeviceService;
	private final ISysClient sysClient;
	private final IUserClient userClient;
	private final IAgentUnauthorizedUserService agentUnauthorizedUserService;
	private final IUserSearchClient userSearchClient;
	private final IAgentClient agentClient;
	private final IWifiStickPlantService wifiStickPlantService;
	private final IDevice23Service device23Service;
	private final IDevice24Service device24Service;
	private final IGuardianPlantService guardianPlantService;
	private final IGatewayPlantService gatewayPlantService;

	@Override
	public boolean save(PlantEntity plant) {
		// 登录用户不能创建相同的站点名称
		LambdaQueryWrapper<PlantEntity> eq = Wrappers.<PlantEntity>query().lambda().eq(PlantEntity::getPlantName, plant.getPlantName())
			.eq(PlantEntity::getCreateUser, plant.getCreateUser());
		Long plantCount = baseMapper.selectCount(eq);
		if (plantCount > 0L && Func.isEmpty(plant.getId())) {
			throw new BusinessException("client.plant.name.exist", plant.getPlantName());
		}
		return super.save(plant);
	}

	@Override
	public List<PlantEntity> queryPlant(PlantEntity eq) {
		return baseMapper.queryPlant(eq);
	}


	@Override
	@Transactional(rollbackFor = Exception.class)
	public R<Boolean> removePlantByIds(String ids) {
		R r = new R<>();
		String currentLanguage = CommonUtil.getCurrentLanguage();
		if (StringUtil.isBlank(ids)) {
			return R.fail("the plant id cannot be empty.");
		}
		List<Long> longList = Func.toLongList(ids);
		List<PlantEntity> plantEntities = this.listByIds(longList);
		if (CollectionUtils.isNullOrEmpty(plantEntities)) {
			r.setCode(I18nMsgCode.SKYWORTH_CLIENT_PLANT_100028.getCode());
			r.setMsg(I18nMsgCode.SKYWORTH_CLIENT_PLANT_100028.autoGetMessage(currentLanguage));
			r.setSuccess(false);
			return r;

		} else {
			for (PlantEntity plantEntity : plantEntities) {
				// 如果该id查不到数据，还调用该接口，则提示异常
				if (!longList.contains(plantEntity.getId())) {
					r.setCode(I18nMsgCode.SKYWORTH_CLIENT_PLANT_100028.getCode());
					r.setMsg(I18nMsgCode.SKYWORTH_CLIENT_PLANT_100028.autoGetMessage(currentLanguage));
					r.setSuccess(false);
					return r;
				}
			}
		}

		BladeUser user = AuthUtil.getUser();

		List<ImportantEventEntity> insertList = new ArrayList<>();
		for (Long plantId : longList) {
			ImportantEventEntity importantEventEntity = new ImportantEventEntity();
			importantEventEntity.setEventType(BizConstant.CLIENT_IMPORTANT_EVENT_TYPE_PLANT);
			importantEventEntity.setPlantId(plantId);
			importantEventEntity.setEventContent("client.guardian.important.event.delete.plant");
			importantEventEntity.setEventDate(new Date());
			importantEventEntity.setCreateUserAccount(user.getAccount());
			importantEventEntity.setCreateUser(user.getUserId());
			insertList.add(importantEventEntity);
		}
		// delete battery important
		importantEventService.saveBatch(insertList);

		// 将电站下 智能能量变换器出厂设备  更新为 未使用
		List<WifiStickPlantEntity> wifiStickPlantEntities = wifiStickPlantService.queryDeviceSerialNumberList(longList);
		List<String> deviceSerialNumberList = new ArrayList<>();
		for (WifiStickPlantEntity entity : wifiStickPlantEntities) {
			Long plantId = longList.get(BizConstant.NUMBER_ZERO);
			String deviceSerialNumber = entity.getDeviceSerialNumber();
			deviceSerialNumberList.add(deviceSerialNumber);
			//发送mqtt通知设备,智能能量变换器与站点已经解绑
			JSONObject jsonObject = new JSONObject();
			jsonObject.put("deviceSn", deviceSerialNumber);
			jsonObject.put("topic", Constants.UNBIND_INVERTER);
			deviceIssueBiz.dataIssueToDevice(jsonObject);
			// 删除设备21表数据
			device21Service.deleteByPlantId(plantId, deviceSerialNumber);
			device23Service.deleteByPlantId(plantId, deviceSerialNumber);
			device24Service.deleteByPlantId(plantId, deviceSerialNumber);
		}

		// 将电站下的 出厂设备信息 中 储能包更新为未使用
		List<BatteryMapDeviceEntity> batteryMapDeviceEntities = batteryMapDeviceService.queryListByPlantId(longList);
		if (batteryMapDeviceEntities != null && !batteryMapDeviceEntities.isEmpty()) {
			List<String> batterySerialList = batteryMapDeviceEntities.stream().map(BatteryMapDeviceEntity::getBatterySerialNumber).collect(Collectors.toList());
			batteryExitFactoryInfoService.batchUpdate(batterySerialList, BizConstant.NUMBER_ZERO);
		}
		if (!deviceSerialNumberList.isEmpty()) {
			deviceExitFactoryInfoService.batchUpdate(deviceSerialNumberList);
		}
		// 删除 站点 和 智能能量变换器 的关系
		wifiStickPlantService.batchDeleteLogicByPlantId(longList, user.getAccount());
		// 删除 站点 和 储能包的 关系
		batteryMapDeviceService.batchDeleteLogicByPlantId(longList, user.getAccount());
		this.deleteLogic(longList);

		// 删除battery_current_status、device_current_status相关数据
		batteryCurrentStatusService.batchDeleteLogicByPlantId(longList, user.getAccount());
		deviceCurrentStatusService.batchDeleteLogicByPlantId(longList, user.getAccount());
		return R.success("successfully delete.");
	}

	@Override
	public PlantVO homePage() {
		PlantVO plantVO = new PlantVO();
		LambdaQueryWrapper<PlantEntity> lambda = Wrappers.<PlantEntity>query().lambda();
		Long plantCount = baseMapper.selectCount(lambda);
		plantVO.setPlantNum(plantCount);
		// 光伏发电功率总和
		List<DeviceCurrentStatusEntity> currentStatusEntityList = deviceCurrentStatusService.list();
		BigDecimal pvPower = new BigDecimal(0);
		for (DeviceCurrentStatusEntity current : currentStatusEntityList) {
			pvPower = pvPower.add(this.add(current.getMppt1Power(), current.getMppt2Power(), current.getMppt3Power(), current.getMppt4Power()));
		}
		plantVO.setPvPowerGenerationSum(pvPower);
		// 历史光伏发电功率累计总和
		BigDecimal pvHisPower = new BigDecimal(0);
		List<DeviceEverydayTotalEntity> everydayTotalEntityList = deviceEverydayTotalService.list();
		for (DeviceEverydayTotalEntity everyday : everydayTotalEntityList) {
			pvHisPower = pvHisPower.add(this.add(everyday.getTodayEnergy()));
		}
		plantVO.setPvHisPowerGenerationSum(pvHisPower);
		// 储能包总功率
		BigDecimal totalEnergy = new BigDecimal(0);
		List<DeviceExitFactoryInfoEntity> deviceExitFacotryEntityList = deviceExitFactoryInfoService.list();
		for (DeviceExitFactoryInfoEntity device : deviceExitFacotryEntityList) {
			if (StringUtil.isNotBlank(device.getPower())) {
				totalEnergy = totalEnergy.add(this.add(new BigDecimal(device.getPower())));
			}
		}
		plantVO.setTotalEnergy(totalEnergy);
		// 储能包装机总电量
		BigDecimal totalBatteryCapacity = new BigDecimal(0);
		List<BatteryExitFactoryInfoEntity> batteryExitFactoryInfoEntityList = batteryExitFactoryInfoService.list();
		for (BatteryExitFactoryInfoEntity battery : batteryExitFactoryInfoEntityList) {
			if (StringUtil.isNotBlank(battery.getRatedBatteryEnergy())) {
				totalBatteryCapacity = totalBatteryCapacity.add(this.add(new BigDecimal(battery.getRatedBatteryEnergy())));
			}
		}
		plantVO.setTotalBatteryCapacity(totalBatteryCapacity);
		return plantVO;
	}

	private BigDecimal add(BigDecimal... dataArray) {
		BigDecimal result = new BigDecimal(0);
		for (BigDecimal data : dataArray) {
			if (data == null) {
				continue;
			}
			result = result.add(data);
		}
		return result;
	}

	//
	@Override
	public IPage<PlantVO> page(Query query, PlantVO plantVO) {
		IPage<PlantVO> page = Condition.getPage(query);

		//搜索用户名
		List<User> userList;
		String createdByName = plantVO.getCreatedByName();
		if (ValidationUtil.isNotEmpty(createdByName)) {
			userList = userSearchClient.listByRealName(createdByName).getData();
			if (ValidationUtil.isNotEmpty(userList) && !userList.isEmpty()) {
				String userIds = userList.stream().map(userVo -> Func.toStr(userVo.getId())).distinct().collect(Collectors.joining(","));
				plantVO.setCreateUserIds(Func.toLongList(userIds));
			} else {
				return page.setRecords(new ArrayList<>());
			}
		}

		//搜索用户手机号
		List<User> userList2;
		String phone = plantVO.getPhone();
		if (ValidationUtil.isNotEmpty(phone)) {
			userList2 = userSearchClient.listByPhone(phone).getData();
			if (ValidationUtil.isNotEmpty(userList2) && !userList2.isEmpty()) {
				String userIds = userList2.stream().map(userVo -> Func.toStr(userVo.getId())).distinct().collect(Collectors.joining(","));
				plantVO.setPhoneUserIds(Func.toLongList(userIds));
			} else {
				return page.setRecords(new ArrayList<>());
			}
		}

		//搜索运维人员
		List<User> agentUserList;
		String agentUserName = plantVO.getAgentUserName();
		if (ValidationUtil.isNotEmpty(agentUserName)) {
			agentUserList = userSearchClient.listByRealName(agentUserName).getData();
			if (ValidationUtil.isNotEmpty(agentUserList) && !agentUserList.isEmpty()) {
				String userIds = agentUserList.stream().map(userVo -> Func.toStr(userVo.getId())).distinct().collect(Collectors.joining(","));
				plantVO.setOperationUserIds(Func.toLongList(userIds));
			} else {
				return page.setRecords(new ArrayList<>());
			}
		}

		//搜索运维团队
		String companyName = plantVO.getAgentCompanyName();
		List<AgentCompanyVO> agentCompanyVOList;
		if (ValidationUtil.isNotEmpty(companyName)) {
			agentCompanyVOList = agentClient.agentCompany(companyName).getData();
			if (ValidationUtil.isNotEmpty(agentCompanyVOList) && !agentCompanyVOList.isEmpty()) {
				String companyIds = agentCompanyVOList.stream().map(companyVO -> Func.toStr(companyVO.getDeptId())).distinct().collect(Collectors.joining(","));
				plantVO.setOperationCompanyIds(Func.toLongList(companyIds));
			} else {
				return page.setRecords(new ArrayList<>());
			}
		}

		BladeUser user = AuthUtil.getUser();
		String deptId = AppServiceImpl.inspectInnerRole(user);
		plantVO.setCreateUser(user.getUserId());
		plantVO.setDeptId(deptId);
		// 获取用户类型，展示站点状态
		String userType = StatusDisplayUtil.getRoleType(user.getRoleName(),user.getDeptId());
		List<PlantVO> plantVOList = baseMapper.selectPlantPage(page, plantVO, userType);
		List<String> regionCodeList = new ArrayList<>();
		String currentLanguage = CommonUtil.getCurrentLanguage();

		// 站点信息list转用户ID集合
		List<Long> userIdList = plantVOList.stream().map(PlantVO::getCreateUser)
			.filter(ObjectUtil::isNotEmpty).distinct().collect(Collectors.toList());
		// 获取用户信息
		R<List<User>> userListR = userSearchClient.listAllByUserIds(userIdList);

		Map<Long, User> userMap = new HashMap<>();
		// 响应成功并且数据不为空
		if (userListR.isSuccess() && CollectionUtil.isNotEmpty(userListR.getData())) {
			userMap = userListR.getData().stream().collect(Collectors.toMap(User::getId, Function.identity()));
		}

		//获取代理商人员信息
		Map<Long, AgentUserVo> agentUserVoMap = new HashMap<>();
		getAgentUserInfo(plantVOList, agentUserVoMap);

		//获取代理商信息
		Map<Long, AgentCompanyVO> companyMap = new HashMap<>();
		getCompanyInfo(plantVOList, companyMap);

		for (PlantVO plant : plantVOList) {
			this.setIncludeDevices(plant, currentLanguage);
			QueryCondition queryCondition = new QueryCondition();
			queryCondition.setPlantId(plant.getId());
			regionCodeList.add(plant.getCountryCode());
			regionCodeList.add(plant.getProvinceCode());
			regionCodeList.add(plant.getCityCode());
			regionCodeList.add(plant.getCountyCode());

			if (ObjectUtil.isNotEmpty(userMap.get(plant.getCreateUser()))) {
				// 设置用户电话号码
				plant.setPhone(StringUtil.isBlank(userMap.get(plant.getCreateUser()).getPhone())
					? "" : userMap.get(plant.getCreateUser()).getPhone());
				// 设置用户电话号码区号
				plant.setPhoneDiallingCode(StringUtil.isBlank(userMap.get(plant.getCreateUser()).getPhoneDiallingCode())
					? "" : userMap.get(plant.getCreateUser()).getPhoneDiallingCode());
				// 设置用户名
				plant.setCreatedByName(StringUtil.isBlank(userMap.get(plant.getCreateUser()).getRealName())
					? "" : userMap.get(plant.getCreateUser()).getRealName());
			} else {
				plant.setPhone("");
				plant.setPhoneDiallingCode("");
				plant.setCreatedByName("");
			}

			Long operationUserId = plant.getOperationUserId();
			Long operationCompanyId = plant.getOperationCompanyId();
			if (ValidationUtil.isNotEmpty(operationUserId)) {
				if (!CollectionUtils.isNullOrEmpty(agentUserVoMap) && agentUserVoMap.containsKey(operationUserId)) {
					plant.setAgentUserName(agentUserVoMap.get(operationUserId).getRealName());
				}
			}
			if (ValidationUtil.isNotEmpty(operationCompanyId)) {
				if (!CollectionUtils.isNullOrEmpty(companyMap) && companyMap.containsKey(operationCompanyId)) {
					plant.setAgentCompanyName(companyMap.get(operationCompanyId).getCompanyName());
				}
			}
			/*// 设置站点状态
			plantVO.setPlantStatus(StatusDisplayUtil.plantAndInverterStatusConvert(plantVO.getPlantStatus(), userType,
				plantVO.getExistUserTypeAlarm(), plantVO.getExistAgentTypeAlarm()));*/
		}
		this.setRegionInfo(regionCodeList, plantVOList);

		return page.setRecords(plantVOList);
	}

	private void setIncludeDevices(PlantVO plant, String currentLanguage) {
		StringBuilder containDevice = new StringBuilder();
		if (CommonConstant.CURRENT_LANGUAGE_ZH.equals(currentLanguage)) {
			if (plant.getPhotovoltaicNumber() != null && plant.getPhotovoltaicNumber() > 0) {
				containDevice.append(",光伏");
			}
			if (plant.getDeviceNumber() != null && plant.getDeviceNumber() > 0) {
				containDevice.append(",智能能量变换器");
			}
			if (plant.getBatteryNumber() != null && plant.getBatteryNumber() > 0) {
				containDevice.append(",储能包");
			}
			if (plant.getChargingStation() != null && plant.getChargingStation() > 0) {
				containDevice.append(",充电桩");
			}
		} else {
			if (plant.getPhotovoltaicNumber() != null && plant.getPhotovoltaicNumber() > 0) {
				containDevice.append(",photovoltaic");
			}
			if (plant.getDeviceNumber() != null && plant.getDeviceNumber() > 0) {
				containDevice.append(",inverter");
			}
			if (plant.getBatteryNumber() != null && plant.getBatteryNumber() > 0) {
				containDevice.append(",battery");
			}
			if (plant.getChargingStation() != null && plant.getChargingStation() > 0) {
				containDevice.append(",charging Station");
			}
		}

		if (containDevice.length() > 0) {
			containDevice.deleteCharAt(0);
		}
		plant.setIncludeDevices(containDevice.toString());
	}

	private void setRegionInfo(List<String> regionCodeList, List<PlantVO> plantVOList) {
		if (CollectionUtil.isEmpty(regionCodeList)) {
			return;
		}
		List<String> regionCodeNotNullList = regionCodeList.stream().filter(StringUtil::isNotBlank).collect(Collectors.toList());
		if (CollectionUtil.isNotEmpty(regionCodeNotNullList)) {
			R<List<Region>> regionResult = sysClient.getRegionList(regionCodeNotNullList);
			List<Region> regionList = regionResult.getData();
			for (PlantVO plantVO : plantVOList) {
				StringBuilder address = new StringBuilder();
				for (Region region : regionList) {
					if (region.getCode().equalsIgnoreCase(plantVO.getCountryCode())) {
						address.append(region.getName()).append(" ");
						plantVO.setCountryName(region.getName());
					}
					if (region.getCode().equalsIgnoreCase(plantVO.getProvinceCode())) {
						address.append(region.getName()).append(" ");
						plantVO.setProvinceName(region.getName());
					}
					if (region.getCode().equalsIgnoreCase(plantVO.getCityCode())) {
						address.append(region.getName()).append(" ");
						plantVO.setCityName(region.getName());
					}
				}
				plantVO.setAddress(address.append(" ").append(plantVO.getDetailAddress() == null ? "" : plantVO.getDetailAddress()).toString());
			}
		}
	}

	@Override
	public List<PlantInstallInfoVO> installedInfo(PlantVO plantVO) {
		List<PlantInstallInfoVO> result = new ArrayList<>();
		List<WifiStickPlantEntity> deviceNumberList = wifiStickPlantService.list(Wrappers.<WifiStickPlantEntity>lambdaQuery().eq(WifiStickPlantEntity::getPlantId, plantVO.getId()));
		if (CollectionUtil.isEmpty(deviceNumberList)) {
			return result;
		}
		List<String> deviceSerialNumberCollect = deviceNumberList.stream().map(WifiStickPlantEntity::getDeviceSerialNumber).distinct().collect(Collectors.toList());
		List<DeviceExitFactoryInfoEntity> deviceExitFactoryInfoEntityList = deviceExitFactoryInfoService.getListByDeviceSerialNumberCollect(deviceSerialNumberCollect);
		Map<String, String> deviceTypeMap = deviceExitFactoryInfoEntityList.stream().collect(Collectors.toMap(DeviceExitFactoryInfoEntity::getDeviceSerialNumber, DeviceExitFactoryInfoEntity::getDeviceType));

		List<BatteryMapDeviceEntity> BatteryMapDeviceList = batteryMapDeviceService.list(Wrappers.<BatteryMapDeviceEntity>lambdaQuery().eq(BatteryMapDeviceEntity::getPlantId, plantVO.getId()));

		List<GatewayPlantEntity> gatewayPlantEntities=gatewayPlantService.list(Wrappers.<GatewayPlantEntity>lambdaQuery().eq(GatewayPlantEntity::getPlantId, plantVO.getId()));
		int guardianSize=0;
		if(ValidationUtil.isNotEmpty(gatewayPlantEntities)&&!gatewayPlantEntities.isEmpty()){
			guardianSize=gatewayPlantEntities.size();
		}
		if (CollectionUtil.isNotEmpty(deviceNumberList)) {
			int tmpLen=1;
			for (WifiStickPlantEntity entity : deviceNumberList) {
				PlantInstallInfoVO vo = new PlantInstallInfoVO();
				vo.setDeviceSerialNumber(entity.getDeviceSerialNumber());
				vo.setBatteryBox(Optional.ofNullable(BatteryMapDeviceList).orElse(new ArrayList<>()).stream()
					.map(a -> {
						Map<String, String> map = new HashMap<>();
						map.put("batterySerialNumber", a.getBatterySerialNumber());
						map.put("batteryEnergyStorageNumber", a.getBatteryEnergyStorageNumber().toString());
						return map;
					}).collect(Collectors.toList()));
				if (deviceTypeMap.containsKey(entity.getDeviceSerialNumber())){
					vo.setInverterKind(DictBizCache.getValue(BizConstant.INVERT_KIND_DICT_CODE, deviceTypeMap.get(entity.getDeviceSerialNumber())));
					vo.setInverterDeviceType(deviceTypeMap.get(entity.getDeviceSerialNumber()));
				}
				result.add(vo);
				if(guardianSize!=0){
					if(tmpLen<=guardianSize) {
						vo.setSecurityGuardSerialNumber(gatewayPlantEntities.get(tmpLen-1).getGatewayUniqueNumber());
					}
					++tmpLen;
				}
				vo.setGuardianBox(Optional.ofNullable(gatewayPlantEntities).orElse(new ArrayList<>()).stream()
					.filter(gatewayPlantEntity -> gatewayPlantEntity.getDeviceType().equals(BizConstant.CLIENT_IMPORTANT_EVENT_TYPE_GUARDIAN))
					.map(GatewayPlantEntity::getGatewayUniqueNumber)
					.collect(Collectors.toList()));
				vo.setBackUpBox(Optional.ofNullable(gatewayPlantEntities).orElse(new ArrayList<>()).stream()
					.filter(gatewayPlantEntity -> gatewayPlantEntity.getDeviceType().equals(BizConstant.CLIENT_IMPORTANT_EVENT_TYPE_BACKUP_BOX))
					.map(GatewayPlantEntity::getGatewayUniqueNumber)
					.collect(Collectors.toList()));
			}
		}

			return result;
	}

	@TenantIgnore
	@Override
	public IPage<PlantVO> selectPlantPage(IPage<PlantVO> page, PlantVO plant,  String userType) {
		return page.setRecords(baseMapper.selectPlantPage(page, plant, userType));
	}


	@Override
	public List<PlantExcel> exportPlant(Wrapper<PlantEntity> queryWrapper) {
		return baseMapper.exportPlant(queryWrapper);
	}

	@Override
	public PlantVO view(PlantEntity plantEntity, String language) {
		PlantEntity plant = super.getOne(Condition.getQueryWrapper(plantEntity));
		if (ObjectUtil.isEmpty(plant)) {
			plant = baseMapper.queryPlantIsDelete(plantEntity.getId());
		}
		if (plant == null) {
			return null;
		}
		PlantVO plantVO = PlantWrapper.build().entityVO(plant);
		if (StringUtil.isNotBlank(plant.getCountryCode())) {
			plantVO.setCountryName(queryRegionName(plant.getCountryCode(), language));
		}
		if (StringUtil.isNotBlank(plant.getProvinceCode())) {
			plantVO.setProvinceName(queryRegionName(plant.getProvinceCode(), language));
		}
		if (StringUtil.isNotBlank(plant.getCityCode())) {
			plantVO.setCityName(queryRegionName(plant.getCityCode(), language));
		}

		// 增加用户手机号 以及用户名
		Long createdBy = plantVO.getCreateUser();
		if (ValidationUtil.isNotEmpty(createdBy)) {
			User user = userClient.userInfoById(createdBy).getData();
			if (cn.hutool.core.util.ObjectUtil.isNotNull(user)) {
				plantVO.setUserPhoneAndAreaCode(user.getPhoneDiallingCode() + " " + user.getPhone());
				plantVO.setCreatedByName(user.getRealName());
			}
		}

		// 该接口已经不需要这个字段
		//if (ValidationUtil.isNotEmpty(plantEntity.getBatterySerialNumber())){
		//	BatteryMapDeviceEntity mapDeviceServiceOne = batteryMapDeviceService.getOne(Wrappers.<BatteryMapDeviceEntity>lambdaQuery()
		//		.eq(BatteryMapDeviceEntity::getPlantId, plantEntity.getId())
		//		.eq(BatteryMapDeviceEntity::getBatterySerialNumber, plantVO.getBatterySerialNumber()));
		//	if (ObjectUtil.isNotEmpty(mapDeviceServiceOne) && ValidationUtil.isNotEmpty(mapDeviceServiceOne.getBatteryEnergyStorageNumber())){
		//		plantVO.setBatteryEnergyStorageNumber(mapDeviceServiceOne.getBatteryEnergyStorageNumber());
		//	}
		//}

		return plantVO;
	}

	@Override
	public int updateStatusById(long id, String status, Integer existUserTypeAlarm, Integer existAgentTypeAlarm) {
		return baseMapper.updateStatusById(id, status, existUserTypeAlarm, existAgentTypeAlarm);
	}

	/**
	 * 查询区域信息
	 *
	 * @param code 入参
	 * @return String
	 * <AUTHOR>
	 * @since 2023/9/20 16:46
	 **/
	private String queryRegionName(String code, String language) {
		Region region = RegionCache.getByCode(code);
		if (region == null) {
			return Strings.EMPTY;
		}
		return CommonConstant.CURRENT_LANGUAGE_EN.equalsIgnoreCase(language) ? region.getNameEn() : region.getName();
	}

	@Override
	public int updatePlant(PlantEntity plantEntity) {
		return baseMapper.updatePlant(plantEntity);
	}

	@Override
	public PlantVO getPlantDetail(PlantEntity plantEntity) {
		PlantEntity detail = getOne(Condition.getQueryWrapper(plantEntity));
		// 判断是否为空
		if (ObjectUtil.isEmpty(detail)) {
			return null;
		}
		PlantVO plantVO = PlantWrapper.build().entityVO(detail);

		// 返回用户手机号以及用户名
		Long createUser = plantVO.getCreateUser();
		if (ValidationUtil.isNotEmpty(createUser)) {
			User user = userClient.userInfoById(createUser).getData();
			if (ObjectUtil.isNotEmpty(user)) {
				plantVO.setPhone(user.getPhone() == null ? "" : user.getPhone());
				plantVO.setPhoneDiallingCode(user.getPhoneDiallingCode() == null ? "" : user.getPhoneDiallingCode());
				plantVO.setCreatedByName(user.getRealName() == null ? "" : user.getRealName());
			}
		}

		// 返回运维团队名称和运维人员名称
		if (ValidationUtil.isNotEmpty(plantVO.getOperationUserId())) {
			List<User> userList = userSearchClient.listByUserIds(Collections.singletonList(plantVO.getOperationUserId())).getData();
			if (!CollectionUtils.isNullOrEmpty(userList)) {
				plantVO.setAgentUserId(userList.get(0).getId());
				plantVO.setAgentUserName(userList.get(0).getRealName());
			}
		}

		// 返回安装团队id和名称
		if (ValidationUtil.isNotEmpty(plantVO.getOperationCompanyId())) {
			List<AgentCompanyVO> agentCompanyList = agentClient.agentCompanyInfoByIds(Collections.singletonList(plantVO.getOperationCompanyId())).getData();
			if (!CollectionUtils.isNullOrEmpty(agentCompanyList)) {
				plantVO.setAgentId(agentCompanyList.get(0).getId());
				plantVO.setOperationCompanyDeptId(agentCompanyList.get(0).getDeptId());
				plantVO.setAgentCompanyName(agentCompanyList.get(0).getCompanyName());
			}
		}
		if (ValidationUtil.isNotEmpty(plantVO.getInstallTeamId())) {
			List<AgentCompanyVO> agentCompanyList = agentClient.agentCompanyInfoByIds(Collections.singletonList(plantVO.getInstallTeamId())).getData();
			if (!CollectionUtils.isNullOrEmpty(agentCompanyList)) {
				plantVO.setInstallTeamId(agentCompanyList.get(0).getDeptId());
				plantVO.setInstallTeam(agentCompanyList.get(0).getCompanyName());
			}
		}
		List<WifiStickPlantEntity> wifiStickPlantVOList = wifiStickPlantService.queryDeviceSerialNumberList(Collections.singletonList(plantEntity.getId()));
		Map<String, Integer> deviceModeList = wifiStickPlantVOList.stream().collect(Collectors.toMap(WifiStickPlantEntity::getDeviceSerialNumber,
			entity -> Optional.ofNullable(entity.getParallelDeviceType()).orElse(BizConstant.CLIENT_INVERTER_MASTER_MODEL)));
		// 提取设备序列号
		List<String> serialNumbers = wifiStickPlantVOList.stream()
			.map(WifiStickPlantEntity::getDeviceSerialNumber)
			.collect(Collectors.toList());
		//根据sn列表查出出厂信息数据列表
		List<DeviceExitFactoryInfoEntity> deviceExitFactoryInfoEntityList = deviceExitFactoryInfoService.getListByDeviceSerialNumberCollect(serialNumbers);
		// key是sn，value是型号
		Map<String, String> deviceExitCollect = deviceExitFactoryInfoEntityList.stream().collect(Collectors.toMap(DeviceExitFactoryInfoEntity::getDeviceSerialNumber, DeviceExitFactoryInfoEntity::getDeviceType));

		Device21Entity device21Entity = new Device21Entity();
		device21Entity.setPlantId(plantEntity.getId());
		List<Device21Entity> list = device21Service.list(Condition.getQueryWrapper(device21Entity));
		JSONObject obj=new JSONObject();
		if (!CollectionUtils.isNullOrEmpty(list)){
			for (Device21Entity entity : list) {
				String deviceSerialNumber = entity.getDeviceSerialNumber();
				if (deviceExitCollect.containsKey(deviceSerialNumber)) {
					//从字典根据出厂型号查出 inverterKind
					String value = DictBizCache.getValue(BizConstant.INVERT_KIND_DICT_CODE, deviceExitCollect.get(deviceSerialNumber));
					//主机
					if (ValidationUtil.isNotEmpty(deviceModeList.get(deviceSerialNumber)) && deviceModeList.get(deviceSerialNumber) == 1) {
						obj.put("mainInverterKind", value);
					} else {
						obj.put("subInverterKind", value);
					}
				}
			}
		}

		List<DeviceExitFactoryInfoVO> deviceExitFactoryInfoVoS = deviceExitFactoryInfoService.queryDeviceExitFactoryByPlant(plantEntity.getId());
		double deviceSum = deviceExitFactoryInfoVoS.stream().filter(p -> StringUtil.isNotBlank(p.getPower())).mapToDouble(p -> Double.parseDouble(p.getPower())).sum();
		// 原始数据为 kw
		plantVO.setDevicePower(DataUnitConversionUtil.getChangeEnergyResultK(new BigDecimal(String.valueOf(deviceSum * 1000)), 2));
		List<AppBatteryExitFactoryInfoVO> appBatteryExitFactoryInfoVoS = batteryExitFactoryInfoService.queryAppBatteryExitFactoryInfo(plantEntity.getId());
		double batterySum = appBatteryExitFactoryInfoVoS.stream().filter(p -> StringUtil.isNotBlank(p.getRatedBatteryEnergy())).mapToDouble(p -> Double.parseDouble(p.getRatedBatteryEnergy())).sum();
		// 原始数据为 kwh
		plantVO.setEnergyBatteryPower(DataUnitConversionUtil.getChangeEnergyResultK(new BigDecimal(String.valueOf(batterySum * 1000)), 1));
		plantVO.setBatteryNumber(appBatteryExitFactoryInfoVoS.size());

		// 如果安装时间为空，则使用电站创建时间来作为安装时间
		if (ObjectUtil.isEmpty(plantVO.getInstallDate())) {
			plantVO.setInstallDate(plantVO.getCreateTime());
		}

		// 运行时间计算
		if (ObjectUtil.isNotEmpty(plantVO.getInstallDate())) {
			LocalDate localDate = plantVO.getInstallDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
			LocalDate currentDate = LocalDate.now();
			long daysDiff = ChronoUnit.DAYS.between(localDate, currentDate);
			plantVO.setRunDay(daysDiff);
		}

//		// 安全卫士总数查询
//		List<GatewayPlantEntity> guardianList = gatewayPlantService.list(Wrappers.<GatewayPlantEntity>lambdaQuery()
//			.eq(GatewayPlantEntity::getPlantId, plantVO.getId())
//			.eq(GatewayPlantEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED));
//		plantVO.setGuardianCount(CollectionUtil.isNotEmpty(guardianList) ? guardianList.size() : 0);

		return plantVO;
	}

	@Override
	public List<JSONObject> queryPlantStatusCount() {
		return baseMapper.queryPlantStatusCount();
	}

	@Override
	public R<List<PlantClientVO>> getPlantByUserInfo(PlantClientVO vo) {
		String currentLanguage = CommonUtil.getCurrentLanguage();
		BladeUser user = AuthUtil.getUser();
		User queryUser = new User();
		queryUser.setTelephoneOrUserName(vo.getTelephoneOrUserName());
		// 设备端对应的租户
		queryUser.setTenantId(CommonConstant.CLIENT_TENANT_ID);
		R<List<User>> userR = userClient.getUserList(queryUser);
		R r = new R();
		if (userR.getCode() != CommonConstant.REST_FUL_RESULT_SUCCESS || CollectionUtil.isEmpty(userR.getData())) {
			r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100031.getCode());
			r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100031.autoGetMessage(currentLanguage));
			return r;
		}
		List<User> data = userR.getData();
		List<Long> userIdList = data.stream().map(User::getId).collect(Collectors.toList());
		PlantEntity query = new PlantEntity();
		query.setCreateUserList(userIdList);
		List<PlantEntity> plantEntities = baseMapper.queryPlant(query);
		if (plantEntities == null || plantEntities.isEmpty()) {
			r.setCode(I18nMsgCode.SKYWORTH_CLIENT_PLANT_100032.getCode());
			r.setMsg(I18nMsgCode.SKYWORTH_CLIENT_PLANT_100032.autoGetMessage(currentLanguage));
			return r;
		}
		List<PlantClientVO> result = new ArrayList<>();
		for (PlantEntity entity : plantEntities) {
			PlantClientVO plantClientVO = new PlantClientVO();
			plantClientVO.setPlantId(entity.getId());
			plantClientVO.setPlantName(entity.getPlantName());
			result.add(plantClientVO);
		}
		return R.data(result);
	}

	@Override
	public R submitOperationInfo(PlantOperationInfoVO vo) {
		String currentLanguage = CommonUtil.getCurrentLanguage();
		// 判断站点是否存在
		PlantEntity plantEntity = baseMapper.selectById(vo.getPlantId());
		// 站点不存在, 返回错误信息
		if (ObjectUtil.isEmpty(plantEntity)) {
			R r = new R();
			r.setCode(I18nMsgCode.SKYWORTH_CLIENT_PLANT_100120.getCode());
			r.setMsg(I18nMsgCode.SKYWORTH_CLIENT_PLANT_100120.autoGetMessage(currentLanguage));
			return r;
		}

		// 运维团队和运维人员必须全部存在或者全部不存在
		if (ObjectUtil.isEmpty(vo.getOperationCompanyDeptId()) && ObjectUtil.isNotEmpty(vo.getOperationUserId())) {
			R r = new R();
			r.setCode(I18nMsgCode.SKYWORTH_CLIENT_PLANT_100121.getCode());
			r.setMsg(I18nMsgCode.SKYWORTH_CLIENT_PLANT_100121.autoGetMessage(currentLanguage));
			return r;
		} else if (ObjectUtil.isNotEmpty(vo.getOperationCompanyDeptId()) && ObjectUtil.isEmpty(vo.getOperationUserId())) {
			R r = new R();
			r.setCode(I18nMsgCode.SKYWORTH_CLIENT_PLANT_100122.getCode());
			r.setMsg(I18nMsgCode.SKYWORTH_CLIENT_PLANT_100122.autoGetMessage(currentLanguage));
			return r;
		}

		Long operationCompanyIdOld = plantEntity.getOperationCompanyId();

		// 安装信息
		plantEntity.setInstallDate(vo.getInstallDate());
		plantEntity.setInstallTeamId(vo.getInstallTeamId());
		plantEntity.setOperationCompanyId(vo.getOperationCompanyDeptId());
		plantEntity.setOperationUserId(vo.getOperationUserId());

		// 判断数据库中运维团队和传入的运维团队是否一致
		if (ObjectUtil.isNotEmpty(operationCompanyIdOld) && !Objects.equals(operationCompanyIdOld, vo.getOperationCompanyDeptId())) {
			agentUnauthorizedUserService.removeByPlantId(vo.getPlantId());
		}

		// 更新站点信息
		plantEntity.setUpdateTime(DateUtil.now());
		int updateById = baseMapper.updateById(plantEntity);

		return R.status(updateById > 0);

	}

	/**
	 * 获取代理商人员信息
	 */
	private void getAgentUserInfo(List<PlantVO> plantVOList, Map<Long, AgentUserVo> agentUserVoMap) {
		List<Long> userIds = plantVOList.stream().map(PlantVO::getOperationUserId)
			.filter(ObjectUtil::isNotEmpty).collect(Collectors.toList());
		List<User> userList = userSearchClient.listByUserIds(userIds).getData();
		if (!CollectionUtils.isNullOrEmpty(userList)) {
			userList.parallelStream().forEach(v -> {
				AgentUserVo agentUserVo = new AgentUserVo();
				BeanUtils.copyProperties(v, agentUserVo);
				agentUserVoMap.put(v.getId(), agentUserVo);
			});
		}
	}

	/**
	 * 获取代理商公司信息
	 */
	private void getCompanyInfo(List<PlantVO> plantVOList, Map<Long, AgentCompanyVO> companyVoMap) {
		List<Long> companyIds = plantVOList.stream().map(PlantVO::getOperationCompanyId)
			.filter(ObjectUtil::isNotEmpty).collect(Collectors.toList());
		List<AgentCompanyVO> agentCompanyVOList = agentClient.agentCompanyInfoByIds(companyIds).getData();
		if (!CollectionUtils.isNullOrEmpty(agentCompanyVOList)) {
			agentCompanyVOList.parallelStream().forEach(v -> {
				AgentCompanyVO agentCompanyVO = new AgentCompanyVO();
				BeanUtils.copyProperties(v, agentCompanyVO);
				companyVoMap.put(v.getDeptId(), agentCompanyVO);
			});
		}
	}

	@Override
	public IPage<PlantAgentViewVO> getPlantAgentViewInfo(Long deptId, Query query) {
		IPage<PlantAgentViewVO> page = Condition.getPage(query);
		List<PlantAgentViewVO> plantAgentViewVOList = baseMapper.getPlantAgentViewInfo(deptId, page);
		Map<String, String> regionMap = queryPlantAddress(plantAgentViewVOList);
		Map<Long, User> userMap = queryUserInfo(plantAgentViewVOList);
		for (PlantAgentViewVO plant : plantAgentViewVOList) {
			StringBuilder addressBuilder = new StringBuilder();
			if (StringUtil.isNotBlank(plant.getCountryCode())) {
				addressBuilder.append(regionMap.get(plant.getCountryCode())).append(" ");
			}
			if (StringUtil.isNotBlank(plant.getProvinceCode())) {
				addressBuilder.append(regionMap.get(plant.getProvinceCode())).append(" ");
			}
			if (StringUtil.isNotBlank(plant.getCityCode())) {
				addressBuilder.append(regionMap.get(plant.getCityCode())).append(" ");
			}
			if (StringUtil.isNotBlank(plant.getDetailAddress())) {
				addressBuilder.append(plant.getDetailAddress());
			}
			plant.setDetailAddress(addressBuilder.length() > 0 ? addressBuilder.toString() : "");
			User user = userMap.get(plant.getCreateUser());
			if (user != null) {
				plant.setUserName(user.getRealName());
				plant.setUserPhone(user.getPhone());
			}
		}
		page.setRecords(plantAgentViewVOList);
		return page;
	}

	private Map<Long, User> queryUserInfo(List<PlantAgentViewVO> plantAgentViewVOList) {
		if (CollectionUtil.isEmpty(plantAgentViewVOList)) {
			return new HashMap<>(0);
		}
		List<User> userList = userSearchClient.listAllByUserIds(plantAgentViewVOList.stream().map(PlantAgentViewVO::getCreateUser).collect(Collectors.toList())).getData();
		return Optional.ofNullable(userList).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(User::getId, Function.identity(), (a, b) -> a));
	}

	private Map<String, String> queryPlantAddress(List<PlantAgentViewVO> plantAgentViewVOList) {
		Map<String, String> regionMap = new HashMap<>();
		if (CollectionUtil.isEmpty(plantAgentViewVOList)) {
			return regionMap;
		}
		List<String> regionCodeList = new ArrayList<>();
		for (PlantAgentViewVO plant : plantAgentViewVOList) {
			if (StringUtil.isNotBlank(plant.getCountryCode())) {
				regionCodeList.add(plant.getCountryCode());
			}
			if (StringUtil.isNotBlank(plant.getProvinceCode())) {
				regionCodeList.add(plant.getProvinceCode());
			}
			if (StringUtil.isNotBlank(plant.getCityCode())) {
				regionCodeList.add(plant.getCityCode());
			}
		}
		if (CollectionUtil.isNotEmpty(regionCodeList)) {
			List<Region> regionList = sysClient.getRegionList(regionCodeList).getData();
			regionMap = Optional.ofNullable(regionList).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(Region::getCode, Region::getName, (a, b) -> a));
		}
		return regionMap;
	}

	@Override
	public Boolean cleanPlantOperationUserId(List<Long> userIdList) {
		return SqlHelper.retBool(baseMapper.cleanPlantOperationUserId(userIdList));
	}

	@Override
	public Boolean cleanPlantDeptIdOrUsers(JSONObject jsonObject) {
		Integer type = jsonObject.getInteger("type");
		String deptIds = jsonObject.getString("deptIds");
		// 删除代理商用户的同时，删除电站运维人员
		if (BizConstant.NUMBER_ZERO.equals(type)) {
			String userIds = jsonObject.getString("sourceUserIds");
			List<Long> userList = Arrays.stream(userIds.split(","))
				.map(Long::parseLong)
				.collect(Collectors.toList());
			List<User> userMappingList = userSearchClient.listByMappingUser(userList).getData();
			if (CollectionUtil.isNotEmpty(userMappingList) && StringUtil.isNotBlank(deptIds)) {
				return SqlHelper.retBool(baseMapper.cleanPlantOperationUser(deptIds, userMappingList.stream().map(User::getId).collect(Collectors.toList())));
			}
		}
		// 删除代理商，需要删除电站的运维人员和运维团队
		else if (BizConstant.NUMBER_ONE.equals(type)) {
			return SqlHelper.retBool(baseMapper.cleanPlantDeptIdAndOperationUser(deptIds));
		}
		return true;
	}
	@Override
	public AppPlantCountVO queryPlantCount(PlantVO plant, String userType) {
		return baseMapper.queryPlantCount(plant,userType);
	}
}
