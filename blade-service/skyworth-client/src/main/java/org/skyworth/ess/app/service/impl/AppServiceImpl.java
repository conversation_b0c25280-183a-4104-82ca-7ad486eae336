package org.skyworth.ess.app.service.impl;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.skyworth.ess.app.service.IAppService;
import org.skyworth.ess.app.vo.*;
import org.skyworth.ess.balconypv.entity.EnergySystemEntity;
import org.skyworth.ess.balconypv.service.IEnergySystemService;
import org.skyworth.ess.battery.entity.BatteryCurrentStatusEntity;
import org.skyworth.ess.battery.entity.BatteryExitFactoryInfoEntity;
import org.skyworth.ess.battery.entity.BatteryMapDeviceEntity;
import org.skyworth.ess.battery.entity.QueryCondition;
import org.skyworth.ess.battery.service.IBatteryCurrentStatusService;
import org.skyworth.ess.battery.service.IBatteryEverydayTotalService;
import org.skyworth.ess.battery.service.IBatteryExitFactoryInfoService;
import org.skyworth.ess.battery.service.IBatteryMapDeviceService;
import org.skyworth.ess.battery.service.impl.BatteryExitFactoryInfoServiceImpl;
import org.skyworth.ess.battery.vo.BatteryEverydayTotalVO;
import org.skyworth.ess.battery.vo.BatteryMapDeviceVO;
import org.skyworth.ess.dailyStatistics.entity.QueryDeviceLog22Condition;
import org.skyworth.ess.dailyStatistics.service.DeviceLog22ByDorisService;
import org.skyworth.ess.dailyStatistics.vo.DeviceLog22VO;
import org.skyworth.ess.device.client.IDeviceIssueBiz;
import org.skyworth.ess.device.entity.*;
import org.skyworth.ess.device.service.*;
import org.skyworth.ess.event.entity.ImportantEventEntity;
import org.skyworth.ess.event.service.IImportantEventService;
import org.skyworth.ess.feign.IAgentClient;
import org.skyworth.ess.guardian.alarmthreshold.entity.GuardianAlarmThresholdEntity;
import org.skyworth.ess.guardian.exitfactory.entity.GuardianExitFactoryInfoEntity;
import org.skyworth.ess.guardian.exitfactory.service.IGuardianExitFactoryInfoService;
import org.skyworth.ess.guardian.guardianplant.entity.GuardianPlantEntity;
import org.skyworth.ess.guardian.guardianplant.service.IGuardianPlantService;
import org.skyworth.ess.guardian.realTimeHandler.AlarmThresholdServiceImpl;
import org.skyworth.ess.guardian.realTimeHandler.PowerSettingServiceImpl;
import org.skyworth.ess.guardian.thresholdcurrentstatus.entity.GuardianThresholdCurrentStatusEntity;
import org.skyworth.ess.guardian.thresholdcurrentstatus.service.IGuardianThresholdCurrentStatusService;
import org.skyworth.ess.guardian.timedcurrentstatus.entity.GuardianTimedCurrentStatusEntity;
import org.skyworth.ess.guardian.timedcurrentstatus.service.IGuardianTimedCurrentStatusService;
import org.skyworth.ess.lazzen.gatewayplant.entity.GatewayPlantEntity;
import org.skyworth.ess.lazzen.gatewayplant.service.IGatewayPlantService;
import org.skyworth.ess.lazzen.productinfo.entity.DeviceGuardProductInfoEntity;
import org.skyworth.ess.lazzen.productinfo.service.IDeviceGuardProductInfoService;
import org.skyworth.ess.ota.entity.OtaUpdatePackEntity;
import org.skyworth.ess.ota.service.IOtaUpdatePackService;
import org.skyworth.ess.plant.entity.PlantEntity;
import org.skyworth.ess.plant.entity.WifiStickPlantEntity;
import org.skyworth.ess.plant.service.IPlantService;
import org.skyworth.ess.plant.service.IWifiStickPlantService;
import org.skyworth.ess.plant.vo.PlantDetailVO;
import org.skyworth.ess.plant.vo.PlantListVO;
import org.skyworth.ess.plant.vo.PlantVO;
import org.skyworth.ess.plant.vo.WifiStickPlantVO;
import org.skyworth.ess.util.SnowFlakeGeneratorUtil;
import org.skyworth.ess.vo.AgentCompanyVO;
import org.skyworth.ess.vo.AgentUserVo;
import org.springblade.common.cache.CacheNames;
import org.springblade.common.cache.EmailCacheNamesEnum;
import org.springblade.common.constant.*;
import org.springblade.common.utils.CommonUtil;
import org.springblade.common.utils.DataUnitConversionUtil;
import org.springblade.common.utils.StatusDisplayUtil;
import org.springblade.common.utils.tool.BeanUtils;
import org.springblade.common.utils.tool.StringUtils;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.*;
import org.springblade.system.cache.DictBizCache;
import org.springblade.system.constant.DictConstant;
import org.springblade.system.entity.AttachmentInfoEntity;
import org.springblade.system.entity.DictBiz;
import org.springblade.system.entity.Region;
import org.springblade.system.entity.User;
import org.springblade.system.feign.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service

@Slf4j
public class AppServiceImpl implements IAppService {

	@Resource
	private IPlantService plantService;

	@Resource
	private IImportantEventService importantEventService;

	@Resource
	private BladeRedis bladeRedis;
	@Resource
	private IWifiStickPlantService wifiStickPlantService;
	@Resource
	private IDictBizClient dictBizClient;
	@Resource
	private IBatteryExitFactoryInfoService batteryExitFactoryInfoService;
	@Resource
	private IDevice21Service device21Service;
	@Resource
	private IBatteryMapDeviceService batteryMapDeviceService;
	@Resource
	private IDeviceExitFactoryInfoService deviceExitFactoryInfoService;
	@Resource
	private IBatteryCurrentStatusService batteryCurrentStatusService;
	@Resource
	private IBatteryEverydayTotalService batteryEverydayTotalService;
	@Resource
	private IDevice23Service device23Service;
	@Resource
	private IOtaUpdatePackService otaUpdatePackService;
	@Resource
	private IAttachmentInfoClient attachmentInfoService;
	@Resource

	private DeviceLog22ByDorisService deviceLog22ByDorisService;
	@Resource
	private IDeviceCurrentStatusService deviceCurrentStatusService;
	@Resource
	private ISysClient sysClient;
	@Resource
	private IDevice24Service device24Service;
	@Resource
	private IUserClient userClient;

	@Resource
	private IDeviceIssueBiz deviceIssueBiz;
	@Resource
	private AppReportServiceImpl appReportServiceImpl;

	@Resource
	private IAgentClient agentClient;

	@Resource
	private IUserSearchClient userSearchClient;

	@Resource
	private IGuardianExitFactoryInfoService guardianExitFactoryInfoService;
	@Resource
	private IGuardianPlantService guardianPlantService;
	@Resource
	private IGuardianTimedCurrentStatusService guardianTimedCurrentStatusService;
	@Resource
	private IGuardianThresholdCurrentStatusService guardianThresholdCurrentStatusService;
	@Resource
	private IGatewayPlantService gatewayPlantService;
	@Resource
	private AlarmThresholdServiceImpl alarmThresholdService;

	@Resource
	private PowerSettingServiceImpl powerSettingService;
	@Resource
	private IDeviceCustomModeService deviceCustomModeService;
	@Resource
	private IDeviceGuardProductInfoService deviceGuardProductInfoService;
	@Resource
	private BusinessServiceUtil businessServiceUtil;
	@Resource
	private IEnergySystemService energySystemService;

	@Override
	public String addPlant(PlantEntity plant) {
		BladeUser user = AuthUtil.getUser();
		plant.setCreateUserAccount(user.getAccount());
		plant.setCreateUser(user.getUserId());
		plant.setInstallDate(new Date());
		plant.setPlantStatus(BizConstant.CHAR_ZERO);
		plantService.save(plant);
		ImportantEventEntity importantEventEntity = new ImportantEventEntity();
		importantEventEntity.setEventType(BizConstant.CLIENT_IMPORTANT_EVENT_TYPE_PLANT);
		importantEventEntity.setPlantId(plant.getId());
		importantEventEntity.setEventContent("client.guardian.important.event.add.plant");
		importantEventEntity.setEventDate(new Date());
		importantEventEntity.setCreateUserAccount(user.getAccount());
		importantEventEntity.setCreateUser(user.getUserId());
		importantEventService.save(importantEventEntity);
		return StrUtil.toString(plant.getId());
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Long addWifiDongle(WifiStickPlantEntity wifiStickPlantEntity) {
		//Boolean isParallelFlag = false;
		//默认为主机
		wifiStickPlantEntity.setParallelDeviceType(BizConstant.CLIENT_INVERTER_MASTER_MODEL);
		PlantEntity byId = plantService.getById(wifiStickPlantEntity.getPlantId());
		if (byId == null) {
			return -200L;
		}
		QueryWrapper<DeviceExitFactoryInfoEntity> queryWrapper = new QueryWrapper<>();
		queryWrapper.eq(DatabaseFieldConstant.DEVICE_SERIAL_NUMBER, wifiStickPlantEntity.getDeviceSerialNumber());
		List<DeviceExitFactoryInfoEntity> list = deviceExitFactoryInfoService.list(queryWrapper);
		if (CollectionUtils.isEmpty(list)) {
			return -100L;
		}
		DeviceExitFactoryInfoEntity newInverterInfo = list.get(0);
		// 根据站点id，查出站点下的所有智能能量变换器以及它的型号，如果新添加的智能能量变换器型号与旧的智能能量变换器不同，则不能作为并机进行添加
		WifiStickPlantEntity queryWifi = new WifiStickPlantEntity();
		queryWifi.setPlantId(wifiStickPlantEntity.getPlantId());
		List<WifiStickPlantEntity> queryWifiList = wifiStickPlantService.list(Condition.getQueryWrapper(queryWifi));
		if (queryWifiList.size() == BizConstant.CLIENT_INVERTER_MAX_PARALLEL) {
			return -300L;
		}
		if (queryWifiList.size() == 1) {
			wifiStickPlantEntity.setParallelDeviceType(BizConstant.CLIENT_INVERTER_SLAVE_MODEL);
			WifiStickPlantEntity wifiStickPlant = queryWifiList.get(0);
			DeviceExitFactoryInfoEntity queryDeviceExit = new DeviceExitFactoryInfoEntity();
			queryDeviceExit.setDeviceSerialNumber(wifiStickPlant.getDeviceSerialNumber());
			DeviceExitFactoryInfoEntity oldInverterInfo =
				deviceExitFactoryInfoService.getOne(Condition.getQueryWrapper(queryDeviceExit));
			if (!oldInverterInfo.getDeviceType().equals(newInverterInfo.getDeviceType())) {
				return -400L;
			}
		}
		if (queryWifiList.isEmpty()) {
			String deviceType = newInverterInfo.getDeviceType();
			String value = DictBizCache.getValue(BizConstant.CLIENT_INVERTER_MODEL_SUPPORT_PARALLEL, deviceType);
			//设备支持并机模式，且为第一个添加的智能能量变换器，则设置为主机
			if (ValidationUtil.isNotEmpty(value)) {
				wifiStickPlantEntity.setParallelDeviceType(BizConstant.CLIENT_INVERTER_MASTER_MODEL);
			}
		}

		BladeUser user = AuthUtil.getUser();
		// 如果未扫描过智能能量变换器，则保存
		wifiStickPlantEntity.setCreateUserAccount(user.getAccount());
		wifiStickPlantEntity.setCreateUser(user.getUserId());
		wifiStickPlantEntity.setWifiStickStatus(BizConstant.CLIENT_WIFI_STICK_STATUS_OFFLINE);
		wifiStickPlantService.save(wifiStickPlantEntity);
		PlantEntity updatePlantEntity = new PlantEntity();
		updatePlantEntity.setId(wifiStickPlantEntity.getPlantId());
		updatePlantEntity.setDeviceNumber(1);
		updatePlantEntity.setUpdateTime(new Date());
		updatePlantEntity.setUpdateUserAccount(user.getAccount());
		updatePlantEntity.setUpdateUser(user.getUserId());

		plantService.updatePlant(updatePlantEntity);
		PlantEntity plantById = plantService.getById(wifiStickPlantEntity.getPlantId());
		this.saveImportant(wifiStickPlantEntity, user, plantById);

		// 保存重要事件
		this.saveImportant(wifiStickPlantEntity, user, plantById);
		//激活日期
		String activationDate = LocalDate.now().format(BatteryExitFactoryInfoServiceImpl.FORMATTER);
		// 如果未设置质保截止日期，则代表没有激活过，此时用质保开始时间+质保年限作为质保截止日期。
		if (StringUtils.isEmpty(newInverterInfo.getWarrantyDeadline())) {
			LocalDate date = LocalDate.parse(activationDate);
			LocalDate warrantyEndDate = date.plusYears(Long.parseLong(newInverterInfo.getQualityGuaranteeYear()));
			newInverterInfo.setWarrantyDeadline(warrantyEndDate.toString());
		}
		LambdaUpdateWrapper<DeviceExitFactoryInfoEntity> update =
			Wrappers.<DeviceExitFactoryInfoEntity>update().lambda()
				.set(DeviceExitFactoryInfoEntity::getStatus, 1)
				.set(DeviceExitFactoryInfoEntity::getUpdateUserAccount, user.getAccount())
				.set(DeviceExitFactoryInfoEntity::getUpdateUser, user.getUserId())
				.set(DeviceExitFactoryInfoEntity::getWarrantyDeadline, newInverterInfo.getWarrantyDeadline())
				.eq(DeviceExitFactoryInfoEntity::getId, newInverterInfo.getId());
		if (StringUtils.isEmpty(newInverterInfo.getActivationDate())) {
			update.set(DeviceExitFactoryInfoEntity::getActivationDate, activationDate);
		}
		if (StringUtils.isEmpty(newInverterInfo.getWarrantyStartDate())) {
			update.set(DeviceExitFactoryInfoEntity::getWarrantyStartDate, activationDate);
		}
		deviceExitFactoryInfoService.update(update);
		return wifiStickPlantEntity.getId();
	}

	private String getAllAddressByPlant(PlantEntity plantById) {
		// 增加详细地址
		List<String> regionCodeList = new ArrayList<>();
		regionCodeList.add(plantById.getCountryCode());
		regionCodeList.add(plantById.getProvinceCode());
		regionCodeList.add(plantById.getCityCode());
		regionCodeList.add(plantById.getCountyCode());
		return buildDeviceAllAddress(regionCodeList, plantById);
	}

	private String buildDeviceAllAddress(List<String> regionCodeList, PlantEntity plantEntity) {
		List<String> regionCodeNotNullList =
			regionCodeList.stream().filter(StringUtil::isNotBlank).collect(Collectors.toList());
		if (CollectionUtil.isNotEmpty(regionCodeNotNullList)) {
			StringBuilder address = new StringBuilder(plantEntity.getDetailAddress() == null ? "" :
				plantEntity.getDetailAddress() + " ");
			List<Region> regionList = sysClient.getRegionList(regionCodeNotNullList).getData();
			Collections.reverse(regionList);
			if (CollectionUtils.isEmpty(regionList)) {
				return "";
			}
			for (Region region : regionList) {
				if (region.getCode().equalsIgnoreCase(plantEntity.getCountryCode())) {
					address.append(region.getName()).append(" ");
				}
				if (region.getCode().equalsIgnoreCase(plantEntity.getProvinceCode())) {
					address.append(region.getName()).append(" ");
				}
				if (region.getCode().equalsIgnoreCase(plantEntity.getCityCode())) {
					address.append(region.getName()).append(" ");
				}
				if (region.getCode().equalsIgnoreCase(plantEntity.getCountyCode())) {
					address.append(region.getName()).append(" ");
				}
			}
			return address.toString();
		}
		return CommonConstant.BLANK;
	}

	private void saveImportant(WifiStickPlantEntity wifiStickPlantEntity, BladeUser user, PlantEntity plant) {
		List<ImportantEventEntity> importantEventEntityArrayList = new ArrayList<>();

		String plantAllAddress = this.getAllAddressByPlant(plant);
		ImportantEventEntity inverterCreateImportEvent = new ImportantEventEntity();
		inverterCreateImportEvent.setPlantId(wifiStickPlantEntity.getPlantId());
		inverterCreateImportEvent.setEventType(BizConstant.CLIENT_IMPORTANT_EVENT_TYPE_INVERTER);
		inverterCreateImportEvent.setEventContent("client.guardian.important.event.add.inverter");
		inverterCreateImportEvent.setEventRemark("Create User : " + user.getUserName() +
			",Energy Station Name : " + plant.getPlantName() + ",Energy Station Address : " + plantAllAddress + "," +
			"Inverter SN : " + wifiStickPlantEntity.getDeviceSerialNumber());
		inverterCreateImportEvent.setSerialNumber(wifiStickPlantEntity.getDeviceSerialNumber());
		inverterCreateImportEvent.setEventDate(new Date());
		inverterCreateImportEvent.setCreateUserAccount(user.getAccount());
		inverterCreateImportEvent.setCreateUser(user.getUserId());
		importantEventEntityArrayList.add(inverterCreateImportEvent);


		DeviceExitFactoryInfoEntity deviceExitFactoryInfoEntity = new DeviceExitFactoryInfoEntity();
		deviceExitFactoryInfoEntity.setDeviceSerialNumber(wifiStickPlantEntity.getDeviceSerialNumber());
		DeviceExitFactoryInfoEntity factoryInfoEntity =
			deviceExitFactoryInfoService.getOne(Condition.getQueryWrapper(deviceExitFactoryInfoEntity));
		ImportantEventEntity inverterExitImportEvent = new ImportantEventEntity();
		inverterExitImportEvent.setEventType(BizConstant.CLIENT_IMPORTANT_EVENT_TYPE_INVERTER);
		inverterExitImportEvent.setPlantId(wifiStickPlantEntity.getPlantId());
		inverterExitImportEvent.setSerialNumber(factoryInfoEntity.getDeviceSerialNumber());
		inverterExitImportEvent.setEventDate(factoryInfoEntity.getExitFactoryDate());
		inverterExitImportEvent.setEventContent("client.guardian.important.event.add.inverter.factory");
		inverterExitImportEvent.setEventRemark("Manufacturer : " + factoryInfoEntity.getCompany() + ",Warranty " +
			"period" +
			" " +
			": " + factoryInfoEntity.getQualityGuaranteeYear() + "years.");
		inverterExitImportEvent.setCreateUserAccount(user.getAccount());
		importantEventEntityArrayList.add(inverterExitImportEvent);

		importantEventService.saveBatch(importantEventEntityArrayList);
	}

	@Override
	public AppVO inverterByWifiStickSn(AppVO appVO) {
		AppVO result = new AppVO();
		Device21Entity device21Entity = new Device21Entity();
		device21Entity.setWifiStickSerialNumber(appVO.getWifiStickSerialNumber());
		device21Entity.setPlantId(appVO.getPlantId());
		// 根据智能能量变换器SN查询业务表，将需要扫描的储能包和智能能量变换器是否绑定, 返回智能能量变换器型号
		List<Device21Entity> dbDevice21EntityList = device21Service.list(Condition.getQueryWrapper(device21Entity));
		if (CollectionUtils.isEmpty(dbDevice21EntityList)) {
			return result;
		}
		Device21Entity dbDevice21Entity = dbDevice21EntityList.get(0);
		result.setDeviceSerialNumber(dbDevice21Entity.getDeviceSerialNumber());
		if (ValidationUtil.isNotEmpty(dbDevice21Entity.getDeviceModel())) {
			String value = DictBizCache.getValue(BizConstant.INVERT_KIND_DICT_CODE, dbDevice21Entity.getDeviceModel());
			appVO.setInverterKind(value);
		}
		return result;
	}

	@Override
	public boolean inverterMatch(AppVO appVO) {
		// 根据储能包SN查询 储能包型号，
		BatteryExitFactoryInfoEntity queryBatteryExitFactoryInfoEntity = new BatteryExitFactoryInfoEntity();
		queryBatteryExitFactoryInfoEntity.setBatterySerialNumber(appVO.getBatterySerialNumber());
		List<BatteryExitFactoryInfoEntity> dbBatteryExitFactoryInfoEntityList =
			batteryExitFactoryInfoService.list(Condition.getQueryWrapper(queryBatteryExitFactoryInfoEntity));
		if (CollectionUtils.isEmpty(dbBatteryExitFactoryInfoEntityList)) {
			return false;
		}
		BatteryExitFactoryInfoEntity dbBatteryExitFactoryInfoEntity = dbBatteryExitFactoryInfoEntityList.get(0);
		DeviceExitFactoryInfoEntity deviceExitFactoryInfoEntityQuery = new DeviceExitFactoryInfoEntity();
		deviceExitFactoryInfoEntityQuery.setDeviceSerialNumber(appVO.getDeviceSerialNumber());
		List<DeviceExitFactoryInfoEntity> list =
			deviceExitFactoryInfoService.list(Condition.getQueryWrapper(deviceExitFactoryInfoEntityQuery));
		if (CollectionUtils.isEmpty(list)) {
			return false;
		}
		DeviceExitFactoryInfoEntity deviceExitFactoryInfoEntity = list.get(0);
		String batteryType = dbBatteryExitFactoryInfoEntity.getBatteryType();
		String deviceModel = deviceExitFactoryInfoEntity.getDeviceType();
		log.info("inverterMatch device type : {}", deviceModel);
		log.info("inverterMatch battery type : {}", batteryType);
		if (this.validateBatteryTypeMatchDeviceType(batteryType, deviceModel)) {
			log.debug("not match");
			return false;
		}
		log.info("inverterMatch is true");
		return true;
	}

	private boolean validateBatteryTypeMatchDeviceType(String batteryType, String deviceModel) {
		// 储能包型号匹配数据字典
		R<List<DictBiz>> deviceBatteryMatch = dictBizClient.getList("device_battery_match");
		List<DictBiz> dictData = deviceBatteryMatch.getData();
		if (CollectionUtils.isEmpty(dictData)) {
			log.info("inverterMatch dictBizClient is null");
			return true;
		}
		// 匹配储能包型号 和 智能能量变换器型号是否匹配
		List<DictBiz> matchList =
			dictData.stream().filter(item -> StringUtil.isNotBlank(item.getDictKey()) && StringUtil.isNotBlank(item.getDictValue()))
				.filter(item -> item.getDictKey().equalsIgnoreCase(batteryType)).filter(item -> item.getDictValue().equalsIgnoreCase(deviceModel)).collect(Collectors.toList());
		if (CollectionUtils.isEmpty(matchList)) {
			log.info("not battery match");
			return true;
		}
		return false;
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public R<String> addBatteryDevice(AppVO appVO) {
		R<String> r = new R<>();
		String batterySerialNumber = appVO.getBatterySerialNumber();
		String currentLanguage = CommonUtil.getCurrentLanguage();

		BatteryExitFactoryInfoEntity batteryExitFactoryInfoServiceOne = batteryExitFactoryInfoService
			.getOne(Wrappers.<BatteryExitFactoryInfoEntity>lambdaQuery().eq(BatteryExitFactoryInfoEntity::getBatterySerialNumber, batterySerialNumber));
		// 只允许添加【在web端后台录入了出厂信息的储能包】，否则提示错误
		if (ObjectUtils.isEmpty(batteryExitFactoryInfoServiceOne)) {
			r.setMsg(I18nMsgCode.SKYWORTH_CLIENT_BATTERY_100128.autoGetMessage(currentLanguage));
			r.setCode(I18nMsgCode.SKYWORTH_CLIENT_BATTERY_100128.getCode());
			return r;
		}

		BatteryMapDeviceEntity queryBatteryMapDevice = new BatteryMapDeviceEntity();
		queryBatteryMapDevice.setBatterySerialNumber(batterySerialNumber);
		List<BatteryMapDeviceEntity> batteryMapDeviceEntityList =
			batteryMapDeviceService.list(Condition.getQueryWrapper(queryBatteryMapDevice));
		// 如果原来有添加过，则报错
		if (CollectionUtil.isNotEmpty(batteryMapDeviceEntityList)) {
			r.setMsg(I18nMsgCode.SKYWORTH_CLIENT_INVERTER_100111.autoGetMessage(currentLanguage));
			r.setCode(I18nMsgCode.SKYWORTH_CLIENT_INVERTER_100111.getCode());
			return r;
		}

		// 如果没有传，默认为储能包1，比如配网扫码时，用户没有明确是从储能包1 还是储能包2进入的
		Integer batteryEnergyStorageNumber = appVO.getBatteryEnergyStorageNumber() == null ? 1 :
			appVO.getBatteryEnergyStorageNumber();

		List<BatteryMapDeviceEntity> allMapList =
			batteryMapDeviceService.list(Wrappers.<BatteryMapDeviceEntity>lambdaQuery()
				.eq(BatteryMapDeviceEntity::getDeviceSerialNumber, appVO.getDeviceSerialNumber())
				.eq(BatteryMapDeviceEntity::getPlantId, appVO.getPlantId())
				.eq(BatteryMapDeviceEntity::getBatteryEnergyStorageNumber, appVO.getBatteryEnergyStorageNumber()));
		if (CollectionUtil.isNotEmpty(allMapList)) {
			List<String> batterySerialNumbers = allMapList.stream()
				.map(BatteryMapDeviceEntity::getBatterySerialNumber)
				.collect(Collectors.toList());
			List<BatteryExitFactoryInfoEntity> batteryExitFactoryList =
				batteryExitFactoryInfoService.queryByBatterySerialNumbers(batterySerialNumbers);
			// 如果之前添加储能包的储能包容量，和后面添加的储能包容量不一致，也要提示错误
			// 型号与容量一致，才能添加成功，但不限制厂商
			for (BatteryExitFactoryInfoEntity batteryExitFactoryInfoEntity : batteryExitFactoryList) {
				if (!batteryExitFactoryInfoServiceOne.getRatedBatteryEnergy().equals(batteryExitFactoryInfoEntity.getRatedBatteryEnergy()) ||
					!batteryExitFactoryInfoServiceOne.getBatteryType().equals(batteryExitFactoryInfoEntity.getBatteryType())) {
					r.setMsg(StringUtil.format(I18nMsgCode.SKYWORTH_CLIENT_BATTERY_100129.autoGetMessage(currentLanguage), batteryExitFactoryInfoServiceOne.getRatedBatteryEnergy(), batteryExitFactoryInfoEntity.getRatedBatteryEnergy()));
					r.setCode(I18nMsgCode.SKYWORTH_CLIENT_BATTERY_100129.getCode());
					return r;
				}
			}
		}


		// 保存 储能包站点关系
		BatteryMapDeviceEntity saveBatteryMapDevice = new BatteryMapDeviceEntity();
		saveBatteryMapDevice.setDeviceSerialNumber(appVO.getDeviceSerialNumber());
		saveBatteryMapDevice.setBatterySerialNumber(batterySerialNumber);
		saveBatteryMapDevice.setPlantId(appVO.getPlantId());
		saveBatteryMapDevice.setBatteryEnergyStorageNumber(batteryEnergyStorageNumber);
		BladeUser user = AuthUtil.getUser();
		saveBatteryMapDevice.setCreateUserAccount(user.getAccount());
		saveBatteryMapDevice.setCreateUser(user.getUserId());
		if (Boolean.TRUE.equals(appVO.getMatchOrNot())) {
			saveBatteryMapDevice.setBatteryMatchDeviceFlag("true");
		} else {
			saveBatteryMapDevice.setBatteryMatchDeviceFlag("false");
		}
		batteryMapDeviceService.save(saveBatteryMapDevice);

		// 更新站点上储能包设备数量
		PlantEntity updatePlantEntity = new PlantEntity();
		updatePlantEntity.setId(appVO.getPlantId());
		updatePlantEntity.setBatteryNumber(1);
		plantService.updatePlant(updatePlantEntity);
		// 保存重要事件
		this.saveBatteryImportantEventByAdd(appVO, user);

		// 将储能包出厂更新为已使用
		batteryExitFactoryInfoService.updateForScanBattery(batterySerialNumber, BizConstant.NUMBER_ONE);
		return R.data("操作成功");
	}

	private void saveBatteryImportantEventByAdd(AppVO appVO, BladeUser user) {
		List<ImportantEventEntity> importantEventEntityArrayList = new ArrayList<>();
		ImportantEventEntity batteryCreateEvent = new ImportantEventEntity();
		batteryCreateEvent.setPlantId(appVO.getPlantId());
		batteryCreateEvent.setEventType(BizConstant.CLIENT_IMPORTANT_EVENT_TYPE_BATTERY);
		batteryCreateEvent.setEventContent("client.guardian.important.event.add.battery");
		batteryCreateEvent.setEventRemark("The Battery SN : " + appVO.getBatterySerialNumber());
		batteryCreateEvent.setSerialNumber(appVO.getBatterySerialNumber());
		batteryCreateEvent.setEventDate(new Date());
		batteryCreateEvent.setCreateUserAccount(user.getAccount());
		batteryCreateEvent.setCreateUser(user.getUserId());
		importantEventEntityArrayList.add(batteryCreateEvent);

		// 查询当前智能能量变换器的储能包
		List<BatteryMapDeviceEntity> inverterMapBatteryList = getInverterMapBatteryList(appVO.getPlantId());
		List<String> batterySnList =
			inverterMapBatteryList.stream().map(BatteryMapDeviceEntity::getBatterySerialNumber).collect(Collectors.toList());
		// 查询出厂信息
		List<BatteryExitFactoryInfoEntity> batteryExitFactoryInfoEntities =
			batteryExitFactoryInfoService.queryByBatterySerialNumbers(batterySnList);
		// 计算储能包总量
		double totalRatedBatteryEnergy = batteryExitFactoryInfoEntities.stream()
			.filter(x -> ObjUtil.isNotNull(x.getRatedBatteryEnergy()))
			.mapToDouble(e -> Double.parseDouble(e.getRatedBatteryEnergy()))
			.sum();
		DecimalFormat df = new DecimalFormat("#");
		ImportantEventEntity inverterAddBatteryEvent = new ImportantEventEntity();
		inverterAddBatteryEvent.setPlantId(appVO.getPlantId());
		inverterAddBatteryEvent.setSerialNumber(appVO.getDeviceSerialNumber());
		inverterAddBatteryEvent.setEventType(BizConstant.CLIENT_IMPORTANT_EVENT_TYPE_INVERTER);
		inverterAddBatteryEvent.setEventDate(new Date());
		inverterAddBatteryEvent.setEventContent("client.guardian.important.event.add.battery.factory");
		inverterAddBatteryEvent.setEventRemark("The Battery SN : " + appVO.getBatterySerialNumber() + ",Current " +
			"Battery Number : " + batterySnList.size()
			+ ",Current Battery Rated Total Energy : " + df.format(totalRatedBatteryEnergy) + " kWh");
		inverterAddBatteryEvent.setCreateUserAccount(user.getAccount());
		importantEventEntityArrayList.add(inverterAddBatteryEvent);

		importantEventService.saveBatch(importantEventEntityArrayList);
	}

	private void saveBatteryImportantEventByDelete(BatteryMapDeviceVO batteryMapDeviceVO, BladeUser user) {
		List<ImportantEventEntity> importantEventEntityArrayList = new ArrayList<>();
		ImportantEventEntity batteryDeleteEvent = new ImportantEventEntity();
		batteryDeleteEvent.setPlantId(batteryMapDeviceVO.getPlantId());
		batteryDeleteEvent.setEventType(BizConstant.CLIENT_IMPORTANT_EVENT_TYPE_BATTERY);
		batteryDeleteEvent.setEventContent("client.guardian.important.event.unbound.battery");
		batteryDeleteEvent.setEventRemark("The Battery SN : " + batteryMapDeviceVO.getBatterySerialNumber());
		batteryDeleteEvent.setSerialNumber(batteryMapDeviceVO.getBatterySerialNumber());
		batteryDeleteEvent.setEventDate(new Date());
		batteryDeleteEvent.setCreateUserAccount(user.getAccount());
		batteryDeleteEvent.setCreateUser(user.getUserId());
		importantEventEntityArrayList.add(batteryDeleteEvent);

		// 查询当前智能能量变换器的储能包
		List<BatteryMapDeviceEntity> inverterMapBatteryList =
			getInverterMapBatteryList(batteryMapDeviceVO.getPlantId());
		List<String> batterySnList =
			inverterMapBatteryList.stream().map(BatteryMapDeviceEntity::getBatterySerialNumber).collect(Collectors.toList());
		// 查询条件删除当前智能能量变换器绑定的储能包
		batterySnList.remove(batteryMapDeviceVO.getBatterySerialNumber());
		double totalRatedBatteryEnergy = 0.0;
		if (CollectionUtil.isNotEmpty(batterySnList)) {
			// 查询出厂信息
			List<BatteryExitFactoryInfoEntity> batteryExitFactoryInfoEntities =
				batteryExitFactoryInfoService.queryByBatterySerialNumbers(batterySnList);
			// 计算储能包总量
			totalRatedBatteryEnergy = batteryExitFactoryInfoEntities.stream()
				.filter(x -> ObjUtil.isNotNull(x.getRatedBatteryEnergy()))
				.mapToDouble(e -> Double.parseDouble(e.getRatedBatteryEnergy()))
				.sum();
		}


		DecimalFormat df = new DecimalFormat("#");
		ImportantEventEntity inverterDeleteBatteryEvent = new ImportantEventEntity();
		inverterDeleteBatteryEvent.setPlantId(batteryMapDeviceVO.getPlantId());
		inverterDeleteBatteryEvent.setSerialNumber(batteryMapDeviceVO.getDeviceSerialNumber());
		inverterDeleteBatteryEvent.setEventType(BizConstant.CLIENT_IMPORTANT_EVENT_TYPE_INVERTER);
		inverterDeleteBatteryEvent.setEventDate(new Date());
		inverterDeleteBatteryEvent.setEventContent("client.guardian.important.event.unbound.battery.factory");
		inverterDeleteBatteryEvent.setEventRemark("The Battery SN : " + batteryMapDeviceVO.getBatterySerialNumber() +
			",Current Battery Number : " + batterySnList.size()
			+ ",Current Battery Rated Total Energy : " + df.format(totalRatedBatteryEnergy) + " kWh");
		inverterDeleteBatteryEvent.setCreateUserAccount(user.getAccount());
		importantEventEntityArrayList.add(inverterDeleteBatteryEvent);

		importantEventService.saveBatch(importantEventEntityArrayList);
	}

	private void saveGuardianImportantByAdd(GuardianPlantEntity guardianPlantEntity, BladeUser user,
											PlantEntity plant) {
		List<ImportantEventEntity> importantEventEntityArrayList = new ArrayList<>();

		String plantAllAddress = this.getAllAddressByPlant(plant);
		ImportantEventEntity guardianCreateImportEvent = new ImportantEventEntity();
		guardianCreateImportEvent.setPlantId(guardianPlantEntity.getPlantId());
		guardianCreateImportEvent.setEventType(BizConstant.CLIENT_IMPORTANT_EVENT_TYPE_GUARDIAN);
		guardianCreateImportEvent.setEventContent("client.guardian.important.event.guardian.bound");
		guardianCreateImportEvent.setEventRemark("Create User : " + user.getUserName() +
			",Energy Station Name : " + plant.getPlantName() + ",Energy Station Address : " + plantAllAddress + "," +
			"Guardian SN : " + guardianPlantEntity.getSecurityGuardSerialNumber());
		guardianCreateImportEvent.setSerialNumber(guardianPlantEntity.getSecurityGuardSerialNumber());
		guardianCreateImportEvent.setEventDate(new Date());
		guardianCreateImportEvent.setCreateUserAccount(user.getAccount());
		guardianCreateImportEvent.setCreateUser(user.getUserId());
		importantEventEntityArrayList.add(guardianCreateImportEvent);


		GuardianExitFactoryInfoEntity exitFactoryInfoEntity = new GuardianExitFactoryInfoEntity();
		exitFactoryInfoEntity.setSecurityGuardSerialNumber(guardianPlantEntity.getSecurityGuardSerialNumber());
		GuardianExitFactoryInfoEntity factoryInfoEntity =
			guardianExitFactoryInfoService.getOne(Condition.getQueryWrapper(exitFactoryInfoEntity));
		ImportantEventEntity inverterExitImportEvent = new ImportantEventEntity();
		inverterExitImportEvent.setEventType(BizConstant.CLIENT_IMPORTANT_EVENT_TYPE_GUARDIAN);
		inverterExitImportEvent.setPlantId(guardianPlantEntity.getPlantId());
		inverterExitImportEvent.setSerialNumber(factoryInfoEntity.getSecurityGuardSerialNumber());
		inverterExitImportEvent.setEventDate(java.sql.Date.valueOf(factoryInfoEntity.getExitFactoryDate()));
		inverterExitImportEvent.setEventContent("client.guardian.important.event.guardian.bound.factory");
		inverterExitImportEvent.setEventRemark("Manufacturer : " + factoryInfoEntity.getCompany() + ",Warranty " +
			"period" +
			" " +
			": " + factoryInfoEntity.getQualityGuaranteeYear() + "years.");
		inverterExitImportEvent.setCreateUserAccount(user.getAccount());
		importantEventEntityArrayList.add(inverterExitImportEvent);

		importantEventService.saveBatch(importantEventEntityArrayList);
	}

	private List<BatteryMapDeviceEntity> getInverterMapBatteryList(Long plantId) {
		QueryWrapper<BatteryMapDeviceEntity> batteryMapDeviceVoQueryWrapper = new QueryWrapper<>();
		batteryMapDeviceVoQueryWrapper.eq(DatabaseFieldConstant.PLANT_ID, plantId);
		return batteryMapDeviceService.list(batteryMapDeviceVoQueryWrapper);
	}

	//@Override
	//public IPage<PlantVO> queryPlantBySelf(AppVO appVO, Query query) {
	//PlantVO queryPlant = new PlantVO();
	//query.setDescs(DatabaseFieldConstant.CREATE_TIME);
	//BladeUser user = AuthUtil.getUser();
	//String deptId = inspectInnerRole(user);
	//queryPlant.setCreateUser(user.getUserId());
	//queryPlant.setDeptId(deptId);
	//IPage<PlantVO> page = Condition.getPage(query);
	//IPage<PlantVO> plantVOIPage = plantService.selectPlantPage(page, queryPlant);
	//List<PlantVO> plantVOList = plantVOIPage.getRecords();
	//List<QueryCondition> list = new ArrayList<>();
	//List<Long> plantIdList = new ArrayList<>();
	//List<String> regionCodeList = new ArrayList<>();
	//List<Device24Entity> device24EntityList = new ArrayList<>();
	//List<WifiStickPlantEntity> wifiStickPlantEntities = new ArrayList<>();
	////获取代理商人员信息
	//Map<Long, AgentUserVo> agentUserVoMap = new HashMap<>();
	//getAgentUserInfo(plantVOList, agentUserVoMap);
	//
	////获取代理商信息
	//Map<Long, AgentCompanyVO> companyMap = new HashMap<>();
	//getCompanyInfo(plantVOList, companyMap);
	//
	//for (PlantVO plantVO : plantVOList) {
	//	QueryCondition queryCondition = new QueryCondition();
	//	queryCondition.setPlantId(plantVO.getId());
	//	list.add(queryCondition);
	//	plantIdList.add(plantVO.getId());
	//	regionCodeList.add(plantVO.getCountryCode());
	//	regionCodeList.add(plantVO.getProvinceCode());
	//	regionCodeList.add(plantVO.getCityCode());
	//	regionCodeList.add(plantVO.getCountyCode());
	//	Long operationUserId = plantVO.getOperationUserId();
	//	Long operationCompany = plantVO.getOperationCompanyId();
	//	plantVO.setCompanyInfo(new AgentCompanyVO());
	//	plantVO.setAgentUserInfo(new AgentUserVo());
	//	if (ValidationUtil.isNotEmpty(operationUserId)) {
	//		if (!org.springblade.common.utils.CollectionUtils.isNullOrEmpty(agentUserVoMap)) {
	//			plantVO.setAgentUserInfo(agentUserVoMap.get(operationUserId));
	//		} else {
	//			plantVO.setAgentUserInfo(new AgentUserVo());
	//		}
	//
	//	}
	//	if (ValidationUtil.isNotEmpty(operationCompany)) {
	//		if (!org.springblade.common.utils.CollectionUtils.isNullOrEmpty(companyMap)) {
	//			plantVO.setCompanyInfo(companyMap.get(operationCompany));
	//		} else {
	//			plantVO.setCompanyInfo(new AgentCompanyVO());
	//		}
	//
	//	}
	//
	//	Device24Entity device24Entity = new Device24Entity();
	//	device24Entity.setPlantId(plantVO.getId());
	//	device24Entity.setDeviceSerialNumber(plantVO.getDeviceSerialNumber());
	//	device24EntityList.add(device24Entity);
	//
	//	WifiStickPlantEntity wifiStickPlant = new WifiStickPlantEntity();
	//	wifiStickPlant.setPlantId(plantVO.getId());
	//	wifiStickPlant.setDeviceSerialNumber(plantVO.getDeviceSerialNumber());
	//	wifiStickPlantEntities.add(wifiStickPlant);
	//}
	//String currentLanguage = CommonUtil.getCurrentLanguage();
	//CompletableFuture<Void> regionInfoFuture;
	//if (CommonConstant.CURRENT_LANGUAGE_ZH.equals(currentLanguage)) {
	//	regionInfoFuture = CompletableFuture.runAsync(() -> setRegionInfo(regionCodeList, plantVOList));
	//} else {
	//	regionInfoFuture = CompletableFuture.runAsync(() -> setRegionInfoEn(regionCodeList, plantVOList));
	//}
	//
	//
	//this.getDeviceSerialNumber(plantIdList, plantVOList);
	//
	//CompletableFuture<Void> batteryDataFuture = CompletableFuture.runAsync(() -> getBatteryData(list, plantVOList));
	//
	//CompletableFuture<Void> inverterControlFuture = CompletableFuture.runAsync(() -> setInverterControl(plantVOList,
	// device24EntityList));
	//
	//CompletableFuture<Void> startupByBackstageFuture = CompletableFuture.runAsync(() -> setStartupByBackstage
	// (plantVOList, wifiStickPlantEntities));
	//
	//// 组合 CompletableFuture 对象并等待所有任务完成：
	//CompletableFuture<Void> allFutures = CompletableFuture.allOf(regionInfoFuture, batteryDataFuture,
	// inverterControlFuture, startupByBackstageFuture);
	//allFutures.join();
	//
	//log.info("query plant : {} ", plantVOIPage);
	//	return plantVOIPage;
	//}

	/**
	 * 核查当前用户是否是内部角色
	 */
	public static String inspectInnerRole(BladeUser user) {
		String deptId = "";
		if (user.getDetail() != null) {
			Boolean roleInnerFlag = (Boolean) user.getDetail().get(CommonConstant.USER_ROLE_INNER_FLAG);
			// 如果包含创维内部角色，则可查看所有数据，不然只能查看他自己部门的数据
			if (roleInnerFlag != null && !roleInnerFlag) {
				deptId = user.getDeptId();
			}
		}
		return deptId;
	}

	/**
	 * 获取代理商人员信息
	 */
	private void getAgentUserInfo(List<PlantVO> plantVOList, Map<Long, AgentUserVo> agentUserVoMap) {
		List<Long> userIds =
			plantVOList.stream().map(PlantVO::getOperationUserId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
		List<User> userList = userSearchClient.listByUserIds(userIds).getData();
		if (!org.springblade.common.utils.CollectionUtils.isNullOrEmpty(userList)) {
			userList.parallelStream().forEach(v -> {
				AgentUserVo agentUserVo = new AgentUserVo();
				BeanUtils.copyProperties(v, agentUserVo);
				agentUserVoMap.put(v.getId(), agentUserVo);
			});
		}

	}

	/**
	 * 获取代理商公司信息
	 */
	private void getCompanyInfo(List<PlantVO> plantVOList, Map<Long, AgentCompanyVO> companyVoMap) {
		List<Long> companyIds =
			plantVOList.stream().map(PlantVO::getOperationCompanyId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
		List<AgentCompanyVO> agentCompanyVOList = agentClient.agentCompanyInfoByIds(companyIds).getData();
		if (!org.springblade.common.utils.CollectionUtils.isNullOrEmpty(agentCompanyVOList)) {
			agentCompanyVOList.parallelStream().forEach(v -> {
				AgentCompanyVO agentCompanyVO = new AgentCompanyVO();
				BeanUtils.copyProperties(v, agentCompanyVO);
				companyVoMap.put(v.getDeptId(), agentCompanyVO);
			});
		}

	}

	/**
	 * @param regionCodeList 区域code集合
	 * @param plantVOList    站点集合
	 */
	private void setRegionInfoEn(List<String> regionCodeList, List<PlantVO> plantVOList) {
		List<String> regionCodeNotNullList =
			regionCodeList.stream().filter(StringUtil::isNotBlank).collect(Collectors.toList());
		if (CollectionUtil.isNotEmpty(regionCodeNotNullList)) {
			R<List<Region>> regionResult = sysClient.getRegionList(regionCodeNotNullList);
			List<Region> regionList = regionResult.getData();
			Collections.reverse(regionList);
			for (PlantVO plantVO : plantVOList) {
				StringBuilder address = new StringBuilder(plantVO.getDetailAddress() == null ? "" :
					plantVO.getDetailAddress() + " ");
				if (CollectionUtils.isEmpty(regionList)) {
					break;
				}
				for (Region region : regionList) {
					if (region.getCode().equalsIgnoreCase(plantVO.getCountryCode())) {
						address.append(region.getName()).append(" ");
						plantVO.setCountryName(region.getName());
					}
					if (region.getCode().equalsIgnoreCase(plantVO.getCityCode())) {
						address.append(region.getName()).append(" ");
						plantVO.setCityName(region.getName());
					}
					if (region.getCode().equalsIgnoreCase(plantVO.getProvinceCode())) {
						address.append(region.getName()).append(" ");
						plantVO.setProvinceName(region.getName());
					}
					if (region.getCode().equalsIgnoreCase(plantVO.getCountyCode())) {
						address.append(region.getName()).append(" ");
						plantVO.setCountyName(region.getName());
					}
				}
				plantVO.setAddress(address.toString());
			}
		}
	}

	private void setStartupByBackstage(List<PlantVO> plantVOList, List<WifiStickPlantEntity> wifiStickPlantEntities) {
		if (CollectionUtils.isEmpty(plantVOList)) {
			return;
		}

		List<WifiStickPlantEntity> wifiStickInfo = wifiStickPlantService.getWifiStickInfo(wifiStickPlantEntities);
		for (PlantVO plantVO : plantVOList) {
			JSONObject objStartupByBackstage = new JSONObject();
			JSONObject objInverterConfigureNetwork = new JSONObject();
			JSONObject objInverterDeviceType = new JSONObject();
			for (WifiStickPlantEntity wifiStickPlant : wifiStickInfo) {
				//主机
				if (plantVO.getId().equals(wifiStickPlant.getPlantId()) && plantVO.getDeviceSerialNumber().equals(wifiStickPlant.getDeviceSerialNumber())) {
					if (ValidationUtil.isNotEmpty(plantVO.getDeviceSerialNumber()) &&
						plantVO.getId().equals(wifiStickPlant.getPlantId()) &&
						plantVO.getDeviceSerialNumber().equals(wifiStickPlant.getDeviceSerialNumber())) {
						plantVO.setStartupByBackstage(wifiStickPlant.getStartupByBackstage());
						plantVO.setInverterConfigureNetwork(wifiStickPlant.getInverterConfigureNetwork());
					}
					objStartupByBackstage.put("mainStartupByBackstage", wifiStickPlant.getStartupByBackstage());
					objInverterConfigureNetwork.put("mainInverterConfigureNetwork",
						wifiStickPlant.getInverterConfigureNetwork());
					objInverterDeviceType.put("mainInverterDeviceType", wifiStickPlant.getDeviceType());
				} else if (plantVO.getId().equals(wifiStickPlant.getPlantId()) && !plantVO.getDeviceSerialNumber().equals(wifiStickPlant.getDeviceSerialNumber())) {
					objStartupByBackstage.put("subStartupByBackstage", wifiStickPlant.getStartupByBackstage());
					objInverterConfigureNetwork.put("subInverterConfigureNetwork",
						wifiStickPlant.getInverterConfigureNetwork());
					objInverterDeviceType.put("subInverterDeviceType", wifiStickPlant.getDeviceType());
				}
			}
			plantVO.setStartupByBackstageArr(objStartupByBackstage);
			plantVO.setInverterConfigureNetworkArr(objInverterConfigureNetwork);
			plantVO.setInverterDeviceTypeArr(objInverterDeviceType);

		}
	}

	private void setInverterControl(List<PlantVO> plantVOList, List<Device24Entity> device24EntityList) {
		if (CollectionUtils.isEmpty(plantVOList)) {
			return;
		}

		List<Device24Entity> device24Info = device24Service.getDevice24Info(device24EntityList);
		for (PlantVO plantVO : plantVOList) {
			JSONObject inverterControlArr = new JSONObject();
			for (Device24Entity device24 : device24Info) {
				//主机的inverterControl
				if (plantVO.getId().equals(device24.getPlantId()) && ValidationUtil.isNotEmpty(plantVO.getDeviceSerialNumber())
					&& (plantVO.getDeviceSerialNumber().equals(device24.getDeviceSerialNumber()))) {
					plantVO.setInverterControl(device24.getInverterControl());
					inverterControlArr.put("mainInverterControl", device24.getInverterControl());
				} else if (plantVO.getId().equals(device24.getPlantId())) {
					inverterControlArr.put("subInverterControl", device24.getInverterControl());
				}
			}
			plantVO.setInverterControlArr(inverterControlArr);
		}
	}

	private void setRegionInfo(List<String> regionCodeList, List<PlantVO> plantVOList) {
		List<String> regionCodeNotNullList =
			regionCodeList.stream().filter(StringUtil::isNotBlank).collect(Collectors.toList());
		if (CollectionUtil.isNotEmpty(regionCodeNotNullList)) {
			R<List<Region>> regionResult = sysClient.getRegionList(regionCodeNotNullList);
			List<Region> regionList = regionResult.getData();
			for (PlantVO plantVO : plantVOList) {
				StringBuilder address = new StringBuilder();
				if (CollectionUtils.isEmpty(regionList)) {
					break;
				}
				for (Region region : regionList) {
					if (region.getCode().equalsIgnoreCase(plantVO.getCountryCode())) {
						address.append(region.getName()).append(" ");
						plantVO.setCountryName(region.getName());
					}
					if (region.getCode().equalsIgnoreCase(plantVO.getProvinceCode())) {
						address.append(region.getName()).append(" ");
						plantVO.setProvinceName(region.getName());
					}
					if (region.getCode().equalsIgnoreCase(plantVO.getCityCode())) {
						address.append(region.getName()).append(" ");
						plantVO.setCityName(region.getName());
					}
					if (region.getCode().equalsIgnoreCase(plantVO.getCountyCode())) {
						address.append(region.getName()).append(" ");
						plantVO.setCountyName(region.getName());
					}
				}
				plantVO.setAddress(address.append(" ").append(plantVO.getDetailAddress() == null ? "" :
					plantVO.getDetailAddress()).toString());
			}
		}
	}

	private void getBatteryData(List<QueryCondition> list, List<PlantVO> plantVOList) {
		if (CollectionUtil.isEmpty(list)) {
			return;
		}
		List<BatteryCurrentStatusEntity> batteryCurrentStatusEntities =
			batteryCurrentStatusService.batchQueryAppBatteryCurrentStatus(list);
		Map<String, BatteryCurrentStatusEntity> collect =
			batteryCurrentStatusEntities.stream().collect(Collectors.toMap(BatteryCurrentStatusEntity::getDeviceSerialNumber, v -> v));
		Date now = new Date();
		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
		for (PlantVO plantVO : plantVOList) {
			WifiStickPlantEntity wifiStickPlant = new WifiStickPlantEntity();
			wifiStickPlant.setPlantId(plantVO.getId());
			List<WifiStickPlantEntity> wifiStickPlantEntityList =
				wifiStickPlantService.list(Condition.getQueryWrapper(wifiStickPlant));
			// 站点表中没有扫描添加设备，则跳出此站点
			if (wifiStickPlantEntityList == null || ValidationUtil.isEmpty(wifiStickPlantEntityList)) {
				continue;
			}

			String deviceSn;
			for (WifiStickPlantEntity wifiStickPlantEntity : wifiStickPlantEntityList) {
				deviceSn = wifiStickPlantEntity.getDeviceSerialNumber();
				Integer deviceType = wifiStickPlantEntity.getParallelDeviceType();
				// 如果主机、从机标识为空，则跳过
				if (Objects.isNull(wifiStickPlantEntity.getParallelDeviceType())) {
					continue;
				}
				// 如果储能包当前状态表中不存在站点对应的SN,则标识无法取到 pv和es，跳过
				if (!collect.containsKey(deviceSn)) {
					continue;
				}
				//主机和从机
				BatteryCurrentStatusEntity entity = collect.get(deviceSn);
				// 判断是否为今天,不为今天，则天数据为0
				if (!formatter.format(now).equalsIgnoreCase(formatter.format(entity.getDeviceDateTime()))) {
					plantVO.setPowerGeneration(this.getChangeEnergyResult(BigDecimal.ZERO));
					plantVO.setLoadCapacity(this.getChangeEnergyResult(BigDecimal.ZERO));
					continue;
				}
				String type = plantVO.getIsParallelMode();
				//读取主机的数据
				if (1 == deviceType) {
					//站点处于并机模式
					if (ValidationUtil.isNotEmpty(type) && Constants.ONE.equals(type)) {
						// 原始数据为kwh
						plantVO.setPowerGeneration(this.getChangeEnergyResult(entity.getPvlDailyGeneratingEnergySum() == null ? BigDecimal.ZERO : entity.getPvlDailyGeneratingEnergySum().multiply(BigDecimal.valueOf(1000))));
						plantVO.setLoadCapacity(this.getChangeEnergyResult(entity.getBatteryDailyChargeEnergyParallel() == null ? BigDecimal.ZERO : entity.getBatteryDailyChargeEnergyParallel().multiply(BigDecimal.valueOf(1000))));
					} else {
						log.info("非并机模式取主机的数据 : {},{}", plantVO.getId(), entity.getDeviceSerialNumber());
						// 非并机模式取主机的数据
						plantVO.setPowerGeneration(this.getChangeEnergyResult(entity.getTodayEnergy()));
						BigDecimal batteryDailyChargeEnergy = entity.getBatteryDailyChargeEnergy() == null ?
							BigDecimal.ZERO : entity.getBatteryDailyChargeEnergy();
						if (!businessServiceUtil.needEnergyStorage2ByDeviceModel(wifiStickPlantEntity.getDeviceSerialNumber())) {
							log.info("非30千瓦 : {},{}", plantVO.getId(), entity.getDeviceSerialNumber());
							plantVO.setLoadCapacity(getChangeEnergyResult(batteryDailyChargeEnergy));
							continue;
						}
						BigDecimal batteryTodayChargeEnergy2 = entity.getBatteryTodayChargeEnergy2() == null ?
							BigDecimal.ZERO : entity.getBatteryTodayChargeEnergy2();
						log.info("30千瓦相加 : {}, {}, {}", entity.getDeviceSerialNumber(), batteryDailyChargeEnergy,
							batteryTodayChargeEnergy2);
						String changeEnergyResult =
							DataUnitConversionUtil.getChangeEnergyResult(batteryDailyChargeEnergy.add(batteryTodayChargeEnergy2), 1);
						plantVO.setLoadCapacity(changeEnergyResult);
					}
				}

			}
		}
	}


	private void getDeviceSerialNumber(List<Long> plantIdList, List<PlantVO> plantVOList) {
		if (CollectionUtil.isNotEmpty(plantIdList)) {
			List<WifiStickPlantEntity> wifiStickPlantVOList =
				wifiStickPlantService.queryDeviceSerialNumberList(plantIdList);

			Map<Long, List<String>> collect = wifiStickPlantVOList.stream()
				.collect(Collectors.groupingBy(WifiStickPlantEntity::getPlantId,
					Collectors.mapping(WifiStickPlantEntity::getDeviceSerialNumber, Collectors.toList())));

			Map<String, Integer> deviceModeList =
				wifiStickPlantVOList.stream().collect(Collectors.toMap(WifiStickPlantEntity::getDeviceSerialNumber,
					entity -> Optional.ofNullable(entity.getParallelDeviceType()).orElse(BizConstant.CLIENT_INVERTER_MASTER_MODEL)));
			List<String> deviceExitFactoryInfoEntities = new ArrayList<>();
			for (PlantVO plantVO : plantVOList) {
				JSONObject obj = new JSONObject();
				if (collect.containsKey(plantVO.getId())) {
					boolean flag = false;
					String deviceSn = null;
					List<String> deviceSerialNumberList = collect.get(plantVO.getId());
					for (String deviceSerialNumber : deviceSerialNumberList) {
						deviceExitFactoryInfoEntities.add(deviceSerialNumber);
						//主机
						if (ValidationUtil.isNotEmpty(deviceModeList.get(deviceSerialNumber)) && deviceModeList.get(deviceSerialNumber) == 1) {
							flag = true;
							deviceSn = deviceSerialNumber;
							obj.put("mainSn", deviceSn);
						} else {
							if (!flag) {
								deviceSn = deviceSerialNumber;
							}
							obj.put("subSn", deviceSerialNumber);
						}
					}
					plantVO.setDeviceSnArr(obj);
					plantVO.setDeviceSerialNumber(deviceSn);

				}
			}

			//根据sn列表查出出厂信息数据列表
			List<DeviceExitFactoryInfoEntity> deviceExitFactoryInfoEntityList =
				deviceExitFactoryInfoService.getListByDeviceSerialNumberCollect(deviceExitFactoryInfoEntities);
			// key是sn，value是型号
			Map<String, String> deviceExitCollect =
				deviceExitFactoryInfoEntityList.stream().collect(Collectors.toMap(DeviceExitFactoryInfoEntity::getDeviceSerialNumber, DeviceExitFactoryInfoEntity::getDeviceType));
			for (PlantVO plantVO : plantVOList) {
				JSONObject obj = new JSONObject();
				//集合中判断是否存在这个站点id
				if (collect.containsKey(plantVO.getId())) {
					// 取出站点的下属智能能量变换器
					List<String> deviceSerialNumberList = collect.get(plantVO.getId());
					for (String deviceSerialNumber : deviceSerialNumberList) {
						if (deviceExitCollect.containsKey(deviceSerialNumber)) {
							//从字典根据出厂型号查出 inverterKind
							String value = DictBizCache.getValue(BizConstant.INVERT_KIND_DICT_CODE,
								deviceExitCollect.get(deviceSerialNumber));
							//主机
							if (ValidationUtil.isNotEmpty(deviceModeList.get(deviceSerialNumber)) && deviceModeList.get(deviceSerialNumber) == 1) {
								obj.put("mainInverterKind", value);
							} else {
								obj.put("subInverterKind", value);
							}
						}
					}
					plantVO.setInverterKindArr(obj);
				}
			}
		}
	}

	private void subtractBeforeData(List<AppReportDataVO> list) {
		BigDecimal beforeCharge = new BigDecimal(0);
		for (AppReportDataVO vo : list) {
			BigDecimal value = vo.getValue();
			if (value.doubleValue() != 0) {
				if (beforeCharge.compareTo(value) > 0) {
					continue;
				}
				BigDecimal result = value.subtract(beforeCharge);
				vo.setValue(result);
				beforeCharge = value;
			}
		}
	}


	//@Override
	//public AppReportHeaderVO queryPlantRunningState(AppVO appVO) {
	//	AppReportHeaderVO resultAppReportHeaderVO = this.setReportHeaderData(appVO);
	//	try {
	//		// 查询日月年报表数据
	//		if (0 == appVO.getType()) {
	//			AppReportDetailVO hourReport = this.getHoursReport(appVO);
	//			List<AppReportDataVO> powerGeneration = hourReport.getPowerGeneration();
	//			List<AppReportDataVO> dischargeCapacity = hourReport.getDischargeCapacity();
	//			this.sortList(powerGeneration);
	//			this.sortList(dischargeCapacity);
	//			this.subtractBeforeData(powerGeneration);
	//			this.subtractBeforeData(dischargeCapacity);
	//			resultAppReportHeaderVO.setDailyReport(hourReport);
	//		} else if (1 == appVO.getType()) {
	//			// 周报表，过去7天
	//			SimpleDateFormat simpleDateFormat = new SimpleDateFormat(DateUtil.PATTERN_DATE);
	//			Date parse = simpleDateFormat.parse(appVO.getDataScope());
	//			Instant instant = parse.toInstant();
	//			LocalDate endLocalDate = instant.atZone(ZoneId.systemDefault()).toLocalDate();
	//			LocalDate beginLocalDate = endLocalDate.plusDays(-7L);
	//			Function<QueryCondition, List<BatteryEverydayTotalVO>> dayFun =
	//			batteryEverydayTotalService::weekEstimate;
	//			AppReportDetailVO dailyReport = this.getAppReportDetailVO(appVO, beginLocalDate, endLocalDate,
	//			simpleDateFormat, dayFun);
	//			this.completionDay(dailyReport, beginLocalDate, endLocalDate, "MM-dd");
	//			resultAppReportHeaderVO.setWeekReport(dailyReport);
	//		} else if (2 == appVO.getType()) {
	//			// 月报表
	//			SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM");
	//			Date dataScope = simpleDateFormat.parse(appVO.getDataScope());
	//			Instant instant = dataScope.toInstant();
	//			LocalDate month = instant.atZone(ZoneId.systemDefault()).toLocalDate();
	//			LocalDate beginLocalDate = month.with(TemporalAdjusters.firstDayOfMonth());
	//			LocalDate endLocalDate = month.with(TemporalAdjusters.lastDayOfMonth());
	//			Function<QueryCondition, List<BatteryEverydayTotalVO>> monthFun =
	//			batteryEverydayTotalService::monthEstimate;
	//			AppReportDetailVO monthReport = this.getAppReportDetailVO(appVO, beginLocalDate, endLocalDate,
	//			simpleDateFormat, monthFun);
	//			this.completionDay(monthReport, beginLocalDate, endLocalDate, "dd");
	//			resultAppReportHeaderVO.setMonthlyReport(monthReport);
	//		} else if (3 == appVO.getType()) {
	//			// 年报表
	//			SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy");
	//			Date dataScope = simpleDateFormat.parse(appVO.getDataScope());
	//			Instant instant = dataScope.toInstant();
	//			LocalDate year = instant.atZone(ZoneId.systemDefault()).toLocalDate();
	//			LocalDate endLocalDate = year.with(TemporalAdjusters.lastDayOfYear());
	//			LocalDate beginLocalDate = year.with(TemporalAdjusters.firstDayOfYear());
	//			Function<QueryCondition, List<BatteryEverydayTotalVO>> annualFun =
	//			batteryEverydayTotalService::annualEstimate;
	//			AppReportDetailVO annualReport = this.getAppReportDetailVO(appVO, beginLocalDate, endLocalDate,
	//			simpleDateFormat, annualFun);
	//			this.completionMonth(annualReport);
	//			resultAppReportHeaderVO.setAnnualReport(annualReport);
	//		}
	//	} catch (ParseException e) {
	//		log.error("error ParseException :{} ", e);
	//	}
	//	return resultAppReportHeaderVO;
	//}

	@Override
	public AppReportHeaderVO queryPlantRunningStateV2(AppVO appVO) {
		return appReportServiceImpl.queryPlantRunningStateV2(appVO);
	}

	@Override
	public AppReportHeaderVO queryPlantRunningStateHeaderV2(AppVO appVO) {
		return appReportServiceImpl.queryPlantRunningStateHeaderV2(appVO);
	}

	@Override
	public AppReportHeaderVO queryRunningStateChartV2(AppVO appVO) {
		return appReportServiceImpl.queryRunningStateChartV2(appVO);
	}

	@Override
	public PlantDetailVO getPlantDetail(AppVO appVO) {
		PlantVO plantVO = new PlantVO();
		PlantDetailVO plantDetailVO = new PlantDetailVO();
		Long plantId = appVO.getPlantId();
		plantVO.setId(plantId);
		PlantEntity one = plantService.getOne(Condition.getQueryWrapper(plantVO));
		BeanUtils.copyProperties(one, plantVO);
		List<PlantVO> plantVOList = Collections.singletonList(plantVO);
		//获取代理商人员信息
		Map<Long, AgentUserVo> agentUserVoMap = new HashMap<>();
		getAgentUserInfo(plantVOList, agentUserVoMap);

		//获取代理商信息
		Map<Long, AgentCompanyVO> companyMap = new HashMap<>();
		getCompanyInfo(plantVOList, companyMap);


		List<String> regionCodeList = new ArrayList<>();
		List<Long> plantIdList = new ArrayList<>();
		List<QueryCondition> list = new ArrayList<>();
		List<Device24Entity> device24EntityList = new ArrayList<>();
		List<WifiStickPlantEntity> wifiStickPlantEntities = new ArrayList<>();
		PlantVO regionCodePlant = plantVOList.get(0);
		regionCodeList.add(regionCodePlant.getCountryCode());
		regionCodeList.add(regionCodePlant.getProvinceCode());
		regionCodeList.add(regionCodePlant.getCityCode());
		regionCodeList.add(regionCodePlant.getCountyCode());
		plantIdList.add(appVO.getPlantId());
		QueryCondition queryCondition = new QueryCondition();
		queryCondition.setPlantId(appVO.getPlantId());
		list.add(queryCondition);
		Device24Entity device24Entity = new Device24Entity();
		device24Entity.setPlantId(plantVO.getId());
		device24Entity.setDeviceSerialNumber(plantVO.getDeviceSerialNumber());
		device24EntityList.add(device24Entity);

		WifiStickPlantEntity wifiStickPlant = new WifiStickPlantEntity();
		wifiStickPlant.setPlantId(plantVO.getId());
		wifiStickPlant.setDeviceSerialNumber(plantVO.getDeviceSerialNumber());
		wifiStickPlantEntities.add(wifiStickPlant);
		String currentLanguage = CommonUtil.getCurrentLanguage();
		CompletableFuture<Void> regionInfoFuture;

		if (CommonConstant.CURRENT_LANGUAGE_ZH.equals(currentLanguage)) {
			regionInfoFuture = CompletableFuture.runAsync(() -> setRegionInfo(regionCodeList, plantVOList));
		} else {
			regionInfoFuture = CompletableFuture.runAsync(() -> setRegionInfoEn(regionCodeList, plantVOList));
		}

		this.getDeviceSerialNumber(plantIdList, plantVOList);

		CompletableFuture<Void> batteryDataFuture = CompletableFuture.runAsync(() -> getBatteryData(list,
			plantVOList));

		CompletableFuture<Void> inverterControlFuture =
			CompletableFuture.runAsync(() -> setInverterControl(plantVOList, device24EntityList));

		CompletableFuture<Void> startupByBackstageFuture =
			CompletableFuture.runAsync(() -> setStartupByBackstage(plantVOList, wifiStickPlantEntities));

		PlantVO agentPlant = plantVOList.get(0);
		Long operationUserId = agentPlant.getOperationUserId();
		Long operationCompany = agentPlant.getOperationCompanyId();
		if (ValidationUtil.isNotEmpty(operationUserId)) {
			if (!org.springblade.common.utils.CollectionUtils.isNullOrEmpty(agentUserVoMap)) {
				plantVO.setAgentUserInfo(agentUserVoMap.get(operationUserId));
			} else {
				plantVO.setAgentUserInfo(new AgentUserVo());
			}

		}
		if (ValidationUtil.isNotEmpty(operationCompany)) {
			if (!org.springblade.common.utils.CollectionUtils.isNullOrEmpty(companyMap)) {
				plantVO.setCompanyInfo(companyMap.get(operationCompany));
			} else {
				plantVO.setCompanyInfo(new AgentCompanyVO());
			}
		}

		// 组合 CompletableFuture 对象并等待所有任务完成：
		CompletableFuture<Void> allFutures = CompletableFuture.allOf(regionInfoFuture, batteryDataFuture,
			inverterControlFuture, startupByBackstageFuture);
		allFutures.join();

		BeanUtils.copyProperties(plantVO, plantDetailVO);
		return plantDetailVO;
	}

	@Override
	public IPage<PlantListVO> queryPlantBySelfV2(AppVO appVO, Query query) {
		BladeUser user = AuthUtil.getUser();
		String deptId = inspectInnerRole(user);
		query.setDescs(DatabaseFieldConstant.CREATE_TIME);
		PlantVO queryPlant = new PlantVO();
		queryPlant.setCreateUser(user.getUserId());
		queryPlant.setDeptId(deptId);
		queryPlant.setBelongingScene(appVO.getBelongingScene());
		queryPlant.setEnergySystemId(appVO.getEnergySystemId());
		IPage<PlantVO> page = Condition.getPage(query);
		// 用户类型
		String userType = StatusDisplayUtil.getRoleType(user.getRoleName(), user.getDeptId());
		IPage<PlantVO> plantVOIPage = plantService.selectPlantPage(page, queryPlant, userType);
		if (CollectionUtils.isEmpty(plantVOIPage.getRecords())) {
			return new Page<>();
		}
		List<PlantVO> plantVOList = plantVOIPage.getRecords();

		List<Long> plantIdList = new ArrayList<>();
		List<String> regionCodeList = new ArrayList<>();
		List<PlantListVO> plantListVOArrayList = new ArrayList<>();
		List<Device24Entity> device24EntityList = new ArrayList<>();
		List<WifiStickPlantEntity> wifiStickPlantEntities = new ArrayList<>();
		List<QueryCondition> batteryDataQueryList = new ArrayList<>();

		for (PlantVO plantVO : plantVOList) {
			QueryCondition queryCondition = new QueryCondition();
			queryCondition.setPlantId(plantVO.getId());
			plantIdList.add(plantVO.getId());
			regionCodeList.addAll(Arrays.asList(plantVO.getCountryCode(), plantVO.getProvinceCode(),
				plantVO.getCityCode(), plantVO.getCountyCode()));
			batteryDataQueryList.add(queryCondition);

			Device24Entity device24Entity = new Device24Entity();
			device24Entity.setPlantId(plantVO.getId());
			device24Entity.setDeviceSerialNumber(plantVO.getDeviceSerialNumber());
			device24EntityList.add(device24Entity);

			WifiStickPlantEntity wifiStickPlant = new WifiStickPlantEntity();
			wifiStickPlant.setPlantId(plantVO.getId());
			wifiStickPlant.setDeviceSerialNumber(plantVO.getDeviceSerialNumber());
			wifiStickPlantEntities.add(wifiStickPlant);
		}
		this.getDeviceSerialNumber(plantIdList, plantVOList);
		CompletableFuture<Void> batteryDataFuture =
			CompletableFuture.runAsync(() -> getBatteryData(batteryDataQueryList, plantVOList));
		CompletableFuture<Void> inverterControlFuture =
			CompletableFuture.runAsync(() -> setInverterControl(plantVOList, device24EntityList));
		CompletableFuture<Void> startupByBackstageFuture =
			CompletableFuture.runAsync(() -> setStartupByBackstage(plantVOList, wifiStickPlantEntities));
		String currentLanguage = CommonUtil.getCurrentLanguage();
		CompletableFuture<Void> regionInfoFuture;
		if (CommonConstant.CURRENT_LANGUAGE_ZH.equals(currentLanguage)) {
			regionInfoFuture = CompletableFuture.runAsync(() -> setRegionInfo(regionCodeList, plantVOList));
		} else {
			regionInfoFuture = CompletableFuture.runAsync(() -> setRegionInfoEn(regionCodeList, plantVOList));
		}
		// 组合 CompletableFuture 对象并等待所有任务完成：
		CompletableFuture<Void> allFutures = CompletableFuture.allOf(regionInfoFuture, batteryDataFuture,
			inverterControlFuture, startupByBackstageFuture);
		allFutures.join();
		for (PlantVO plantVO : plantVOList) {
			PlantListVO plantListVO = new PlantListVO();
			plantListVO.setPlantName(plantVO.getPlantName());
			plantListVO.setCountryCode(plantVO.getCountryCode());
			plantListVO.setProvinceCode(plantVO.getProvinceCode());
			plantListVO.setCityCode(plantVO.getCityCode());
			plantListVO.setCountyCode(plantVO.getCountyCode());
			plantListVO.setCountryName(plantVO.getCountryName());
			plantListVO.setProvinceName(plantVO.getProvinceName());
			plantListVO.setCityName(plantVO.getCityName());
			plantListVO.setCountyName(plantVO.getCountyName());
			plantListVO.setTimeZone(plantVO.getTimeZone());
			plantListVO.setDetailAddress(plantVO.getDetailAddress());
			// 状态转换
			plantListVO.setPlantStatus(plantVO.getPlantStatus());
			plantListVO.setPowerGeneration(plantVO.getPowerGeneration());
			plantListVO.setLoadCapacity(plantVO.getLoadCapacity());
			plantListVO.setDeviceSerialNumber(plantVO.getDeviceSerialNumber());
			plantListVO.setAddress(plantVO.getAddress());
			plantListVO.setInverterKind(plantVO.getInverterKind());
			plantListVO.setCreateUserAccount(plantVO.getCreateUserAccount());
			plantListVO.setUpdateUserAccount(plantVO.getUpdateUserAccount());
			plantListVO.setId(plantVO.getId());
			plantListVO.setCreateTime(plantVO.getCreateTime());
			plantListVO.setUpdateTime(plantVO.getUpdateTime());

			plantListVO.setInverterControl(plantVO.getInverterControl());
			plantListVO.setStartupByBackstage(plantVO.getStartupByBackstage());
			plantListVO.setInverterConfigureNetwork(plantVO.getInverterConfigureNetwork());
			plantListVO.setIsParallelMode(plantVO.getIsParallelMode());
			plantListVO.setInverterControlArr(plantVO.getInverterControlArr());
			plantListVO.setDeviceSnArr(plantVO.getDeviceSnArr());
			plantListVO.setStartupByBackstageArr(plantVO.getStartupByBackstageArr());
			plantListVO.setInverterConfigureNetworkArr(plantVO.getInverterConfigureNetworkArr());
			plantListVO.setInverterKindArr(plantVO.getInverterKindArr());
			plantListVO.setInverterDeviceTypeArr(plantVO.getInverterDeviceTypeArr());
			plantListVO.setEnergySystemId(plantVO.getEnergySystemId());
			plantListVO.setBelongingScene(plantVO.getBelongingScene());

			plantListVOArrayList.add(plantListVO);
		}
		IPage<PlantListVO> plantListVOIPage = new Page<>();
		plantListVOIPage.setPages(plantVOIPage.getPages());
		plantListVOIPage.setSize(plantVOIPage.getSize());
		plantListVOIPage.setCurrent(plantVOIPage.getCurrent());
		plantListVOIPage.setTotal(plantVOIPage.getTotal());
		plantListVOIPage.setRecords(plantListVOArrayList);

		return plantListVOIPage;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public R<String> unbindWifiDongle(WifiStickPlantEntity wifiStickPlantEntity) {
		String unBindDeviceSerialNumber = wifiStickPlantEntity.getDeviceSerialNumber();
		// 根据站点id查询已经绑定的wifiStick列表，如果没有绑定，则返回错误；绑定两个时，解绑主机则从机升级为主机、解绑从机则直接解绑
		// 解绑使用逻辑删除，恢复出厂信息标识
		WifiStickPlantEntity queryEntity = new WifiStickPlantEntity();
		queryEntity.setPlantId(wifiStickPlantEntity.getPlantId());
		List<WifiStickPlantEntity> list = wifiStickPlantService.list(Condition.getQueryWrapper(queryEntity));
		if (CollectionUtil.isNotEmpty(list)) {

			//目前只绑定了一台，则直接解绑
			if (list.size() == 1) {
				WifiStickPlantEntity entity = list.get(0);
				if (entity.getDeviceSerialNumber().equals(unBindDeviceSerialNumber)) {
					//解绑操作
					unbindWifiDongle(wifiStickPlantEntity.getPlantId(), wifiStickPlantEntity.getDeviceSerialNumber());
				}
				//两台时则进行判断
			} else if (list.size() == 2) {
				boolean masterFlag = false;
				for (WifiStickPlantEntity wifiStickPlant : list) {
					if (wifiStickPlant.getDeviceSerialNumber().equals(unBindDeviceSerialNumber)) {
						//解绑操作
						unbindWifiDongle(wifiStickPlantEntity.getPlantId(),
							wifiStickPlantEntity.getDeviceSerialNumber());
						// 如果解绑是主机则标记为true
						if (Objects.equals(wifiStickPlant.getParallelDeviceType(),
							BizConstant.CLIENT_INVERTER_MASTER_MODEL)) {
							masterFlag = true;
						}
						wifiStickPlant.setIsDeleted(BladeConstant.DB_IS_DELETED);
					}
				}
				if (masterFlag) {
					for (WifiStickPlantEntity wifiStickPlant : list) {
						if (wifiStickPlant.getIsDeleted().equals(BladeConstant.DB_NOT_DELETED)) {
							//把从机升级为主机
							wifiStickPlant.setParallelDeviceType(BizConstant.CLIENT_INVERTER_MASTER_MODEL);
							wifiStickPlantService.saveOrUpdate(wifiStickPlant);
						}
					}
				}

			}
			// 解除站点的并机状态
			PlantEntity plantEntity = new PlantEntity();
			plantEntity.setId(wifiStickPlantEntity.getPlantId());
			plantEntity.setIsParallelMode(BizConstant.CLIENT_PLANT_PARALLEL_MODE_NO);
			plantEntity.setDeviceNumber(-1);
			plantService.updatePlant(plantEntity);
		} else {
			throw new BusinessException("");
		}
		return R.success("success");
	}

	@Override
	public Boolean inverterParallelEnable(AppVO appVO) {
		Long plantId = appVO.getPlantId();

		WifiStickPlantEntity wifiStickPlantQuery = new WifiStickPlantEntity();
		wifiStickPlantQuery.setPlantId(plantId);

		List<WifiStickPlantEntity> list = wifiStickPlantService.list(Condition.getQueryWrapper(wifiStickPlantQuery));
		if (CollectionUtil.isNotEmpty(list)) {
			if (list.size() == BizConstant.CLIENT_INVERTER_MAX_PARALLEL) {
				return false;
			} else if (list.size() == 1) {
				WifiStickPlantEntity wifiStickPlant = list.get(0);
				DeviceExitFactoryInfoEntity deviceExitFactoryInfoEntity = new DeviceExitFactoryInfoEntity();
				deviceExitFactoryInfoEntity.setDeviceSerialNumber(wifiStickPlant.getDeviceSerialNumber());
				DeviceExitFactoryInfoEntity factoryInfoEntity =
					deviceExitFactoryInfoService.getOne(Condition.getQueryWrapper(deviceExitFactoryInfoEntity));
				if (ObjectUtil.isNotEmpty(factoryInfoEntity)) {
					String value = DictBizCache.getValue(BizConstant.CLIENT_INVERTER_MODEL_SUPPORT_PARALLEL,
						factoryInfoEntity.getDeviceType());
					//判断设备是否支持并机模式(类型必须是可并机类型+目前绑定的机器是主机)
					return ValidationUtil.isNotEmpty(value) && wifiStickPlant.getParallelDeviceType().equals(BizConstant.CLIENT_INVERTER_MASTER_MODEL);
				} else {
					return false;
				}

			}
		}
		return false;
	}

	private void unbindWifiDongle(Long plantId, String deviceSerialNumber) {
		BladeUser user = AuthUtil.getUser();

		//发送mqtt通知设备,智能能量变换器与站点已经解绑
		sendTopicMessageByDeleteInverter(deviceSerialNumber);

		// 将电站下的 出厂设备信息 中 储能包更新为未使用
		List<BatteryMapDeviceEntity> batteryMapDeviceEntities =
			batteryMapDeviceService.queryListByPlantIdAndSn(plantId, deviceSerialNumber);
		if (CollectionUtil.isNotEmpty(batteryMapDeviceEntities)) {
			List<String> batterySerialList =
				batteryMapDeviceEntities.stream().map(BatteryMapDeviceEntity::getBatterySerialNumber).collect(Collectors.toList());
			batteryExitFactoryInfoService.batchUpdate(batterySerialList, BizConstant.NUMBER_ZERO);
		}
		// 将电站下的 出厂设备信息 中 智能能量变换器更新为未使用
		deviceExitFactoryInfoService.batchUpdate(List.of(deviceSerialNumber));
		// 删除 站点 和 智能能量变换器 的关系
		wifiStickPlantService.batchDeleteLogicByPlantIdAndSn(plantId, deviceSerialNumber, user.getAccount());
		// 删除 站点 和 储能包的 关系
		batteryMapDeviceService.batchDeleteLogicByPlantIdAndSn(plantId, deviceSerialNumber, user.getAccount());
		// 删除设备21表数据
		device21Service.deleteByPlantId(plantId, deviceSerialNumber);
		device23Service.deleteByPlantId(plantId, deviceSerialNumber);
		device24Service.deleteByPlantId(plantId, deviceSerialNumber);
		// 删除battery_current_status、device_current_status相关数据
		batteryCurrentStatusService.batchDeleteLogicByPlantIdAndSn(plantId, deviceSerialNumber, user.getAccount());
		deviceCurrentStatusService.batchDeleteLogicByPlantIdAndSn(plantId, deviceSerialNumber, user.getAccount());


		// 给站点记录重要事件
		this.saveImportantEventByUnbindInverter(plantId, deviceSerialNumber, user);
	}

	private void saveImportantEventByUnbindInverter(Long plantId, String deviceSerialNumber, BladeUser user) {
		List<ImportantEventEntity> importantEventEntityArrayList = new ArrayList<>();
		ImportantEventEntity plantImportEvent = new ImportantEventEntity();
		plantImportEvent.setEventType(BizConstant.CLIENT_IMPORTANT_EVENT_TYPE_PLANT);
		plantImportEvent.setPlantId(plantId);
		plantImportEvent.setSerialNumber(deviceSerialNumber);
		plantImportEvent.setEventDate(new Date());
		plantImportEvent.setEventContent("client.guardian.important.event.unbound.inverter");
		plantImportEvent.setEventRemark("The Inverter SN : " + deviceSerialNumber);
		plantImportEvent.setCreateUserAccount(user.getAccount());
		plantImportEvent.setCreateUser(user.getUserId());
		importantEventEntityArrayList.add(plantImportEvent);

		importantEventService.saveBatch(importantEventEntityArrayList);
	}


	private void completionDay(AppReportDetailVO report, LocalDate beginLocalDate, LocalDate endLocalDate,
							   String format) {
		List<AppReportDataVO> dischargeCapacityList = report.getDischargeCapacity();
		int interval;
		if ("dd".equals(format)) {
			interval = beginLocalDate.lengthOfMonth();
		} else {
			long epochDay = endLocalDate.toEpochDay() - beginLocalDate.toEpochDay();
			interval = Long.valueOf(epochDay).intValue();
		}
		DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(format);
		Map<String, BigDecimal> initDaysMap = this.initDaysMap(dateTimeFormatter, interval, beginLocalDate);
		List<AppReportDataVO> resultList = this.setDbToInitMap(initDaysMap, dischargeCapacityList);
		report.setDischargeCapacity(resultList);
		List<AppReportDataVO> powerGenerationList = report.getPowerGeneration();
		report.setPowerGeneration(this.setDbToInitMap(initDaysMap, powerGenerationList));
	}

	private void completionMonth(AppReportDetailVO report) {
		List<AppReportDataVO> dischargeCapacityList = report.getDischargeCapacity();
		Map<String, BigDecimal> initDaysMap = this.initMonthsMap();
		List<AppReportDataVO> resultList = this.getNotExistKeyList(initDaysMap, dischargeCapacityList);
		dischargeCapacityList.addAll(resultList);
		List<AppReportDataVO> powerGenerationList = report.getPowerGeneration();
		powerGenerationList.addAll(this.getNotExistKeyList(initDaysMap, powerGenerationList));
		this.sortList(dischargeCapacityList);
		this.sortList(powerGenerationList);
	}

	private Map<String, BigDecimal> initMonthsMap() {
		Map<String, BigDecimal> initMonthsMap = new HashMap<>();
		BigDecimal zero = new BigDecimal("0.000");
		initMonthsMap.put("01", zero);
		initMonthsMap.put("02", zero);
		initMonthsMap.put("03", zero);
		initMonthsMap.put("04", zero);
		initMonthsMap.put("05", zero);
		initMonthsMap.put("06", zero);
		initMonthsMap.put("07", zero);
		initMonthsMap.put("08", zero);
		initMonthsMap.put("09", zero);
		initMonthsMap.put("10", zero);
		initMonthsMap.put("11", zero);
		initMonthsMap.put("12", zero);
		return initMonthsMap;
	}

	@NotNull
	private List<AppReportDataVO> getNotExistKeyList(Map<String, BigDecimal> initDaysMap,
													 List<AppReportDataVO> dischargeCapacityList) {
		List<AppReportDataVO> resultList = new ArrayList<>();
		for (Map.Entry<String, BigDecimal> entry : initDaysMap.entrySet()) {
			String key = entry.getKey();
			BigDecimal value = entry.getValue();
			boolean existKey = false;
			for (AppReportDataVO vo : dischargeCapacityList) {
				if (key.equals(vo.getKey())) {
					existKey = true;
					break;
				}
			}
			if (!existKey) {
				AppReportDataVO addVo = new AppReportDataVO();
				addVo.setKey(key);
				addVo.setValue(value);
				resultList.add(addVo);
			}
		}
		return resultList;
	}

	private List<AppReportDataVO> setDbToInitMap(Map<String, BigDecimal> initDaysMap,
												 List<AppReportDataVO> dischargeCapacityList) {
		List<AppReportDataVO> resultList = new ArrayList<>();
		for (AppReportDataVO vo : dischargeCapacityList) {
			for (Map.Entry<String, BigDecimal> entry : initDaysMap.entrySet()) {
				String key = entry.getKey();
				if (key.equals(vo.getKey())) {
					entry.setValue(vo.getValue());
					break;
				}
			}
		}
		for (Map.Entry<String, BigDecimal> entry : initDaysMap.entrySet()) {
			String key = entry.getKey();
			BigDecimal value = entry.getValue();
			AppReportDataVO addVo = new AppReportDataVO();
			addVo.setKey(key);
			addVo.setValue(value);
			resultList.add(addVo);
		}

		return resultList;
	}

	private Map<String, BigDecimal> initDaysMap(DateTimeFormatter dateTimeFormatter, int intervalDay,
												LocalDate beginLocalDate) {
		Map<String, BigDecimal> initDaysMap = new LinkedHashMap<>();
		BigDecimal zero = new BigDecimal("0.000");
		for (int i = 0; i < intervalDay; i++) {
			LocalDate localDate = beginLocalDate.plusDays(Integer.toUnsignedLong(i));
			String formatDay = dateTimeFormatter.format(localDate);
			initDaysMap.put(formatDay, zero);
		}
		log.info("initDaysMap : {}", initDaysMap);
		return initDaysMap;
	}


	private AppReportDetailVO getHoursReport(AppVO appVO) throws ParseException {
		// 天报表，每4小时累计统计
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat(DateUtil.PATTERN_DATE);
		Date begin = simpleDateFormat.parse(appVO.getDataScope());//" 00:00:00"
		QueryDeviceLog22Condition query = new QueryDeviceLog22Condition();
		query.setStartDateTime(begin);
		query.setPlantId(appVO.getPlantId());
		query.setDeviceSerialNumber(appVO.getDeviceSerialNumber());
		List<DeviceLog22VO> deviceLog22VoList = deviceLog22ByDorisService.appReportEstimate(query);
		Map<String, BigDecimal> pvDailyChargeMap = this.initHoursMap();
		// 当日总充电量
		Map<String, BigDecimal> chargeMap = this.initHoursMap();
		for (DeviceLog22VO deviceLog22VO : deviceLog22VoList) {
			String appTotalDate = deviceLog22VO.getAppTotalDate();
			BigDecimal appPvTodayEnergyTemp = deviceLog22VO.getPvTodayEnergy();
			if (appPvTodayEnergyTemp == null) {
				appPvTodayEnergyTemp = new BigDecimal(0);
			}
			BigDecimal appBatteryDailyAccumulatedChargeEnergyTemp =
				deviceLog22VO.getAppBatteryAccumulatedChargeEnergy();
			if (appBatteryDailyAccumulatedChargeEnergyTemp == null) {
				appBatteryDailyAccumulatedChargeEnergyTemp = new BigDecimal(0);
			}
			BigDecimal thoundsandAppPvTodayEnergyEnergy = appPvTodayEnergyTemp.divide(new BigDecimal(1000), 4,
				RoundingMode.HALF_UP);
			BigDecimal thoundsandAppBatteryAccumulatedChargeEnergy =
				appBatteryDailyAccumulatedChargeEnergyTemp.divide(new BigDecimal(1000), 4, RoundingMode.HALF_UP);
			if (appTotalDate.compareTo("00:00") > 0 && appTotalDate.compareTo("04:00") <= 0) {
				this.addTimeValue(pvDailyChargeMap, "04:00", thoundsandAppPvTodayEnergyEnergy);
				this.addTimeValue(chargeMap, "04:00", thoundsandAppBatteryAccumulatedChargeEnergy);
			} else if (appTotalDate.compareTo("04:00") > 0 && appTotalDate.compareTo("08:00") <= 0) {
				this.addTimeValue(pvDailyChargeMap, "08:00", thoundsandAppPvTodayEnergyEnergy);
				this.addTimeValue(chargeMap, "08:00", thoundsandAppBatteryAccumulatedChargeEnergy);
			} else if (appTotalDate.compareTo("08:00") > 0 && appTotalDate.compareTo("12:00") <= 0) {
				this.addTimeValue(pvDailyChargeMap, "12:00", thoundsandAppPvTodayEnergyEnergy);
				this.addTimeValue(chargeMap, "12:00", thoundsandAppBatteryAccumulatedChargeEnergy);
			} else if (appTotalDate.compareTo("12:00") > 0 && appTotalDate.compareTo("16:00") <= 0) {
				this.addTimeValue(pvDailyChargeMap, "16:00", thoundsandAppPvTodayEnergyEnergy);
				this.addTimeValue(chargeMap, "16:00", thoundsandAppBatteryAccumulatedChargeEnergy);
			} else if (appTotalDate.compareTo("16:00") > 0 && appTotalDate.compareTo("20:00") <= 0) {
				this.addTimeValue(pvDailyChargeMap, "20:00", thoundsandAppPvTodayEnergyEnergy);
				this.addTimeValue(chargeMap, "20:00", thoundsandAppBatteryAccumulatedChargeEnergy);
			} else if (appTotalDate.compareTo("20:00") > 0 && appTotalDate.compareTo("24:00") <= 0) {
				this.addTimeValue(pvDailyChargeMap, "24:00", thoundsandAppPvTodayEnergyEnergy);
				this.addTimeValue(chargeMap, "24:00", thoundsandAppBatteryAccumulatedChargeEnergy);
			}
		}
		return this.getAppReportDetailVO(pvDailyChargeMap, chargeMap);
	}

	private Map<String, BigDecimal> initHoursMap() {
		Map<String, BigDecimal> chargeMap = new HashMap<>();
		BigDecimal zero = new BigDecimal("0.000");
		chargeMap.put("00:00", zero);
		chargeMap.put("04:00", zero);
		chargeMap.put("08:00", zero);
		chargeMap.put("12:00", zero);
		chargeMap.put("16:00", zero);
		chargeMap.put("20:00", zero);
		chargeMap.put("24:00", zero);
		return chargeMap;
	}

	@NotNull
	private AppReportDetailVO getAppReportDetailVO(Map<String, BigDecimal> pvDailyChargeMap,
												   Map<String, BigDecimal> chargeMap) {
		AppReportDetailVO hourReport = new AppReportDetailVO();
		List<AppReportDataVO> powerGeneration = new ArrayList<>();
		List<AppReportDataVO> pvDailyChargeCapacity = new ArrayList<>();
		for (Map.Entry<String, BigDecimal> entry : pvDailyChargeMap.entrySet()) {
			// 光伏日发电量
			AppReportDataVO pvDailyCharge = new AppReportDataVO();
			pvDailyCharge.setKey(entry.getKey());
			pvDailyCharge.setValue(entry.getValue());
			pvDailyChargeCapacity.add(pvDailyCharge);
		}
		for (Map.Entry<String, BigDecimal> entry : chargeMap.entrySet()) {
			// 总充电量
			AppReportDataVO dailyPower = new AppReportDataVO();
			dailyPower.setKey(entry.getKey());
			dailyPower.setValue(entry.getValue());
			powerGeneration.add(dailyPower);
		}

		hourReport.setPowerGeneration(pvDailyChargeCapacity);
		hourReport.setDischargeCapacity(powerGeneration);
		return hourReport;
	}

	private void sortList(List<AppReportDataVO> list) {
		if (list != null) {
			list.sort(Comparator.comparing(AppReportDataVO::getKey));
		}
	}

	private void addTimeValue(Map<String, BigDecimal> map, String timeKey, BigDecimal addNumber) {
		BigDecimal timeValue = map.get(timeKey);
		if (timeValue == null) {
			timeValue = new BigDecimal(0);
		}
		BigDecimal addResult = timeValue.add(addNumber);
		map.put(timeKey, addResult);
	}

	private AppReportHeaderVO setReportHeaderData(AppVO appVO) {
		AppReportHeaderVO resultAppReportHeaderVO = new AppReportHeaderVO();
		// 查询报表头部数据
		this.getBatteryData(appVO, resultAppReportHeaderVO);
		//查询电网数据
		this.getDeviceImportData(appVO, resultAppReportHeaderVO);

		return resultAppReportHeaderVO;
	}

	private void getBatteryData(AppVO appVO, AppReportHeaderVO resultAppReportHeaderVO) {
		BatteryCurrentStatusEntity queryBatteryCurrentStatusEntity = new BatteryCurrentStatusEntity();
		queryBatteryCurrentStatusEntity.setPlantId(appVO.getPlantId());
		queryBatteryCurrentStatusEntity.setDeviceSerialNumber(appVO.getDeviceSerialNumber());
		List<BatteryCurrentStatusEntity> dbBatteryCurrentStatuList =
			batteryCurrentStatusService.list(Condition.getQueryWrapper(queryBatteryCurrentStatusEntity));
		if (CollectionUtil.isNotEmpty(dbBatteryCurrentStatuList)) {
			BatteryCurrentStatusEntity dbBatteryCurrentStatus = dbBatteryCurrentStatuList.get(0);
			log.info("before getBatteryData deviceSerialNumber : {}", appVO.getDeviceSerialNumber());
			// 数据库中为瓦
			BigDecimal todayEnergyTemp = dbBatteryCurrentStatus.getTodayEnergy();
			log.info("before getBatteryData todayEnergy : {}", todayEnergyTemp);
			resultAppReportHeaderVO.setTodayEnergy(this.getChangeEnergyResult(todayEnergyTemp));
			BigDecimal tatal = dbBatteryCurrentStatus.getTotalEnergy() == null ? new BigDecimal(0) :
				dbBatteryCurrentStatus.getTotalEnergy();
			BigDecimal multiplyTotal = tatal.multiply(BizConstant.THOUSAND);
			log.info("before getBatteryData TotalEnergy : {}", multiplyTotal);
			// 数据库中本来为千瓦
			resultAppReportHeaderVO.setTotalEnergy(this.getChangeEnergyResult(multiplyTotal));
			resultAppReportHeaderVO.setOriginalTotalEnergy(multiplyTotal);
			BigDecimal batteryDailyDischargeEnergy = dbBatteryCurrentStatus.getBatteryDailyDischargeEnergy();
			log.info("before getBatteryData batteryDailyDischarge : {}", batteryDailyDischargeEnergy);
			resultAppReportHeaderVO.setBatteryTodayDischargeEnergy(this.getChangeEnergyResult(batteryDailyDischargeEnergy));
			BigDecimal batteryTotalDischargeEnergy = dbBatteryCurrentStatus.getBatteryAccumulatedDischargeEnergy();
			log.info("before getBatteryData batteryTotalDischarge : {}", batteryTotalDischargeEnergy);
			resultAppReportHeaderVO.setBatteryAccumulatedDischargeEnergy(this.getChangeEnergyResult(batteryTotalDischargeEnergy));
			SimpleDateFormat formatter = new SimpleDateFormat(DateUtil.PATTERN_DATE);
			Date deviceDateTime = dbBatteryCurrentStatus.getDeviceDateTime();
			Date now = new Date();
			// 判断是否为今天,不为今天，则天数据为0
			if (!formatter.format(now).equalsIgnoreCase(formatter.format(deviceDateTime))) {
				resultAppReportHeaderVO.setTodayEnergy(BizConstant.BIGDECIMAL_DEFAULT_VALUE_ONE + BizConstant.UNIT_WH);
				resultAppReportHeaderVO.setBatteryTodayDischargeEnergy(BizConstant.BIGDECIMAL_DEFAULT_VALUE_ONE + BizConstant.UNIT_WH);
			}
		}
	}

	private String getChangeEnergyResult(BigDecimal bigDecimal) {
		BigDecimal temp = bigDecimal == null ? new BigDecimal(0) : bigDecimal;
		String result;
		if (temp.compareTo(BizConstant.THOUSAND) > 0) {
			result =
				temp.divide(BizConstant.THOUSAND, BizConstant.NUMBER_TWO, RoundingMode.HALF_UP) + BizConstant.UNIT_THOUSAND_WH;
		} else {
			result = temp.setScale(1, RoundingMode.HALF_UP) + BizConstant.UNIT_WH;
		}
		log.info("after getBatteryData result : {}", result);
		return result;
	}

	private void getDeviceImportData(AppVO appVO, AppReportHeaderVO resultAppReportHeaderVO) {
		DeviceCurrentStatusEntity queryDeviceCurrentStatusEntity = new DeviceCurrentStatusEntity();
		queryDeviceCurrentStatusEntity.setPlantId(appVO.getPlantId());
		queryDeviceCurrentStatusEntity.setDeviceSerialNumber(appVO.getDeviceSerialNumber());
		List<DeviceCurrentStatusEntity> dbDeviceCurrentStatusList =
			deviceCurrentStatusService.list(Condition.getQueryWrapper(queryDeviceCurrentStatusEntity));
		if (CollectionUtil.isNotEmpty(dbDeviceCurrentStatusList)) {
			DeviceCurrentStatusEntity dbDeviceCurrentStatus = dbDeviceCurrentStatusList.get(0);
			BigDecimal todayImportEnergy = dbDeviceCurrentStatus.getTodayImportEnergy();
			BigDecimal accumulatedEnergyOfPositive = dbDeviceCurrentStatus.getAccumulatedEnergyOfPositive();
			log.info("before getDeviceImportData DeviceSerialNumber : {} ; todayImportEnergy : {} ; " +
					"totalImportEnergy" +
					" " +
					"： {} ", appVO.getDeviceSerialNumber(),
				todayImportEnergy, accumulatedEnergyOfPositive);
			resultAppReportHeaderVO.setTodayImportEnergy(this.getChangeEnergyResult(todayImportEnergy));
			resultAppReportHeaderVO.setTotalImportEnergy(this.getChangeEnergyResult(accumulatedEnergyOfPositive));
			SimpleDateFormat formatter = new SimpleDateFormat(DateUtil.PATTERN_DATE);
			Date deviceDateTime = dbDeviceCurrentStatus.getDeviceDateTime();
			Date now = new Date();
			// 判断是否为今天,不为今天，则天数据为0
			if (!formatter.format(now).equalsIgnoreCase(formatter.format(deviceDateTime))) {
				resultAppReportHeaderVO.setTodayImportEnergy(BizConstant.BIGDECIMAL_DEFAULT_VALUE_ONE + BizConstant.UNIT_WH);
			}

		}
		LambdaQueryWrapper<Device23Entity> queryWrapper = Wrappers.<Device23Entity>query().lambda()
			.eq(Device23Entity::getDeviceSerialNumber, appVO.getDeviceSerialNumber())
			.eq(Device23Entity::getPlantId, appVO.getPlantId());
		Device23Entity device23 = device23Service.getOne(queryWrapper);
		resultAppReportHeaderVO.setHybridWorkMode("");
		if (ObjUtil.isNotNull(device23) && ValidationUtil.isNotEmpty(device23.getHybridWorkMode())) {
			List<DictBiz> inverterMode = dictBizClient.getListByLang(DictConstant.INVERTER_MODE,
				CommonUtil.getCurrentLanguage()).getData();
			DictBiz snj = inverterMode.stream()
				.filter(dict -> Func.equals(dict.getDictKey(), device23.getHybridWorkMode()))
				.filter(dict -> Func.equals(dict.getLanguage(), CommonUtil.getCurrentLanguage()))
				.filter(dict -> Func.equals(dict.getAttribute2(), "snj"))
				.findFirst()
				.orElse(null);

			if (ObjUtil.isNotNull(snj)) {
				resultAppReportHeaderVO.setHybridWorkMode(snj.getDictValue());
			}
			// 查询模式设置表，如果是智能模式，则不显示设备上报的
			LambdaQueryWrapper<DeviceCustomModeEntity> deviceCustomModeWrapper = new LambdaQueryWrapper<>();
			deviceCustomModeWrapper.eq(DeviceCustomModeEntity::getPlantId, appVO.getPlantId())
				.eq(DeviceCustomModeEntity::getDeviceSerialNumber, appVO.getDeviceSerialNumber())
				.eq(DeviceCustomModeEntity::getHybridWorkMode, DictConstant.INVERTER_MODE_AI);
			List<DeviceCustomModeEntity> deviceCustomModeEntities =
				deviceCustomModeService.list(deviceCustomModeWrapper);
			if (CollectionUtil.isNotEmpty(deviceCustomModeEntities)) {
				DictBiz dictBiz =
					inverterMode.stream().filter(p -> DictConstant.INVERTER_MODE_AI.equals(p.getDictKey())).findAny().orElse(new DictBiz());
				resultAppReportHeaderVO.setHybridWorkMode(dictBiz.getDictValue());
			}
		}
	}

	@NotNull
	private AppReportDetailVO getAppReportDetailVO(AppVO appVO, LocalDate beginLocalDate, LocalDate endLocalDate,
												   SimpleDateFormat simpleDateFormat,
												   Function<QueryCondition, List<BatteryEverydayTotalVO>> fun
	) {
		QueryCondition queryCondition = new QueryCondition();
		queryCondition.setStartDateTime(Date.from(beginLocalDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
		queryCondition.setEndDateTime(Date.from(endLocalDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
		queryCondition.setPlantId(appVO.getPlantId());
		queryCondition.setDeviceSerialNumber(appVO.getDeviceSerialNumber());
		List<BatteryEverydayTotalVO> batteryEverydayTotalList = fun.apply(queryCondition);
		AppReportDetailVO dailyReport = new AppReportDetailVO();
		List<AppReportDataVO> powerGeneration = new ArrayList<>();
		List<AppReportDataVO> dischargeCapacity = new ArrayList<>();
		for (BatteryEverydayTotalVO entity : batteryEverydayTotalList) {
			// 充电量
			AppReportDataVO dailyPower = new AppReportDataVO();
			// 放电量
			AppReportDataVO dailyDischarge = new AppReportDataVO();
			if (1 == appVO.getType()) {
				dailyDischarge.setKey(entity.getAppTotalDate().substring(5));
				dailyPower.setKey(entity.getAppTotalDate().substring(5));
			} else if (2 == appVO.getType() || 3 == appVO.getType()) {
				dailyDischarge.setKey(entity.getAppTotalDate());
				dailyPower.setKey(entity.getAppTotalDate());
			} else {
				dailyDischarge.setKey(simpleDateFormat.format(entity.getAppTotalDate()));
				dailyPower.setKey(simpleDateFormat.format(entity.getAppTotalDate()));
			}
			BigDecimal appBatteryAccumulatedChargeEnergyTemp = entity.getAppBatteryAccumulatedChargeEnergy() == null ?
				new BigDecimal(0) : entity.getAppBatteryAccumulatedChargeEnergy();
			BigDecimal appBatteryDailyChargeEnergyTemp = entity.getAppBatteryDailyChargeEnergy() == null ?
				new BigDecimal(0) : entity.getAppBatteryDailyChargeEnergy();
			dailyDischarge.setValue(appBatteryAccumulatedChargeEnergyTemp.divide(new BigDecimal(1000), 4,
				RoundingMode.HALF_UP));
			dailyPower.setValue(appBatteryDailyChargeEnergyTemp.divide(new BigDecimal(1000), 4, RoundingMode.HALF_UP));
			dischargeCapacity.add(dailyDischarge);
			powerGeneration.add(dailyPower);
		}
		dailyReport.setPowerGeneration(powerGeneration);
		dailyReport.setDischargeCapacity(dischargeCapacity);
		return dailyReport;
	}

	@Override
	public AppDeviceDetail queryDeviceDetail(AppVO appVO) {
		AppDeviceDetail appDeviceDetail = new AppDeviceDetail();
		List<AppDeviceInfo> appDeviceInfoList = device21Service.queryAppInverterInfo(appVO.getPlantId(),
			appVO.getDeviceSerialNumber());
		if (CollectionUtil.isNotEmpty(appDeviceInfoList)) {
			AppDeviceInfo appDeviceInfo = appDeviceInfoList.get(0);
			String newQualityQuaranteeYear = appDeviceInfo.getNewQualityQuaranteeYear();
			if (StringUtil.isNotBlank(newQualityQuaranteeYear)) {
				String warrantyStartDate = appDeviceInfo.getWarrantyStartDate();
				String qualityGuaranteeYear = appDeviceInfo.getQualityGuaranteeYear();
				if (ValidationUtil.isNotEmpty(warrantyStartDate) && ValidationUtil.isNotEmpty(qualityGuaranteeYear)) {
					String warrantyDeadline =
						BatteryExitFactoryInfoServiceImpl.deadline(Integer.parseInt(qualityGuaranteeYear),
							warrantyStartDate);
					appDeviceInfo.setNewQualityGuaranteeYearDate(warrantyDeadline);
				}
			}
			appDeviceDetail.setInverterInfo(appDeviceInfo);
		}
		List<AppBatteryCurrentStatusInfo> appBatteryCurrentStatusInfoList =
			batteryCurrentStatusService.queryAppBatteryInfo(appVO.getPlantId(), appVO.getDeviceSerialNumber());
		if (CollectionUtils.isEmpty(appBatteryCurrentStatusInfoList)) {
			return appDeviceDetail;
		}
		AppBatteryCurrentStatusInfo appBatteryCurrentStatusInfo = appBatteryCurrentStatusInfoList.get(0);
		appDeviceDetail.setBatteryInfo(appBatteryCurrentStatusInfo);
		return appDeviceDetail;
	}

	@Override
	public AppDeviceDetail queryDeviceDetailV2(AppVO appVO) {
		Long plantId = appVO.getPlantId();
		String deviceSerialNumber = appVO.getDeviceSerialNumber();
		Integer batteryEnergyStorageNumber = appVO.getBatteryEnergyStorageNumber() == null ? 1 :
			appVO.getBatteryEnergyStorageNumber();
		AppDeviceDetail appDeviceDetail = new AppDeviceDetail();
		// 兼容只扫描了智能能量变换器，没有配网的情形，避免下面关联查询时，没有数据
		this.getDeviceExitFactoryInfo(appVO, appDeviceDetail);
		// 查询设备信息
		BladeUser user = AuthUtil.getUser();
		// 用户类型
		String userType = StatusDisplayUtil.getRoleType(user.getRoleName(), user.getDeptId());
		List<AppDeviceInfo> appDeviceInfoList = device21Service.queryAppInverterInfoV2(plantId, deviceSerialNumber);
		if (CollectionUtil.isNotEmpty(appDeviceInfoList)) {
			AppDeviceInfo appDeviceInfo = appDeviceInfoList.get(0);
			String newQualityGuaranteeYear = appDeviceInfo.getNewQualityGuaranteeYear();
			if (StringUtil.isNotBlank(newQualityGuaranteeYear)) {
				String warrantyStartDate = appDeviceInfo.getWarrantyStartDate();
				String qualityGuaranteeYear = appDeviceInfo.getQualityGuaranteeYear();
				if (ValidationUtil.isNotEmpty(warrantyStartDate) && ValidationUtil.isNotEmpty(qualityGuaranteeYear)) {
					String warrantyDeadline =
						BatteryExitFactoryInfoServiceImpl.deadline(Integer.parseInt(qualityGuaranteeYear),
							warrantyStartDate);
					appDeviceInfo.setNewQualityGuaranteeYearDate(warrantyDeadline);
				}
			}
			//  在线状态转换
			appDeviceInfo.setOnLineStatus(StatusDisplayUtil.plantAndInverterStatusConvert(appDeviceInfo.getOnLineStatus(), userType,
				appDeviceInfo.getExistUserTypeAlarm(), appDeviceInfo.getExistAgentTypeAlarm()));
			appDeviceDetail.setInverterInfo(appDeviceInfo);
		}
		AppDeviceInfo inverterInfo = appDeviceDetail.getInverterInfo();
		if (Objects.isNull(inverterInfo)) {
			log.info("queryDeviceDetailV2 inverterInfo is null");
			return appDeviceDetail;
		}
		AppBatteryCurrentStatusInfo appBatteryCurrentStatusInfo = new AppBatteryCurrentStatusInfo();
		// 从储能包绑定表查询目前已绑定的储能包列表，根据SN列表，查询出厂信息，然后将储能包容量进相加，并统计储能包数量
		BigDecimal bigDecimalTotalRatedCapacity = BigDecimal.valueOf(0);
		List<BatteryMapDeviceEntity> allMapList =
			batteryMapDeviceService.list(Wrappers.<BatteryMapDeviceEntity>lambdaQuery()
				.eq(BatteryMapDeviceEntity::getDeviceSerialNumber, deviceSerialNumber)
				.eq(BatteryMapDeviceEntity::getPlantId, plantId)
				.eq(BatteryMapDeviceEntity::getBatteryEnergyStorageNumber, batteryEnergyStorageNumber));
		if (CollectionUtil.isNotEmpty(allMapList)) {
			List<String> batterySerialNumbers = allMapList.stream()
				.map(BatteryMapDeviceEntity::getBatterySerialNumber)
				.collect(Collectors.toList());
			List<BatteryExitFactoryInfoEntity> batteryExitFactoryList =
				batteryExitFactoryInfoService.queryByBatterySerialNumbers(batterySerialNumbers);
			for (BatteryExitFactoryInfoEntity batteryExitFactoryInfoEntity : batteryExitFactoryList) {
				bigDecimalTotalRatedCapacity =
					bigDecimalTotalRatedCapacity.add(new BigDecimal(batteryExitFactoryInfoEntity.getRatedBatteryEnergy()));
			}
		}
		appBatteryCurrentStatusInfo.setTotalRatedCapacity(bigDecimalTotalRatedCapacity.setScale(2,
			RoundingMode.HALF_UP));
		appBatteryCurrentStatusInfo.setNumberOfBattery(BigDecimal.valueOf(allMapList.size()));
		appDeviceDetail.setBatteryInfo(appBatteryCurrentStatusInfo);

		// 查询储能包信息
		List<AppBatteryCurrentStatusInfo> appBatteryCurrentStatusInfoList =
			batteryCurrentStatusService.queryAppBatteryInfoV2(plantId, deviceSerialNumber);
		if (CollectionUtils.isEmpty(appBatteryCurrentStatusInfoList)) {
			return appDeviceDetail;
		} else {
			appBatteryCurrentStatusInfo = appBatteryCurrentStatusInfoList.get(0);
			appBatteryCurrentStatusInfo.setTotalRatedCapacity(bigDecimalTotalRatedCapacity.setScale(2,
				RoundingMode.HALF_UP));
			appBatteryCurrentStatusInfo.setNumberOfBattery(BigDecimal.valueOf(allMapList.size()));
			appDeviceDetail.setBatteryInfo(appBatteryCurrentStatusInfo);
		}

		return appDeviceDetail;
	}

	@NotNull
	private AppDeviceInfo getDeviceExitFactoryInfo(AppVO appVO, AppDeviceDetail appDeviceDetail) {
		PlantEntity plantEntity = plantService.getById(appVO.getPlantId());
		AppDeviceInfo deviceInfo = new AppDeviceInfo();
		deviceInfo.setPlantName(plantEntity.getPlantName());
		deviceInfo.setInstallDate(plantEntity.getInstallDate());
		LambdaQueryWrapper<DeviceExitFactoryInfoEntity> lambdaQueryWrapper =
			Wrappers.<DeviceExitFactoryInfoEntity>query().lambda()
				.eq(DeviceExitFactoryInfoEntity::getDeviceSerialNumber, appVO.getDeviceSerialNumber());
		List<DeviceExitFactoryInfoEntity> factoryList = deviceExitFactoryInfoService.list(lambdaQueryWrapper);
		if (CollectionUtil.isNotEmpty(factoryList)) {
			DeviceExitFactoryInfoEntity factoryInfoEntity = factoryList.get(0);
			deviceInfo.setDeviceSerialNumber(factoryInfoEntity.getDeviceSerialNumber());
			deviceInfo.setDeviceModel(factoryInfoEntity.getDeviceType());
			deviceInfo.setQualityGuaranteeYear(factoryInfoEntity.getQualityGuaranteeYear());
			deviceInfo.setWarrantyStartDate(factoryInfoEntity.getWarrantyStartDate());
			if (StringUtil.isNotBlank(factoryInfoEntity.getNewQualityGuaranteeYear())) {
				deviceInfo.setNewQualityQuaranteeYear(factoryInfoEntity.getQualityGuaranteeYear());
			}
			if (StringUtil.isNotBlank(factoryInfoEntity.getNewQualityGuaranteeYear())) {
				String warrantyStartDate = factoryInfoEntity.getWarrantyStartDate();
				String qualityGuaranteeYear = factoryInfoEntity.getQualityGuaranteeYear();
				if (ValidationUtil.isNotEmpty(warrantyStartDate) && ValidationUtil.isNotEmpty(qualityGuaranteeYear)) {
					String warrantyDeadline =
						BatteryExitFactoryInfoServiceImpl.deadline(Integer.parseInt(qualityGuaranteeYear),
							warrantyStartDate);
					deviceInfo.setNewQualityGuaranteeYearDate(warrantyDeadline);
				}
			}
		}
		appDeviceDetail.setInverterInfo(deviceInfo);
		return deviceInfo;
	}

	@Override
	public List<AppBatteryExitFactoryInfoVO> queryBatteryExitFactoryList(AppVO appVO) {
		if (appVO.getBatteryEnergyStorageNumber() == null) {
			appVO.setBatteryEnergyStorageNumber(1);
		}
		List<AppBatteryExitFactoryInfoVO> appBatteryExitFactoryInfoVoS =
			batteryExitFactoryInfoService.queryAppBatteryDeviceInfo(appVO);
		for (AppBatteryExitFactoryInfoVO vo : appBatteryExitFactoryInfoVoS) {
			String warrantyStartDate = vo.getWarrantyStartDate();
			String qualityGuaranteeYear = vo.getQualityGuaranteeYear();
			if (StringUtils.isNotEmpty(warrantyStartDate) && StringUtils.isNotEmpty(qualityGuaranteeYear)) {
				// todo 后期需要把质保结束时间放在数据库字段中算好
				String warrantyDeadline =
					BatteryExitFactoryInfoServiceImpl.deadline(Integer.parseInt(qualityGuaranteeYear),
						warrantyStartDate);
				vo.setQualityQuaranteeDate(warrantyDeadline);
			}
		}
		return appBatteryExitFactoryInfoVoS;
	}

	@Override
	public boolean deleteBattery(Long batteryMapDeviceId) {
		if (batteryMapDeviceId == null) {
			return false;
		}
		BladeUser user = AuthUtil.getUser();
		BatteryMapDeviceEntity queryEntity = new BatteryMapDeviceEntity();
		queryEntity.setId(batteryMapDeviceId);
		// 在关联查询出厂设置表
		List<BatteryMapDeviceVO> batteryMapDeviceVOList = batteryMapDeviceService.queryBatteryDeviceInfo(queryEntity);
		if (CollectionUtil.isNotEmpty(batteryMapDeviceVOList)) {
			BatteryMapDeviceVO batteryMapDeviceVO = batteryMapDeviceVOList.get(CommonConstant.NOT_SEALED_ID);
			// 删除储能包后更新储能包出厂设置为 未使用
			LambdaUpdateWrapper<BatteryExitFactoryInfoEntity> update =
				Wrappers.<BatteryExitFactoryInfoEntity>update().lambda()
					.set(BatteryExitFactoryInfoEntity::getStatus, 0)
					.set(BatteryExitFactoryInfoEntity::getUpdateUserAccount, user.getAccount())
					.set(BatteryExitFactoryInfoEntity::getUpdateUser, user.getUserId())
					.eq(BatteryExitFactoryInfoEntity::getBatterySerialNumber,
						batteryMapDeviceVO.getBatterySerialNumber());
			batteryExitFactoryInfoService.update(update);
			PlantEntity updatePlantEntity = new PlantEntity();
			updatePlantEntity.setId(batteryMapDeviceVO.getPlantId());
			updatePlantEntity.setBatteryNumber(-1);
			plantService.updatePlant(updatePlantEntity);
			// 删除储能包更改智能能量变换器质保期限


			// 删除储能包的重要事件
			this.saveBatteryImportantEventByDelete(batteryMapDeviceVO, user);
		}

		return batteryMapDeviceService.deleteLogic(Lists.newArrayList(batteryMapDeviceId));
	}


	@NotNull
	private DeviceCustomModeEntity getDeviceCustomModeEntity(List<Device23Entity> dbResultList) {
		DeviceCustomModeEntity entity = new DeviceCustomModeEntity();
		Device23Entity dbResult = dbResultList.get(0);
		String hybridWorkMode = dbResult.getHybridWorkMode();
		entity.setOnceEveryday(dbResult.getOnceEveryday());
		entity.setChargeStartTime1(dbResult.getChargeStartTime1());
		entity.setChargeEndTime1(dbResult.getChargeEndTime1());
		entity.setDischargeStartTime1(dbResult.getDischargeStartTime1());
		entity.setDischargeEndTime1(dbResult.getDischargeEndTime1());
		entity.setCapacityOfChargerEnd(dbResult.getCapacityOfChargerEnd() == null ? 0 :
			dbResult.getCapacityOfChargerEnd().intValue());
		entity.setCapacityOfDischargerEnd(dbResult.getCapacityOfDischargerEnd() == null ? 0 :
			dbResult.getCapacityOfDischargerEnd().intValue());
		entity.setMaximumChargerPower(dbResult.getMaximumChargerPower() == null ? 0 :
			dbResult.getMaximumChargerPower().intValue());
		entity.setMaximumDischargerPower(dbResult.getMaximumDischargerPower() == null ? 0 :
			dbResult.getMaximumDischargerPower().intValue());
		entity.setHybridWorkMode(hybridWorkMode);
		entity.setChargePowerInTime1HighWord(dbResult.getChargePowerInTime1HighWord() == null ? "0" :
			dbResult.getChargePowerInTime1HighWord());
		entity.setChargeEndSocInTime1(dbResult.getChargeEndSocInTime1() == null ? "0" :
			dbResult.getChargeEndSocInTime1());
		entity.setDischargePowerInTime1HighWord(dbResult.getDischargePowerInTime1HighWord() == null ? "0" :
			dbResult.getDischargePowerInTime1HighWord());
		entity.setDischargeEndSocInTime1(dbResult.getDischargeEndSocInTime1() == null ? "0" :
			dbResult.getDischargeEndSocInTime1());
		return entity;
	}


	@Transactional(rollbackFor = Exception.class)
	@Override
	public R<Boolean> deletePlant(String ids) {
		R r = new R<>();
		String currentLanguage = CommonUtil.getCurrentLanguage();
		if (StringUtil.isBlank(ids)) {
			return R.fail("the plant id cannot be empty.");
		}
		List<Long> longList = Func.toLongList(ids);
		List<PlantEntity> plantEntities = plantService.listByIds(longList);
		if (CollectionUtils.isEmpty(plantEntities)) {
			r.setCode(I18nMsgCode.SKYWORTH_CLIENT_PLANT_100028.getCode());
			r.setMsg(I18nMsgCode.SKYWORTH_CLIENT_PLANT_100028.autoGetMessage(currentLanguage));
			r.setSuccess(false);
			return r;

		} else {
			for (PlantEntity plantEntity : plantEntities) {
				// 如果该id查不到数据，还调用该接口，则提示异常
				if (!longList.contains(plantEntity.getId())) {
					r.setCode(I18nMsgCode.SKYWORTH_CLIENT_PLANT_100028.getCode());
					r.setMsg(I18nMsgCode.SKYWORTH_CLIENT_PLANT_100028.autoGetMessage(currentLanguage));
					r.setSuccess(false);
					return r;
				}
			}
		}

		BladeUser user = AuthUtil.getUser();

		List<ImportantEventEntity> insertList = new ArrayList<>();
		for (Long plantId : longList) {
			ImportantEventEntity importantEventEntity = new ImportantEventEntity();
			importantEventEntity.setEventType(BizConstant.CLIENT_IMPORTANT_EVENT_TYPE_PLANT);
			importantEventEntity.setPlantId(plantId);
			importantEventEntity.setEventContent("client.guardian.important.event.delete.plant");
			importantEventEntity.setEventDate(new Date());
			importantEventEntity.setCreateUserAccount(user.getAccount());
			importantEventEntity.setCreateUser(user.getUserId());
			insertList.add(importantEventEntity);
		}
		importantEventService.saveBatch(insertList);

		// 01.将电站下的 智能能量变换器出厂设备  更新为未使用
		List<WifiStickPlantEntity> wifiStickPlantEntities =
			wifiStickPlantService.queryDeviceSerialNumberList(longList);
		List<String> deviceSerialNumberList = new ArrayList<>();
		for (WifiStickPlantEntity entity : wifiStickPlantEntities) {
			Long plantId = longList.get(BizConstant.NUMBER_ZERO);
			String deviceSerialNumber = entity.getDeviceSerialNumber();
			deviceSerialNumberList.add(deviceSerialNumber);
			//发送mqtt通知设备,智能能量变换器与站点已经解绑
			sendTopicMessageByDeleteInverter(deviceSerialNumber);
			// 删除设备21表数据
			device21Service.deleteByPlantId(plantId, deviceSerialNumber);
			device23Service.deleteByPlantId(plantId, deviceSerialNumber);
			device24Service.deleteByPlantId(plantId, deviceSerialNumber);
			// 删除battery_current_status、device_current_status相关数据
			batteryCurrentStatusService.batchDeleteLogicByPlantIdAndSn(plantId, deviceSerialNumber, user.getAccount());
			deviceCurrentStatusService.batchDeleteLogicByPlantIdAndSn(plantId, deviceSerialNumber, user.getAccount());
		}
		if (!deviceSerialNumberList.isEmpty()) {
			deviceExitFactoryInfoService.batchUpdate(deviceSerialNumberList);
		}

		// 02.将电站下的 出厂设备信息 中 储能包更新为未使用
		List<BatteryMapDeviceEntity> batteryMapDeviceEntities = batteryMapDeviceService.queryListByPlantId(longList);
		if (batteryMapDeviceEntities != null && !batteryMapDeviceEntities.isEmpty()) {
			List<String> batterySerialList =
				batteryMapDeviceEntities.stream().map(BatteryMapDeviceEntity::getBatterySerialNumber).collect(Collectors.toList());
			batteryExitFactoryInfoService.batchUpdate(batterySerialList, BizConstant.NUMBER_ZERO);
		}

		// 删除 站点 和 智能能量变换器 的关系
		wifiStickPlantService.batchDeleteLogicByPlantId(longList, user.getAccount());
		// 删除 站点 和 储能包的 关系
		batteryMapDeviceService.batchDeleteLogicByPlantId(longList, user.getAccount());
		// 删除 站点 和 安全卫士的 关系
		gatewayPlantService.batchDeleteLogicByPlantId(longList, user.getAccount());
		plantService.deleteLogic(longList);

		return R.success("successfully delete.");
	}

	@Override
	public OtaUpdatePackEntity upgrading(Map<String, Object> map) {
		Object currentVersion = map.get("currentVersion");
		Object bigType = map.get("bigType");
		Object smallType = map.get("smallType");
		if (Objects.isNull(currentVersion) || Objects.isNull(bigType) || Objects.isNull(smallType)) {
			throw new BusinessException("client.parameter.error.empty");
		}
		OtaUpdatePackEntity otaUpdatePackEntity =
			otaUpdatePackService.getOne(Wrappers.<OtaUpdatePackEntity>query().lambda().eq(OtaUpdatePackEntity::getSmallType, smallType).eq(OtaUpdatePackEntity::getBigType, bigType).eq(OtaUpdatePackEntity::getCompany, CommonConstant.SKY_WORTH).eq(OtaUpdatePackEntity::getIsDeleted, BizConstant.CHAR_ZERO).eq(OtaUpdatePackEntity::getIsNewVersion, BizConstant.CHAR_ONE).ne(OtaUpdatePackEntity::getVersionNumber, currentVersion)
			);
		if (otaUpdatePackEntity != null) {
			String latestVersion = otaUpdatePackEntity.getVersionNumber();
			if (!latestVersion.equals(currentVersion)) {
				Long businessId = otaUpdatePackEntity.getBusinessId();
				List<AttachmentInfoEntity> attachmentInfoEntityList =
					attachmentInfoService.findByBusinessIdsNoTent(Collections.singletonList(businessId)).getData().get(businessId);
				if (!attachmentInfoEntityList.isEmpty()) {
					otaUpdatePackEntity.setPackCdnUrl(otaUpdatePackEntity.getPackCdnUrl() + CommonConstant.SYMBOL_QUESTION_MARK + attachmentInfoEntityList.get(0).getSasToken());
				}
			} else {
				return null;
			}
		}
		return otaUpdatePackEntity;
	}

	private DeviceCustomModeEntity queryInverterMode(Long plantId, String deviceSerialNumber) {
		LambdaQueryWrapper<Device23Entity> lambdaQueryWrapper = Wrappers.<Device23Entity>query().lambda()
			.eq(Device23Entity::getPlantId, plantId).eq(Device23Entity::getDeviceSerialNumber, deviceSerialNumber);
		List<Device23Entity> dbResultList = device23Service.list(lambdaQueryWrapper);
		if (CollectionUtils.isEmpty(dbResultList)) {
			return new DeviceCustomModeEntity();
		}
		return this.getDeviceCustomModeEntity(dbResultList);
	}

	@Override
	public List<InverterModeVO> inverterList(AppVO appVO) {
		List<WifiStickPlantVO> wifiStickPlantVoS = wifiStickPlantService.queryDeviceCompany(appVO);
		String companyCode = Optional.ofNullable(wifiStickPlantVoS).orElse(new ArrayList<>()).stream().
			map(WifiStickPlantVO::getCompany).findFirst().orElse("");
		R<List<DictBiz>> inverterMode = dictBizClient.getListByLang(DictConstant.INVERTER_MODE,
			CommonUtil.getCurrentLanguage());
		List<DictBiz> dictData = inverterMode.getData();
		List<DictBiz> dictBizList =
			dictData.stream().filter(p -> companyCode.equals(p.getAttribute2()) && p.getLanguage().equals(CommonUtil.getCurrentLanguage())).collect(Collectors.toList());
		// 查询模式设置表
		LambdaQueryWrapper<DeviceCustomModeEntity> deviceCustomModeWrapper = new LambdaQueryWrapper<>();
		deviceCustomModeWrapper.eq(DeviceCustomModeEntity::getPlantId, appVO.getPlantId())
			.eq(DeviceCustomModeEntity::getDeviceSerialNumber, appVO.getDeviceSerialNumber())
			.eq(DeviceCustomModeEntity::getHybridWorkMode, DictConstant.INVERTER_MODE_AI);
		List<DeviceCustomModeEntity> deviceCustomModeEntities = deviceCustomModeService.list(deviceCustomModeWrapper);
		DeviceCustomModeEntity currentMode = queryInverterMode(appVO.getPlantId(), appVO.getDeviceSerialNumber());
		List<InverterModeVO> result = new ArrayList<>();
		if (CollectionUtil.isNotEmpty(deviceCustomModeEntities)) {
			// 如果为智能模式，则不取 设备上报的 模式，直接显示 智能模式（智能模式是由 其他几种模式组合成的 ）
			DeviceCustomModeEntity currentModeEntity = deviceCustomModeEntities.get(0);
			for (DictBiz dictBiz : dictBizList) {
				InverterModeVO vo = new InverterModeVO();
				BeanUtil.copy(dictBiz, vo);
				if (currentModeEntity.getHybridWorkMode().equals(dictBiz.getDictKey())) {
					vo.setCurrentDeviceMode("1");
				}
				result.add(vo);
			}
		} else {
			result = dictBizList.stream().map(p -> {
				InverterModeVO vo = new InverterModeVO();
				BeanUtil.copy(p, vo);
				if (p.getDictKey().equalsIgnoreCase(currentMode.getHybridWorkMode())) {
					vo.setCurrentDeviceMode("1");
				}
				return vo;
			}).collect(Collectors.toList());
		}
		log.info("get inverterList : {}", result);
		return result;
	}

	@Override
	public R<String> editPlant(PlantEntity plantEntity) {
		R<String> r = new R<>();
		String currentLanguage = CommonUtil.getCurrentLanguage();
		if (StringUtil.isBlank(plantEntity.getPlantName()) || StringUtil.isBlank(plantEntity.getCountryCode())
			|| StringUtil.isBlank(plantEntity.getDetailAddress())) {
			r.setCode(I18nMsgCode.SKYWORTH_CLIENT_PLANT_100008.getCode());
			r.setMsg(I18nMsgCode.SKYWORTH_CLIENT_PLANT_100008.autoGetMessage(currentLanguage));
			return r;
		}
		if (plantEntity.getId() == null) {
			r.setCode(I18nMsgCode.SKYWORTH_CLIENT_PLANT_100026.getCode());
			r.setMsg(I18nMsgCode.SKYWORTH_CLIENT_PLANT_100026.autoGetMessage(currentLanguage));
			return r;
		}


		Wrapper<PlantEntity> wrapper = Wrappers.<PlantEntity>lambdaQuery().eq(PlantEntity::getPlantName,
			plantEntity.getPlantName()).eq(
			PlantEntity::getCreateUser, AuthUtil.getUser().getUserId()).ne(
			PlantEntity::getId, plantEntity.getId());
		long count = plantService.count(wrapper);
		if (count != 0) {
			r.setCode(I18nMsgCode.SKYWORTH_CLIENT_PLANT_100001.getCode());
			r.setMsg(StringUtil.format(I18nMsgCode.SKYWORTH_CLIENT_PLANT_100001.autoGetMessage(currentLanguage),
				plantEntity.getPlantName()));
			return r;
		}
		// 根据id查询原有信息
		PlantEntity plantOld = plantService.getById(plantEntity.getId());
		plantOld.setPlantName(plantEntity.getPlantName());
		plantOld.setCountryCode(plantEntity.getCountryCode());
		plantOld.setProvinceCode(plantEntity.getProvinceCode());
		plantOld.setCityCode(plantEntity.getCityCode());
		plantOld.setCountyCode(plantEntity.getCountyCode());
		plantOld.setDetailAddress(plantEntity.getDetailAddress());
		plantOld.setTimeZone(plantEntity.getTimeZone());
		plantOld.setOperationUserId(plantEntity.getOperationUserId());
		plantOld.setOperationCompanyId(plantEntity.getOperationCompanyId());
		plantService.updateById(plantOld);
		return R.data("操作成功");
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public R<Boolean> deleteUser(AppVO appVO) {
		R<Boolean> r = new R<>();
		String currentLanguage = CommonUtil.getCurrentLanguage();
		String verificationType = appVO.getVerificationType();
		String smsTemplate = "";
		String phone = "";
		String emailAddress = "";
		// 邮箱方式删除账号
		if (ValidationUtil.isNotEmpty(verificationType) && BizConstant.CLIENT_ACCOUNT_VERIFICATION_TYPE_EMAIL.equals(verificationType)) {
			// 获取验证码
			emailAddress = appVO.getEmail();
			String code = appVO.getVerificationCode();
			String redisCode =
				bladeRedis.get(CacheNames.CAPTCHA_KEY + EmailCacheNamesEnum.EMAIL_DELETE_CAPTCHA_SUFFIX.getCacheNameSuffix() + emailAddress);
			// 判断验证码
			if (StringUtil.isBlank(redisCode) || StringUtil.isBlank(code)) {
				r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100006.getCode());
				r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100006.autoGetMessage(currentLanguage));
				return r;
			}
			if (!StringUtil.equalsIgnoreCase(code, redisCode)) {
				r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100006.getCode());
				r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100006.autoGetMessage(currentLanguage));
				return r;
			}
			// verificationType为空或者1就是手机号删除账户
		} else if (ValidationUtil.isEmpty(verificationType) || (ValidationUtil.isNotEmpty(verificationType) && BizConstant.CLIENT_ACCOUNT_VERIFICATION_TYPE_PHONE.equals(verificationType))) {
			String phoneDiallingCode = appVO.getPhoneDiallingCode();
			if (StringUtil.isBlank(phoneDiallingCode)) {
				r.setCode(I18nMsgCode.SKYWORTH_RESOURCE_SMS_100103.getCode());
				r.setMsg(I18nMsgCode.SKYWORTH_RESOURCE_SMS_100103.autoGetMessage(currentLanguage));
				return r;
			}
			String trim = phoneDiallingCode.trim();

			if (!trim.contains("+")) {
				trim = "+" + trim;
			}
			phone = trim + appVO.getPhone();
			String code = appVO.getVerificationCode();
			// 获取验证码

			String getTemplateLanguage = CommonUtil.getCurrentLanguage();
			smsTemplate = SmsEnum.getSmsTemplate(SmsEnum.FIVE.getSmsType(), getTemplateLanguage);
			log.info("deleteUser get smsTemplate : {} , phone : {} ", smsTemplate, phone);
			String smsCode = bladeRedis.get(SmsConstant.CAPTCHA_KEY + smsTemplate + StringPool.COLON + phone);
			log.info("deleteUser get redis smsCode : {}", smsCode);
			if (StringUtil.isBlank(smsCode) || StringUtil.isBlank(code)) {
				r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100006.getCode());
				r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100006.autoGetMessage(currentLanguage));
				return r;
			}
			if (!StringUtil.equalsIgnoreCase(code, smsCode)) {
				r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100006.getCode());
				r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100006.autoGetMessage(currentLanguage));
				return r;
			}
		}

		BladeUser user = AuthUtil.getUser();
		List<WifiStickPlantEntity> wifiStickPlantEntities = wifiStickPlantService.queryOwnerData(user.getUserId());
		List<BatteryMapDeviceEntity> batteryMapDeviceEntities =
			batteryMapDeviceService.queryOwnerData(user.getUserId());
//		List<GatewayPlantEntity> guardianPlantEntities = gatewayPlantService.queryOwnerData(user.getUserId());

		PlantEntity updatePlant = new PlantEntity();
		updatePlant.setIsDeleted(1);
		updatePlant.setCreateUser(user.getUserId());
		updatePlant.setUpdateUser(user.getUserId());
		updatePlant.setUpdateUserAccount(user.getAccount());

		WifiStickPlantEntity updateWifi = new WifiStickPlantEntity();
		updateWifi.setIsDeleted(1);
		updateWifi.setCreateUser(user.getUserId());
		updateWifi.setUpdateUser(user.getUserId());
		updateWifi.setUpdateUserAccount(user.getAccount());

		BatteryMapDeviceEntity updateBatteryMap = new BatteryMapDeviceEntity();
		updateBatteryMap.setIsDeleted(1);
		updateBatteryMap.setCreateUser(user.getUserId());
		updateBatteryMap.setUpdateUser(user.getUserId());
		updateBatteryMap.setUpdateUserAccount(user.getAccount());

		GatewayPlantEntity updateGuardianPlantEntity = new GatewayPlantEntity();
		updateGuardianPlantEntity.setIsDeleted(1);
		updateGuardianPlantEntity.setCreateUser(user.getUserId());
		updateGuardianPlantEntity.setUpdateUser(user.getUserId());
		updateGuardianPlantEntity.setUpdateUserAccount(user.getAccount());


		// 将 智能能量变换器 、储能包、安全卫士 出厂设置 状态改为 未使用
		if (CollectionUtil.isNotEmpty(wifiStickPlantEntities)) {
			List<String> deviceSerialList =
				wifiStickPlantEntities.stream().map(WifiStickPlantEntity::getDeviceSerialNumber).collect(Collectors.toList());
			deviceExitFactoryInfoService.batchUpdate(deviceSerialList);
			// 删除设备21表数据
			for (WifiStickPlantEntity update : wifiStickPlantEntities) {
				Long plantId = update.getPlantId();
				String deviceSerialNumber = update.getDeviceSerialNumber();
				device21Service.deleteByPlantId(plantId, deviceSerialNumber);
				device23Service.deleteByPlantId(plantId, deviceSerialNumber);
				device24Service.deleteByPlantId(plantId, deviceSerialNumber);
				//发送mqtt通知设备,智能能量变换器与站点已经解绑
				sendTopicMessageByDeleteInverter(deviceSerialNumber);
				// 删除battery_current_status、device_current_status相关数据
				batteryCurrentStatusService.batchDeleteLogicByPlantIdAndSn(plantId, deviceSerialNumber,
					user.getAccount());
				deviceCurrentStatusService.batchDeleteLogicByPlantIdAndSn(plantId, deviceSerialNumber,
					user.getAccount());
			}
		}
		if (CollectionUtil.isNotEmpty(batteryMapDeviceEntities)) {
			List<String> batterySerialList =
				batteryMapDeviceEntities.stream().map(BatteryMapDeviceEntity::getBatterySerialNumber).collect(Collectors.toList());
			batteryExitFactoryInfoService.batchUpdate(batterySerialList, BizConstant.NUMBER_ZERO);
		}
		userClient.deleteUserById(user.getUserId());
		// 删除当前用户下 智能能量变换器 和 站点关系
		wifiStickPlantService.updateDataByCondition(updateWifi);
		// 删除储能包 和 站点关系
		batteryMapDeviceService.updateDataByCondition(updateBatteryMap);
		// 删除安全卫士 和 站点关系
		gatewayPlantService.updateDataByCondition(updateGuardianPlantEntity);
		// 删除当前用户下所有站点
		plantService.updatePlant(updatePlant);

		if (ValidationUtil.isNotEmpty(verificationType) && BizConstant.CLIENT_ACCOUNT_VERIFICATION_TYPE_EMAIL.equals(verificationType)) {
			bladeRedis.del(CacheNames.CAPTCHA_KEY + EmailCacheNamesEnum.EMAIL_DELETE_CAPTCHA_SUFFIX.getCacheNameSuffix() + emailAddress);
		} else if (ValidationUtil.isEmpty(verificationType) || BizConstant.CLIENT_ACCOUNT_VERIFICATION_TYPE_PHONE.equals(verificationType)) {
			bladeRedis.del(SmsConstant.CAPTCHA_KEY + smsTemplate + StringPool.COLON + phone);
		}
		List<cn.hutool.json.JSONObject> agentList = new ArrayList<>();
		cn.hutool.json.JSONObject object = new cn.hutool.json.JSONObject();
		object.set("userId", user.getUserId());
		object.set("userType", "constructor");
		agentList.add(object);
		agentClient.cleanUpAgentUser(agentList);

		return R.data(true);
	}

	private void sendTopicMessageByDeleteInverter(String deviceSerialNumber) {
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("deviceSn", deviceSerialNumber);
		jsonObject.put("topic", Constants.UNBIND_INVERTER);
		deviceIssueBiz.dataIssueToDevice(jsonObject);
	}

	@Override
	public Long addGuardian(GuardianPlantEntity guardianPlantEntity) {
		PlantEntity byId = plantService.getById(guardianPlantEntity.getPlantId());
		if (byId == null) {
			return -200L;
		}

		String securityGuardSerialNumber = BizConstant.CHAR_ZERO + guardianPlantEntity.getSecurityGuardSerialNumber();
		guardianPlantEntity.setSecurityGuardSerialNumber(securityGuardSerialNumber);

		LambdaQueryWrapper<GuardianExitFactoryInfoEntity> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(GuardianExitFactoryInfoEntity::getSecurityGuardSerialNumber, securityGuardSerialNumber);
		GuardianExitFactoryInfoEntity guardianExitFactoryInfoEntity =
			guardianExitFactoryInfoService.getOne(queryWrapper);
		if (ObjectUtils.isEmpty(guardianExitFactoryInfoEntity)) {
			return -100L;
		} else {
			Integer status = guardianExitFactoryInfoEntity.getStatus();
			if (BizConstant.CLIENT_EXIT_INFO_STATUS_USE.equals(status)) {
				return -300L;
			}
		}

		LambdaQueryWrapper<GuardianPlantEntity> queryWrapperGuardianPlantEntity = new LambdaQueryWrapper<>();
		queryWrapperGuardianPlantEntity.eq(GuardianPlantEntity::getSecurityGuardSerialNumber,
			securityGuardSerialNumber);
		GuardianPlantEntity guardianPlantServiceOne = guardianPlantService.getOne(queryWrapperGuardianPlantEntity);
		if (ObjectUtil.isNotEmpty(guardianPlantServiceOne)) {
			return -300L;
		}

		BladeUser user = AuthUtil.getUser();
		// 如果未扫描过安全卫士，则保存
		guardianPlantEntity.setCreateUserAccount(user.getAccount());
		guardianPlantEntity.setCreateUser(user.getUserId());
		guardianPlantEntity.setSecurityGuardStatus(BizConstant.CLIENT_GUARDIAN_STATUS_OFFLINE);
		guardianPlantEntity.setGatePositionStatus(BizConstant.CLIENT_GUARDIAN_GATE_POSITION_STATUS_CLOSE);
		guardianPlantEntity.setTimingTypePower(BizConstant.CLIENT_GUARDIAN_TIMING_TYPE_CLOSE);
		guardianPlantEntity.setTimingTypeGate(BizConstant.CLIENT_GUARDIAN_TIMING_TYPE_CLOSE);
		guardianPlantEntity.setLockSet(BizConstant.CLIENT_GUARDIAN_LOCK_SET_UNLOCK);
		guardianPlantEntity.setRecloseSet(BizConstant.CLIENT_GUARDIAN_RECLOSE_SET_CLOSE);
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtil.PATTERN_DATE);
		guardianPlantEntity.setPartitionDate(LocalDate.now().format(formatter));
		guardianPlantEntity.setDeviceType(guardianExitFactoryInfoEntity.getDeviceType());
		guardianPlantService.save(guardianPlantEntity);

		PlantEntity updatePlantEntity = new PlantEntity();
		updatePlantEntity.setId(guardianPlantEntity.getPlantId());
		updatePlantEntity.setSecurityGuardNumber(1);
		updatePlantEntity.setUpdateTime(new Date());
		updatePlantEntity.setUpdateUserAccount(user.getAccount());
		updatePlantEntity.setUpdateUser(user.getUserId());
		plantService.updatePlant(updatePlantEntity);

		PlantEntity plantById = plantService.getById(guardianPlantEntity.getPlantId());
		this.saveGuardianImportantByAdd(guardianPlantEntity, user, plantById);

		//绑定时同步修改质保日期
		LocalDate exitFactoryDate = guardianExitFactoryInfoEntity.getExitFactoryDate();
		//判断出厂时间是否是三个月前，是的话质保开始日期设置为出厂日期，否则为当前日期
		boolean flag = BatteryExitFactoryInfoServiceImpl.diffDate(java.sql.Date.valueOf(exitFactoryDate), 3);
		LocalDate warrantyStartDate = LocalDate.now();
		if (flag) {
			warrantyStartDate = exitFactoryDate;
		}
		LambdaUpdateWrapper<GuardianExitFactoryInfoEntity> update =
			Wrappers.<GuardianExitFactoryInfoEntity>update().lambda()
				.set(GuardianExitFactoryInfoEntity::getStatus, BizConstant.NUMBER_ONE)
				.set(GuardianExitFactoryInfoEntity::getUpdateUserAccount, user.getAccount())
				.set(GuardianExitFactoryInfoEntity::getUpdateUser, user.getUserId())
				.set(GuardianExitFactoryInfoEntity::getActivationDate, LocalDate.now())
				.set(GuardianExitFactoryInfoEntity::getWarrantyStartDate, warrantyStartDate)
				.eq(GuardianExitFactoryInfoEntity::getSecurityGuardSerialNumber, securityGuardSerialNumber);
		guardianExitFactoryInfoService.update(update);
		updateAssociatedData(securityGuardSerialNumber, guardianPlantEntity.getPlantId());
		return guardianPlantEntity.getId();
	}

	@Override
	public AppDeviceOverviewInfo queryAppDeviceOverviewInfo(AppVO appVO) {
		AppDeviceOverviewInfo appDeviceOverviewInfo = new AppDeviceOverviewInfo();
		List<AppDeviceOverview> overviewArrayList = new ArrayList<>();
		String currentLanguage = CommonUtil.getCurrentLanguage();
		this.queryBatteryNumber(appVO, currentLanguage, overviewArrayList);
		//01.查询安全卫士
		LambdaQueryWrapper<GatewayPlantEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
		lambdaQueryWrapper.eq(GatewayPlantEntity::getPlantId, appVO.getPlantId());
		lambdaQueryWrapper.eq(GatewayPlantEntity::getDeviceType, BizConstant.CLIENT_IMPORTANT_EVENT_TYPE_GUARDIAN);
		List<GatewayPlantEntity> entityList = gatewayPlantService.list(lambdaQueryWrapper);

		if (CollectionUtil.isNotEmpty(entityList)) {
			List<DictBiz> list = DictBizCache.getList(DictBizCodeEnum.CLIENT_LAZZEN_GUARDIAN_TYPE.getDictCode());
			Map<String, String> stringMap = list.stream().collect(Collectors.toMap(DictBiz::getDictKey,
				DictBiz::getDictValue));
			for (GatewayPlantEntity entity : entityList) {
				AppDeviceOverview appDeviceOverviewGuardian = new AppDeviceOverview();
				appDeviceOverviewGuardian.setPlantId(appVO.getPlantId());
				appDeviceOverviewGuardian.setDeviceCategory(BizConstant.CLIENT_IMPORTANT_EVENT_TYPE_GUARDIAN);
				appDeviceOverviewGuardian.setSerialNumber(entity.getGatewayUniqueNumber());
				// 从doris获取
				DeviceGuardProductInfoEntity productInfoServiceOne =
					deviceGuardProductInfoService.getOne(Wrappers.lambdaQuery(DeviceGuardProductInfoEntity.class)
						.eq(DeviceGuardProductInfoEntity::getGatewayUniqueNumber, entity.getGatewayUniqueNumber()));
				if (!ObjectUtils.isEmpty(productInfoServiceOne)) {
					appDeviceOverviewGuardian.setDeviceModel(stringMap.getOrDefault(productInfoServiceOne.getProductStandard(), productInfoServiceOne.getProductStandard()));
				}
				appDeviceOverviewGuardian.setDeviceStatus(entity.getStatus());
				overviewArrayList.add(appDeviceOverviewGuardian);
			}
		}
		//01.查询全屋备电箱
		LambdaQueryWrapper<GatewayPlantEntity> boxWrapper = new LambdaQueryWrapper<>();
		boxWrapper.eq(GatewayPlantEntity::getPlantId, appVO.getPlantId());
		boxWrapper.eq(GatewayPlantEntity::getDeviceType, BizConstant.CLIENT_IMPORTANT_EVENT_TYPE_BACKUP_BOX);
		List<GatewayPlantEntity> boxList = gatewayPlantService.list(boxWrapper);

		if (CollectionUtil.isNotEmpty(boxList)) {
			List<DictBiz> list = DictBizCache.getList(DictBizCodeEnum.CLIENT_LAZZEN_GUARDIAN_TYPE.getDictCode());
			Map<String, String> stringMap = list.stream().collect(Collectors.toMap(DictBiz::getDictKey,
				DictBiz::getDictValue));
			for (GatewayPlantEntity entity : boxList) {
				AppDeviceOverview appDeviceOverviewBox = new AppDeviceOverview();
				appDeviceOverviewBox.setPlantId(appVO.getPlantId());
				appDeviceOverviewBox.setDeviceCategory(BizConstant.CLIENT_IMPORTANT_EVENT_TYPE_BACKUP_BOX);
				appDeviceOverviewBox.setSerialNumber(entity.getGatewayUniqueNumber());
				// 从doris获取
				DeviceGuardProductInfoEntity productInfoServiceOne =
					deviceGuardProductInfoService.getOne(Wrappers.lambdaQuery(DeviceGuardProductInfoEntity.class)
						.eq(DeviceGuardProductInfoEntity::getGatewayUniqueNumber, entity.getGatewayUniqueNumber()));
				if (!ObjectUtils.isEmpty(productInfoServiceOne)) {
					appDeviceOverviewBox.setDeviceModel(stringMap.getOrDefault(productInfoServiceOne.getProductStandard(), productInfoServiceOne.getProductStandard()));
				}
				appDeviceOverviewBox.setDeviceStatus(entity.getStatus());
				overviewArrayList.add(appDeviceOverviewBox);
			}
		}


		//02.智能能量变换器、储能包包 状态和数量，通过设备mqtt直接获取了
		//03.后续DTU和其他产品，直接往overviewArrayList添加即可

		appDeviceOverviewInfo.setAppDeviceOverviewList(overviewArrayList);
		return appDeviceOverviewInfo;
	}

	private void queryBatteryNumber(AppVO appVO, String currentLanguage,
									List<AppDeviceOverview> appBatteryOverviewList) {
		LambdaQueryWrapper<WifiStickPlantEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
		lambdaQueryWrapper.eq(WifiStickPlantEntity::getPlantId, appVO.getPlantId());
		List<WifiStickPlantEntity> wifiStickPlantEntityList = wifiStickPlantService.list(lambdaQueryWrapper);
		if (CollectionUtil.isEmpty(wifiStickPlantEntityList)) {
			log.info("wifiStickPlantService is null : {}", appVO.getPlantId());
			return;
		}
		WifiStickPlantEntity wifiStickPlantEntity = wifiStickPlantEntityList.get(0);
		LambdaQueryWrapper<DeviceExitFactoryInfoEntity> queryDeviceExitFactoryInfoEntity =
			Wrappers.<DeviceExitFactoryInfoEntity>lambdaQuery()
				.eq(DeviceExitFactoryInfoEntity::getDeviceSerialNumber, wifiStickPlantEntity.getDeviceSerialNumber());
		List<DeviceExitFactoryInfoEntity> dbDeviceExitFactoryList =
			deviceExitFactoryInfoService.list(queryDeviceExitFactoryInfoEntity);
		if (CollectionUtil.isEmpty(dbDeviceExitFactoryList)) {
			log.info("device exit factory is null : {}", appVO.getPlantId());
			return;
		}
		DeviceExitFactoryInfoEntity factoryInfoEntity = dbDeviceExitFactoryList.get(0);
		List<DictBiz> dictBizList = getEnergyStorage2ByDeviceModel(appVO, factoryInfoEntity);
		if (CollectionUtil.isEmpty(dictBizList)) {
			AppDeviceOverview appDeviceOverview = new AppDeviceOverview();
			appDeviceOverview.setBatteryEnergyStorageName("储能");
			appDeviceOverview.setDeviceSerialNumber(factoryInfoEntity.getDeviceSerialNumber());
			appDeviceOverview.setPlantId(appVO.getPlantId());
			appDeviceOverview.setBatteryEnergyStorageNumber(1);
			appDeviceOverview.setDeviceCategory(BizConstant.CLIENT_IMPORTANT_EVENT_TYPE_BATTERY);
			appBatteryOverviewList.add(appDeviceOverview);
		} else {
			List<String> attribute1List = dictBizList.stream().filter(p -> currentLanguage.equals(p.getLanguage())
					&& p.getDictKey().equals(factoryInfoEntity.getDeviceType()) && StringUtil.isNotBlank(p.getAttribute1()))
				.map(DictBiz::getAttribute1).collect(Collectors.toList());
			String attribute1 = attribute1List.get(0);
			String[] nameArray = attribute1.split(CommonConstant.SYMBOL_COMMA);
			for (int i = 1; i <= nameArray.length; i++) {
				AppDeviceOverview appDeviceOverview = new AppDeviceOverview();
				appDeviceOverview.setBatteryEnergyStorageName(nameArray[i - 1]);
				appDeviceOverview.setDeviceSerialNumber(factoryInfoEntity.getDeviceSerialNumber());
				appDeviceOverview.setPlantId(appVO.getPlantId());
				appDeviceOverview.setBatteryEnergyStorageNumber(i);
				appDeviceOverview.setDeviceCategory(BizConstant.CLIENT_IMPORTANT_EVENT_TYPE_BATTERY);
				appBatteryOverviewList.add(appDeviceOverview);
			}
		}
		List<BatteryMapDeviceEntity> dbBatteryMapList =
			batteryMapDeviceService.list(Wrappers.<BatteryMapDeviceEntity>lambdaQuery()
				.eq(BatteryMapDeviceEntity::getDeviceSerialNumber, factoryInfoEntity.getDeviceSerialNumber())
				.eq(BatteryMapDeviceEntity::getPlantId, appVO.getPlantId()));
		if (CollectionUtil.isNotEmpty(dbBatteryMapList)) {
			List<String> batterySerialNumbers = dbBatteryMapList.stream()
				.map(BatteryMapDeviceEntity::getBatterySerialNumber)
				.collect(Collectors.toList());
			List<BatteryExitFactoryInfoEntity> dbBatteryExitFactoryList =
				batteryExitFactoryInfoService.queryByBatterySerialNumbers(batterySerialNumbers);
			Map<String, BatteryExitFactoryInfoEntity> batterySerialFactoryMap = dbBatteryExitFactoryList.stream()
				.collect(Collectors.toMap(BatteryExitFactoryInfoEntity::getBatterySerialNumber, Function.identity(),
					(a, b) -> a));

			for (int i = 1; i <= appBatteryOverviewList.size(); i++) {
				AppDeviceOverview appBatteryOverview = appBatteryOverviewList.get(i - 1);
				BigDecimal bigDecimalTotalRatedCapacity = BigDecimal.valueOf(0);
				StringBuilder sb = new StringBuilder();
				for (BatteryMapDeviceEntity dbBatteryMap : dbBatteryMapList) {
					if (dbBatteryMap.getBatteryEnergyStorageNumber().equals(i)) {
						sb.append(dbBatteryMap.getBatterySerialNumber()).append(CommonConstant.SYMBOL_COMMA);
						BatteryExitFactoryInfoEntity batteryExitFactoryInfoEntity =
							batterySerialFactoryMap.get(dbBatteryMap.getBatterySerialNumber());
						bigDecimalTotalRatedCapacity =
							bigDecimalTotalRatedCapacity.add(new BigDecimal(batteryExitFactoryInfoEntity.getRatedBatteryEnergy()));
					}
				}
				appBatteryOverview.setTotalRatedCapacity(bigDecimalTotalRatedCapacity.setScale(2,
					RoundingMode.HALF_UP));
				appBatteryOverview.setBatterySerialNumber(sb.toString());
			}


		}
	}

	private List<DictBiz> getEnergyStorage2ByDeviceModel(AppVO appVO, DeviceExitFactoryInfoEntity factoryInfoEntity) {
		R<List<DictBiz>> resultDict =
			dictBizClient.getListAllLang(DictBizCodeEnum.ENERGY_STORAGE_2_BY_DEVICE_MODEL.getDictCode());
		List<DictBiz> dictBizList = resultDict.getData();
		if (CollectionUtil.isEmpty(dictBizList)) {
			log.info("energy_storage_2_by_device_model is null : {}", appVO.getDeviceSerialNumber());
			return null;
		}
		Set<String> deviceModelSet = dictBizList.stream().map(DictBiz::getDictKey).collect(Collectors.toSet());
		if (!deviceModelSet.contains(factoryInfoEntity.getDeviceType())) {
			log.info("device exit factory model is not in dict : {} ,  device model : {}",
				appVO.getDeviceSerialNumber(), factoryInfoEntity.getDeviceType());
			return null;
		}
		log.info("needEnergyStorage2ByDeviceModel is true");
		return dictBizList;
	}

	@Override
	public JSONObject getPlantCount() {
		JSONObject jsonObject = new JSONObject();
		Query query = new Query();
		query.setCurrent(1);
		query.setSize(500);
		BladeUser user = AuthUtil.getUser();
		String deptId = inspectInnerRole(user);
		query.setDescs(DatabaseFieldConstant.CREATE_TIME);
		PlantVO queryPlant = new PlantVO();
		queryPlant.setCreateUser(user.getUserId());
		queryPlant.setDeptId(deptId);
		IPage<PlantVO> page = Condition.getPage(query);
		// 用户类型
		String userType = StatusDisplayUtil.getRoleType(user.getRoleName(), user.getDeptId());
		IPage<PlantVO> plantVOIPage = plantService.selectPlantPage(page, queryPlant, userType);
		long total = plantVOIPage.getTotal();

		jsonObject.put("plantCount", total);
		return jsonObject;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Long wifiDongleAddV2(WifiStickPlantEntity wifiStickPlantEntity) {
		String deviceSerialNumber = wifiStickPlantEntity.getDeviceSerialNumber();
		Integer belongingScene = wifiStickPlantEntity.getBelongingScene();
		DeviceExitFactoryInfoEntity newInverterInfo =
			deviceExitFactoryInfoService.getOne(Wrappers.lambdaQuery(DeviceExitFactoryInfoEntity.class).eq(DeviceExitFactoryInfoEntity::getDeviceSerialNumber, deviceSerialNumber));
		// 出场信息不存在
		if (newInverterInfo == null) {
			return -100L;
		}
		// 出场信息设备的归属和传入的归属不一致
		if (!belongingScene.equals(newInverterInfo.getBelongingScene())) {
			return -500L;
		}
		// 根据站点id，查出站点下的所有智能能量变换器以及它的型号，如果新添加的智能能量变换器型号与旧的智能能量变换器不同，则不能作为并机进行添加
		Long plantId = wifiStickPlantEntity.getPlantId();
		List<WifiStickPlantEntity> queryWifiList = new ArrayList<>();
		// 第一次创建场景没有plantId
		if (plantId != null) {
			WifiStickPlantEntity queryWifi = new WifiStickPlantEntity();
			queryWifi.setPlantId(wifiStickPlantEntity.getPlantId());
			queryWifiList = wifiStickPlantService.list(Condition.getQueryWrapper(queryWifi));
		}
		// 当前逆变器数量已经是2，达到最大并机数量，不允许再并机
		if (queryWifiList.size() == BizConstant.CLIENT_INVERTER_MAX_PARALLEL) {
			return -300L;
		} else if (queryWifiList.size() == 1) {
			// 如果当前已有一台逆变器，则是并机模式，需要验证新加入的逆变器型号和之前的型号是否一致
			wifiStickPlantEntity.setParallelDeviceType(BizConstant.CLIENT_INVERTER_SLAVE_MODEL);
			WifiStickPlantEntity wifiStickPlant = queryWifiList.get(0);
			DeviceExitFactoryInfoEntity queryDeviceExit = new DeviceExitFactoryInfoEntity();
			queryDeviceExit.setDeviceSerialNumber(wifiStickPlant.getDeviceSerialNumber());
			DeviceExitFactoryInfoEntity oldInverterInfo =
				deviceExitFactoryInfoService.getOne(Condition.getQueryWrapper(queryDeviceExit));
			if (!oldInverterInfo.getDeviceType().equals(newInverterInfo.getDeviceType())) {
				return -400L;
			}
		}
		// 当数据库没有逆变器记录时，则维护新增逆变器记录
		if (queryWifiList.isEmpty()) {
			String deviceType = newInverterInfo.getDeviceType();
			String value = DictBizCache.getValue(BizConstant.CLIENT_INVERTER_MODEL_SUPPORT_PARALLEL, deviceType);
			//设备支持并机模式，且为第一个添加的智能能量变换器，则设置为主机
			if (ValidationUtil.isNotEmpty(value)) {
				wifiStickPlantEntity.setParallelDeviceType(BizConstant.CLIENT_INVERTER_MASTER_MODEL);
			}
		}
		BladeUser user = AuthUtil.getUser();
		PlantEntity plantEntity;
		if (plantId == null) {
			// 创建电站信息
			plantEntity = createPlantInfoAndAddEvent(wifiStickPlantEntity, user);
		}else{
			plantEntity = plantService.getById(plantId);
		}
		wifiStickPlantEntity.setPlantId(plantEntity.getId());
		// 如果未扫描过智能能量变换器，则保存
		wifiStickPlantEntity.setCreateUserAccount(user.getAccount());
		wifiStickPlantEntity.setCreateUser(user.getUserId());
		wifiStickPlantEntity.setWifiStickStatus(BizConstant.CLIENT_WIFI_STICK_STATUS_OFFLINE);
		wifiStickPlantService.save(wifiStickPlantEntity);
		// 保存重要事件
		this.saveImportant(wifiStickPlantEntity, user, plantEntity);
		//激活日期
		String activationDate = LocalDate.now().format(BatteryExitFactoryInfoServiceImpl.FORMATTER);
		// 如果未设置质保截止日期，则代表没有激活过，此时用质保开始时间+质保年限作为质保截止日期。
		if (StringUtils.isEmpty(newInverterInfo.getWarrantyDeadline())) {
			LocalDate date = LocalDate.parse(activationDate);
			LocalDate warrantyEndDate = date.plusYears(Long.parseLong(newInverterInfo.getQualityGuaranteeYear()));
			newInverterInfo.setWarrantyDeadline(warrantyEndDate.toString());
		}
		LambdaUpdateWrapper<DeviceExitFactoryInfoEntity> update =
			Wrappers.<DeviceExitFactoryInfoEntity>update().lambda()
				.set(DeviceExitFactoryInfoEntity::getStatus, 1)
				.set(DeviceExitFactoryInfoEntity::getUpdateUserAccount, user.getAccount())
				.set(DeviceExitFactoryInfoEntity::getUpdateUser, user.getUserId())
				.set(DeviceExitFactoryInfoEntity::getWarrantyDeadline, newInverterInfo.getWarrantyDeadline())
				.eq(DeviceExitFactoryInfoEntity::getId, newInverterInfo.getId());
		if (StringUtils.isEmpty(newInverterInfo.getActivationDate())) {
			update.set(DeviceExitFactoryInfoEntity::getActivationDate, activationDate);
		}
		if (StringUtils.isEmpty(newInverterInfo.getWarrantyStartDate())) {
			update.set(DeviceExitFactoryInfoEntity::getWarrantyStartDate, activationDate);
		}
		deviceExitFactoryInfoService.update(update);
		return plantEntity.getId();
	}

	/**
	 * 添加WifiDongle
	 *
	 * @param wifiStickPlantEntity wifiStickPlantEntity
	 */
	@NotNull
	private PlantEntity createPlantInfoAndAddEvent(WifiStickPlantEntity wifiStickPlantEntity, BladeUser bladeUser) {
		Long energySystemId = wifiStickPlantEntity.getEnergySystemId();
		// 查询能源系统信息
		EnergySystemEntity energySystemEntity =
			energySystemService.getOne(Wrappers.lambdaQuery(EnergySystemEntity.class).eq(EnergySystemEntity::getId,
				energySystemId));
		String account = bladeUser.getAccount();
		Long userId = bladeUser.getUserId();
		Long plantId = SnowFlakeGeneratorUtil.getSnowFlakeId();
		// 保存站点信息
		PlantEntity plantEntity = addPlantInfo(wifiStickPlantEntity, plantId, energySystemEntity, energySystemId,
			userId, account);
		// 记录站点事件信息
		addPlantEvent(plantId, account, userId);
		return plantEntity;
	}

	/**
	 * 添加Plant信息
	 *
	 * @param wifiStickPlantEntity 入参
	 * @param plantId              站点id
	 * @param energySystemEntity   能源系统对象
	 * @param energySystemId       能源系统id
	 * @param userId               用户id
	 * @param account              用户账号
	 * @return PlantEntity
	 */
	@NotNull
	private PlantEntity addPlantInfo(WifiStickPlantEntity wifiStickPlantEntity, Long plantId,
									 EnergySystemEntity energySystemEntity, Long energySystemId, Long userId,
									 String account) {
		PlantEntity plantEntity = new PlantEntity();
		plantEntity.setId(plantId);
		plantEntity.setPlantName(wifiStickPlantEntity.getDeviceSerialNumber());
		plantEntity.setCountryCode(energySystemEntity.getCountryCode());
		plantEntity.setProvinceCode(energySystemEntity.getProvinceCode());
		plantEntity.setCityCode(energySystemEntity.getCityCode());
		plantEntity.setCountyCode(energySystemEntity.getCountyCode());
		plantEntity.setDetailAddress(energySystemEntity.getDetailAddress());
		plantEntity.setEnergySystemId(energySystemId);
		plantEntity.setBelongingScene(wifiStickPlantEntity.getBelongingScene());
		plantEntity.setDeviceNumber(1);
		plantEntity.setCreateUser(userId);
		plantEntity.setUpdateUser(userId);
		plantEntity.setCreateUserAccount(account);
		plantEntity.setUpdateUserAccount(account);
		plantEntity.setCreateTime(new Date());
		plantEntity.setUpdateTime(new Date());
		plantService.save(plantEntity);
		return plantEntity;
	}

	/**
	 * 添加Plant事件
	 *
	 * @param plantId 站点
	 * @param account 账号
	 * @param userId  用户id
	 */
	private void addPlantEvent(Long plantId, String account, Long userId) {
		ImportantEventEntity importantEventEntity = new ImportantEventEntity();
		importantEventEntity.setEventType(BizConstant.CLIENT_IMPORTANT_EVENT_TYPE_PLANT);
		importantEventEntity.setPlantId(plantId);
		importantEventEntity.setEventContent("client.guardian.important.event.add.plant");
		importantEventEntity.setEventDate(new Date());
		importantEventEntity.setCreateUserAccount(account);
		importantEventEntity.setCreateUser(userId);
		importantEventEntity.setUpdateUserAccount(account);
		importantEventEntity.setUpdateUser(userId);
		importantEventService.save(importantEventEntity);
	}


	@Override
	public AppPlantCountVO queryPlantCount(Long energySystemId) {
		BladeUser user = AuthUtil.getUser();
		String deptId = inspectInnerRole(user);
		PlantVO queryPlant = new PlantVO();
		queryPlant.setCreateUser(user.getUserId());
		queryPlant.setDeptId(deptId);
		queryPlant.setEnergySystemId(energySystemId);
		// 用户类型
		String userType = StatusDisplayUtil.getRoleType(user.getRoleName(), user.getDeptId());
		AppPlantCountVO appPlantCountVO = plantService.queryPlantCount(queryPlant, userType);
		int total = 0;
		// 处理数据未查到的场景
		if(appPlantCountVO == null){
			appPlantCountVO = new AppPlantCountVO();
			appPlantCountVO.setAiVillaCount(0);
			appPlantCountVO.setGreenVillaCount(0);
			appPlantCountVO.setEnergyVillaCount(0);
			appPlantCountVO.setGreenElectricVillaCount(0);
		}else{
			total = appPlantCountVO.getAiVillaCount() + appPlantCountVO.getGreenVillaCount() + appPlantCountVO.getEnergyVillaCount() + appPlantCountVO.getGreenElectricVillaCount();
		}
		appPlantCountVO.setAllCount(total);
		return appPlantCountVO;
	}
	/**l
	 * 更新相关数据
	 */
	public void updateAssociatedData(String deviceSn, Long plantId) {
		//更新报警阈值信息
		GuardianThresholdCurrentStatusEntity thresholdCurrentStatus =
			guardianThresholdCurrentStatusService.getThresholdByLast(deviceSn);
		if (ValidationUtil.isNotEmpty(thresholdCurrentStatus)) {
			GuardianAlarmThresholdEntity entity = new GuardianAlarmThresholdEntity();
			BeanUtils.copyProperties(thresholdCurrentStatus, entity);
			entity.setPlantId(plantId);
			entity.setId(null);
			entity.setCreateTime(null);
			entity.setUpdateTime(null);
			alarmThresholdService.insertOrUpdateAlarm(CollectionUtil.toList(Collections.singleton(entity)));
		}
		GuardianTimedCurrentStatusEntity timedCurrentStatus =
			guardianTimedCurrentStatusService.getTimeByLast(deviceSn);
		if (ValidationUtil.isNotEmpty(timedCurrentStatus)) {

			PowerSettingServiceImpl.PowerSetting powerSetting1 =
				new PowerSettingServiceImpl.PowerSetting(timedCurrentStatus.getStartTime1(),
					timedCurrentStatus.getEndTime1(), timedCurrentStatus.getMinPower1().doubleValue(),
					timedCurrentStatus.getMaxPower1().doubleValue());
			PowerSettingServiceImpl.PowerSetting powerSetting2 =
				new PowerSettingServiceImpl.PowerSetting(timedCurrentStatus.getStartTime2(),
					timedCurrentStatus.getEndTime2(), timedCurrentStatus.getMinPower2().doubleValue(),
					timedCurrentStatus.getMaxPower2().doubleValue());
			PowerSettingServiceImpl.PowerSetting powerSetting3 =
				new PowerSettingServiceImpl.PowerSetting(timedCurrentStatus.getStartTime3(),
					timedCurrentStatus.getEndTime3(), timedCurrentStatus.getMinPower3().doubleValue(),
					timedCurrentStatus.getMaxPower3().doubleValue());
			PowerSettingServiceImpl.PowerSetting powerSetting4 =
				new PowerSettingServiceImpl.PowerSetting(timedCurrentStatus.getStartTime4(),
					timedCurrentStatus.getEndTime4(), timedCurrentStatus.getMinPower4().doubleValue(),
					timedCurrentStatus.getMaxPower4().doubleValue());
			powerSettingService.insertOrUpdatePower(plantId, deviceSn, powerSetting1, 1);
			powerSettingService.insertOrUpdatePower(plantId, deviceSn, powerSetting2, 2);
			powerSettingService.insertOrUpdatePower(plantId, deviceSn, powerSetting3, 3);
			powerSettingService.insertOrUpdatePower(plantId, deviceSn, powerSetting4, 4);

			PowerSettingServiceImpl.GateSetting gateSetting1 =
				new PowerSettingServiceImpl.GateSetting(timedCurrentStatus.getClosingTime1(), "1",
					timedCurrentStatus.getOpeningTime1(), "0");
			PowerSettingServiceImpl.GateSetting gateSetting2 =
				new PowerSettingServiceImpl.GateSetting(timedCurrentStatus.getClosingTime2(), "1",
					timedCurrentStatus.getOpeningTime2(), "0");
			PowerSettingServiceImpl.GateSetting gateSetting3 =
				new PowerSettingServiceImpl.GateSetting(timedCurrentStatus.getClosingTime3(), "1",
					timedCurrentStatus.getOpeningTime3(), "0");
			PowerSettingServiceImpl.GateSetting gateSetting4 =
				new PowerSettingServiceImpl.GateSetting(timedCurrentStatus.getClosingTime4(), "1",
					timedCurrentStatus.getOpeningTime4(), "0");

			powerSettingService.insertOrUpdateGate(plantId, deviceSn, gateSetting1, 1);
			powerSettingService.insertOrUpdateGate(plantId, deviceSn, gateSetting2, 2);
			powerSettingService.insertOrUpdateGate(plantId, deviceSn, gateSetting3, 3);
			powerSettingService.insertOrUpdateGate(plantId, deviceSn, gateSetting4, 4);
		}

	}


}
