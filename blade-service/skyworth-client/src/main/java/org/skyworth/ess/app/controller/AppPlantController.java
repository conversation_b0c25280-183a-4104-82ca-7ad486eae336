package org.skyworth.ess.app.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.app.service.IAppService;
import org.skyworth.ess.app.service.IAppSetupService;
import org.skyworth.ess.app.vo.AppPlantCountVO;
import org.skyworth.ess.app.vo.AppReportHeaderVO;
import org.skyworth.ess.app.vo.AppVO;
import org.skyworth.ess.plant.entity.PlantEntity;
import org.skyworth.ess.plant.vo.PlantDetailVO;
import org.skyworth.ess.plant.vo.PlantListVO;
import org.springblade.common.constant.I18nMsgCode;
import org.springblade.common.utils.CommonUtil;
import org.springblade.core.log.annotation.ApiLog;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.StringUtil;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/app/plant")
@Api(value = "app站点接口", tags = "app站点接口")
@Slf4j
public class AppPlantController {

	@Resource
	private IAppService appService;
	@Resource
	private IAppSetupService appSetupService;

	@PostMapping("/add")
	@ApiOperation(value = "新增站点", notes = "新增站点")
	@ApiLog("app新增站点")
	public R<String> addPlant(@RequestBody @Validated PlantEntity plant) {
		R<String> r = new R<>();
		String currentLanguage = CommonUtil.getCurrentLanguage();
		try {
			if (StringUtil.isBlank(plant.getPlantName()) || StringUtil.isBlank(plant.getCountryCode())
				|| StringUtil.isBlank(plant.getDetailAddress())) {
				r.setCode(I18nMsgCode.SKYWORTH_CLIENT_PLANT_100008.getCode());
				r.setMsg(I18nMsgCode.SKYWORTH_CLIENT_PLANT_100008.autoGetMessage(currentLanguage));
				return r;
			}
			return R.data(appService.addPlant(plant));
		} catch (Exception e) {
			if (e instanceof BusinessException) {
				r.setCode(I18nMsgCode.SKYWORTH_CLIENT_PLANT_100001.getCode());
				r.setMsg(StringUtil.format(I18nMsgCode.SKYWORTH_CLIENT_PLANT_100001.autoGetMessage(currentLanguage), plant.getPlantName()));
				return r;
			} else {
				r.setCode(I18nMsgCode.SKYWORTH_COMMON_ERROR_900000.getCode());
				r.setMsg(I18nMsgCode.SKYWORTH_COMMON_ERROR_900000.autoGetMessage(currentLanguage));
				return r;
			}
		}

	}

	//@PostMapping("/queryBySelf/{pageSize}/{current}")
	//@ApiOperation(value = "查询用户站点列表信息", notes = "查询用户站点列表信息")
	//@ApiLog("app查询用户站点列表信息")
	//public R<IPage<PlantVO>> queryPlantBySelf(@RequestBody AppVO appVO,
	//										  @ApiParam(value = "每页大小", required = true) @PathVariable("pageSize") int pageSize,
	//										  @ApiParam(value = "当前页", required = true) @PathVariable("current") int current) {
	//	Query query = new Query();
	//	query.setCurrent(current);
	//	query.setSize(pageSize);
	//	return R.data(appService.queryPlantBySelf(appVO, query));
	//}

	@PostMapping("/queryBySelf/v2/{pageSize}/{current}")
	@ApiOperation(value = "查询用户站点列表信息", notes = "查询用户站点列表信息")
	@ApiLog("app查询用户站点列表信息")
	public R<IPage<PlantListVO>> queryPlantBySelfV2(@RequestBody AppVO appVO,
													@ApiParam(value = "每页大小", required = true) @PathVariable("pageSize") int pageSize,
													@ApiParam(value = "当前页", required = true) @PathVariable("current") int current) {
		Query query = new Query();
		query.setCurrent(current);
		query.setSize(pageSize);
		return R.data(appService.queryPlantBySelfV2(appVO, query));
	}

	@GetMapping("/queryPlantCount")
	@ApiOperation(value = "查询各场景下站点数量", notes = "查询各场景下站点数量")
	@ApiLog("查询各场景下站点数量")
	public R<AppPlantCountVO> queryPlantCount(@ApiParam(value = "能源系统id", required = true) @RequestParam("energySystemId") Long energySystemId) {
		return R.data(appService.queryPlantCount(energySystemId));
	}
	@PostMapping("/detail")
	@ApiOperation(value = "查询电站代理商信息", notes = "查询电站代理商信息")
	@ApiLog("查询电站代理商信息")
	public R<PlantDetailVO> plantDetail(@RequestBody AppVO appVO) {
		return R.data(appService.getPlantDetail(appVO));
	}

	//@PostMapping("/queryRunningState")
	//@ApiOperation(value = "查询电站运行状态报表信息", notes = "查询电站运行状态报表信息")
	//@ApiLog("app查询电站运行状态报表信息")
	//public R<AppReportHeaderVO> queryPlantRunningState(@RequestBody AppVO appVO) {
	//	return R.data(appService.queryPlantRunningState(appVO));
	//}

	@PostMapping("/queryRunningState/v2")
	@ApiOperation(value = "查询电站运行状态报表信息", notes = "查询电站运行状态报表信息")
	@ApiLog("app查询电站运行状态报表信息")
	public R<AppReportHeaderVO> queryPlantRunningStateV2(@RequestBody AppVO appVO) {
		return R.data(appService.queryPlantRunningStateV2(appVO));
	}

	@PostMapping("/queryRunningStateHeader/v2")
	@ApiOperation(value = "查询电站运行状态报表信息头部数据", notes = "查询电站运行状态报表信息头部数据，6个方块")
	@ApiLog("app查询电站运行状态报表信息头部数据")
	public R<AppReportHeaderVO> queryPlantRunningStateHeaderV2(@RequestBody AppVO appVO) {
		return R.data(appService.queryPlantRunningStateHeaderV2(appVO));
	}

	@PostMapping("/queryRunningStateChart/v2")
	@ApiOperation(value = "查询电站运行状态报表信息折线图数据", notes = "查询电站运行状态报表信息折线图数据，饼图和折线图")
	@ApiLog("app查询电站运行状态报表信息折线图数据")
	public R<AppReportHeaderVO> queryRunningStateChartV2(@RequestBody AppVO appVO) {
		return R.data(appService.queryRunningStateChartV2(appVO));
	}

	@PostMapping("/delete")
	@ApiOperation(value = "删除站点", notes = "删除站点")
	@ApiLog("app删除站点")
	public R<Boolean> deletePlant(@RequestParam String ids) {
		return appService.deletePlant(ids);
	}

	@PostMapping("/edit")
	@ApiOperation(value = "站点编辑", notes = "站点编辑")
	@ApiLog("app站点编辑")
	public R<String> editPlant(@RequestBody @Validated PlantEntity plant) {
		R<String> r = new R<>();
		try {
			return appService.editPlant(plant);
		} catch (Exception e) {
			String currentLanguage = CommonUtil.getCurrentLanguage();
			r.setCode(I18nMsgCode.SKYWORTH_COMMON_ERROR_900000.getCode());
			r.setMsg(I18nMsgCode.SKYWORTH_COMMON_ERROR_900000.autoGetMessage(currentLanguage));
			return r;
		}

	}

	@GetMapping("/count")
	@ApiOperation(value = "查询账户下电站数量", notes = "查询账户下电站数量")
	@ApiLog("查询账户下电站数量")
	public R<JSONObject> getPlantCount() {
		return R.data(appService.getPlantCount());
	}
}
