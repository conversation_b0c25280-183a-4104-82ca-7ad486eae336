package org.skyworth.ess.app.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.app.service.IAppLazzenGatewayService;
import org.skyworth.ess.app.service.IAppService;
import org.skyworth.ess.app.service.IAppSetupService;
import org.skyworth.ess.app.vo.AppSetRequestVO;
import org.skyworth.ess.constant.AppSetupTypeEnum;
import org.skyworth.ess.lazzen.gatewayplant.entity.GatewayPlantEntity;
import org.skyworth.ess.lazzen.gatewayplant.vo.GatewayPlantCurrentStatusVO;
import org.springblade.common.constant.EntityFieldConstant;
import org.springblade.common.constant.I18nMsgCode;
import org.springblade.common.utils.CommonUtil;
import org.springblade.core.log.annotation.ApiLog;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.StringUtil;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Map;

@RestController
@RequestMapping("/app/guardian/lz")
@Api(value = "app安全卫士接口，良信", tags = "app安全卫士接口，良信")
@Slf4j
public class AppLazzenGatewayController {

	@Resource
	private IAppService appService;
	@Resource
	private IAppSetupService appSetupService;
	@Resource
	private IAppLazzenGatewayService appLazzenGatewayService;

	@PostMapping("/add")
	@ApiOperation(value = "新增安全卫士", notes = "新增安全卫士")
	@ApiLog("新增安全卫士")
	public R<String> addGuardian(@RequestBody GatewayPlantEntity gatewayPlantEntity) {
		String currentLanguage = CommonUtil.getCurrentLanguage();
		R<String> r = new R<>();
		String id = appLazzenGatewayService.addGuardian(gatewayPlantEntity);
		if ("-200L".equals(id)) {
			r.setCode(I18nMsgCode.SKYWORTH_CLIENT_PLANT_100022.getCode());
			r.setMsg(StringUtil.format(I18nMsgCode.SKYWORTH_CLIENT_PLANT_100022.autoGetMessage(currentLanguage), gatewayPlantEntity.getGatewayUniqueNumber()));
			return r;
		}
		if ("-300L".equals(id)) {
			r.setCode(I18nMsgCode.SKYWORTH_CLIENT_GUARDIAN_100127.getCode());
			r.setMsg(StringUtil.format(I18nMsgCode.SKYWORTH_CLIENT_GUARDIAN_100127.autoGetMessage(currentLanguage), gatewayPlantEntity.getGatewayUniqueNumber()));
			return r;
		}
		if ("-400L".equals(id)) {
			r.setCode(I18nMsgCode.SKYWORTH_CLIENT_GUARDIAN_100130.getCode());
			r.setMsg(StringUtil.format(I18nMsgCode.SKYWORTH_CLIENT_GUARDIAN_100130.autoGetMessage(currentLanguage), gatewayPlantEntity.getGatewayUniqueNumber()));
			return r;
		}
		return R.data(id);
	}


	@GetMapping("/statusAndDeviceInfo")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "详情", notes = "传入GuardianCurrentStatus")
	public R<GatewayPlantCurrentStatusVO> getGuardianStatusAndDeviceInfo(GatewayPlantEntity gatewayPlantEntity) {
		GatewayPlantCurrentStatusVO detail = appLazzenGatewayService.getStatusAndDeviceInfo(gatewayPlantEntity);
		return R.data(detail);
	}


	@GetMapping("/GatePositionSetup/queryAll")
	@ApiOperation(value = "查询安全卫士闸位状态设置", notes = "查询安全卫士闸位状态设置")
	@ApiLog("app查询安全卫士闸位状态设置")
	public R<JSONObject> getGuardianGatePositionSetup(@ApiIgnore @RequestParam Map<String, String> appSetRequestVO) {
		AppSetRequestVO requestVO = new AppSetRequestVO();
		requestVO.setPlantId(Long.parseLong(appSetRequestVO.get(EntityFieldConstant.PLANT_ID)));
		requestVO.setDeviceSerialNumber(appSetRequestVO.get(EntityFieldConstant.GATEWAY_UNIQUE_NUMBER));
		//查询安全卫士类型
		requestVO.setDeviceType(AppSetupTypeEnum.LAZZEN_GUARDIAN_GATE_POSITION_SETUP.getDeviceType());
		//闸位状态设置
		requestVO.setSetCategory(AppSetupTypeEnum.LAZZEN_GUARDIAN_GATE_POSITION_SETUP.getNumType());
		String currentLanguage = CommonUtil.getCurrentLanguage();
		requestVO.setLanguage(currentLanguage);
		return R.data(appSetupService.getSetupItem(requestVO));
	}

	/**
	 * 安全卫士信息 解绑
	 */
	@PostMapping("/unbind")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "解绑", notes = "传入guardianPlantEntity")
	//	@PreAuth("hasPermission('client:guardianPlant:detail')")
	public R<Boolean> unbind(@Valid @RequestBody GatewayPlantEntity gatewayPlantEntity) {
		return R.status(appLazzenGatewayService.unbind(gatewayPlantEntity));
	}
}
