/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.ota.mapper;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.skyworth.ess.battery.entity.QueryCondition;
import org.skyworth.ess.battery.vo.BatteryPageResultVO;
import org.skyworth.ess.ota.entity.DeviceSoftwareVersionInfoEntity;
import org.skyworth.ess.ota.excel.DeviceSoftwareVersionInfoExcel;
import org.skyworth.ess.ota.vo.DeviceSoftwareVersionInfoVO;
import org.skyworth.ess.ota.vo.OtaUpdatePackVO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 设备软件版本信息表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
public interface DeviceSoftwareVersionInfoMapper extends BaseMapper<DeviceSoftwareVersionInfoEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page                      分页参数
	 * @param deviceSoftwareVersionInfo 传入条件
	 * @return List
	 */
	List<DeviceSoftwareVersionInfoVO> selectDeviceSoftwareVersionInfoPage(IPage page, DeviceSoftwareVersionInfoVO deviceSoftwareVersionInfo);


	/**
	 * 获取导出数据
	 *
	 * @param queryWrapper 查询条件
	 * @return List
	 */
	List<DeviceSoftwareVersionInfoExcel> exportDeviceSoftwareVersionInfo(@Param("ew") Wrapper<DeviceSoftwareVersionInfoEntity> queryWrapper);

	/**
	 * 获取需要升级的设备SN号
	 * 1.在线的并且版本号和最新版本号不一致的wifi棒
	 * 2.在线+故障得智能能量变换器，并且版本号和最新版本号不一致的智能能量变换器
	 *
	 * @param page 入参
	 * @return List<String>
	 * <AUTHOR>
	 * @since 2023/9/22 11:27
	 **/
	List<String> findSerialNumberList(@Param("page") IPage page);

	/**
	 * 根据设备编号查询升级包信息
	 *
	 * @param serialNumbers 入参
	 * @return List<DeviceSoftwareVersionInfoVO>
	 * <AUTHOR>
	 * @since 2023/9/22 13:45
	 **/
	List<OtaUpdatePackVO> findDeviceUpgradeListBySn(@Param("serialNumbers") List<String> serialNumbers);

	/**
	 * 获取OTA最终升级结果
	 *
	 * @param map 入参
	 * <AUTHOR>
	 * @since 2023/9/22 16:55
	 **/
	void obtainUpgradeResults(@Param("map") Map<String, String> map);

	/**
	 * 获取OTA软件下发结果
	 *
	 * @param deviceSns 入参
	 * <AUTHOR>
	 * @since 2023/9/22 16:55
	 **/
	void obtainUpgradePushResults(@Param("serialNumbers") List<String> deviceSns);

	/**
	 * 针对数据下发未响应，或升级结果未回写的数据，做超时失败处理
	 *
	 * <AUTHOR>
	 * @since 2023/9/25 13:29
	 **/
	void processingTimeoutStatus();

	/**
	 * 修改升级表状态以及维护升级版本
	 *
	 * @param otaUpdatePackVOList 入参
	 * <AUTHOR>
	 * @since 2023/9/25 14:34
	 **/
	void updateStatusAndLatestReleasedVersion(@Param("list") List<OtaUpdatePackVO> otaUpdatePackVOList);

	/**
	 * 根据设备软件版本信息表id查询升级包信息
	 *
	 * @param deviceSoftwareVersionIds 入参
	 * @return List<OtaUpdatePackVO>
	 * <AUTHOR>
	 * @since 2023/9/25 15:43
	 **/
	List<OtaUpdatePackVO> findDeviceUpgradeListByVersionIds(@Param("list") List<Long> deviceSoftwareVersionIds);

	/**
	 * 查询设备是否在线
	 *
	 * @param ids 入参
	 * @return List<String>
	 * <AUTHOR>
	 * @since 2023/12/1 9:06
	 **/
	String validDeviceIsOnLine(@Param("ids") List<Long> ids);

	/**
	 * 验证当前设备软件版本已经是最新版
	 *
	 * @param ids 入参
	 * @return List<String>
	 * <AUTHOR>
	 * @since 2023/12/1 9:07
	 **/
	Set<Long> validDeviceIsLatestVersion(@Param("ids") List<Long> ids);

	/**
	 * 脱机操作上报异常，修改升级状态
	 *
	 * @param jsonObject 入参
	 * <AUTHOR>
	 * @since 2024/3/5 17:50
	 **/
	void otaOfflineUpgradeException(@Param("json") JSONObject jsonObject);

	List<DeviceSoftwareVersionInfoEntity> queryPage(@Param("soft") Map<String, Object> deviceSoftwareVersionInfo, IPage page,@Param("createUser") Long createUser);

	void batchDeleteByDeviceSn(@Param("listDeviceSn") List<String> listDeviceSn);
	/**
	 * 查询不能升级的设备
	 *
	 * @param listDeviceSn 入参
	 * <AUTHOR>
	 * @since 2024/3/6 10:06
	 **/
	List<String> queryCanNotUpgradedSN(@Param("listDeviceSn") List<String> listDeviceSn);
}
