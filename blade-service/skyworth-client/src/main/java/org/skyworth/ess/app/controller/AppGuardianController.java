package org.skyworth.ess.app.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.app.service.IAppService;
import org.skyworth.ess.app.service.IAppSetupService;
import org.skyworth.ess.app.vo.AppSetRequestVO;
import org.skyworth.ess.constant.AppSetupTypeEnum;
import org.skyworth.ess.guardian.guardianplant.entity.GuardianPlantEntity;
import org.springblade.common.constant.EntityFieldConstant;
import org.springblade.common.constant.I18nMsgCode;
import org.springblade.common.utils.CommonUtil;
import org.springblade.core.log.annotation.ApiLog;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.StringUtil;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.Map;

@RestController
@RequestMapping("/app/guardian")
@Api(value = "app安全卫士接口", tags = "app安全卫士接口")
@Slf4j
public class AppGuardianController {

	@Resource
	private IAppService appService;
	@Resource
	private IAppSetupService appSetupService;

	@PostMapping("/add")
	@ApiOperation(value = "新增安全卫士", notes = "新增安全卫士")
	@ApiLog("新增安全卫士")
	public R<String> addGuardian(@RequestBody GuardianPlantEntity guardianPlantEntity) {
		String currentLanguage = CommonUtil.getCurrentLanguage();
		R<String> r = new R<>();
		Long id = appService.addGuardian(guardianPlantEntity);
		if (-100L == id) {
			r.setCode(I18nMsgCode.SKYWORTH_CLIENT_GUARDIAN_100126.getCode());
			r.setMsg(StringUtil.format(I18nMsgCode.SKYWORTH_CLIENT_GUARDIAN_100126.autoGetMessage(currentLanguage), guardianPlantEntity.getSecurityGuardSerialNumber()));
			return r;
		}
		if (-200L == id) {
			r.setCode(I18nMsgCode.SKYWORTH_CLIENT_PLANT_100022.getCode());
			r.setMsg(StringUtil.format(I18nMsgCode.SKYWORTH_CLIENT_PLANT_100022.autoGetMessage(currentLanguage), guardianPlantEntity.getSecurityGuardSerialNumber()));
			return r;
		}
		if (-300L == id) {
			r.setCode(I18nMsgCode.SKYWORTH_CLIENT_GUARDIAN_100127.getCode());
			r.setMsg(StringUtil.format(I18nMsgCode.SKYWORTH_CLIENT_GUARDIAN_100127.autoGetMessage(currentLanguage), guardianPlantEntity.getSecurityGuardSerialNumber()));
			return r;
		}

		return R.data(StrUtil.toString(id));
	}

	@GetMapping("/alarmSetup/queryAll")
	@ApiOperation(value = "查询安全卫士告警设置", notes = "查询安全卫士告警设置")
	@ApiLog("app查询安全卫士告警设置")
	public R<JSONObject> getGuardianAlarmSet(@ApiIgnore @RequestParam Map<String, String> appSetRequestVO) {
		AppSetRequestVO requestVO = new AppSetRequestVO();
		requestVO.setPlantId(Long.parseLong(appSetRequestVO.get(EntityFieldConstant.PLANT_ID)));
		requestVO.setDeviceSerialNumber(appSetRequestVO.get(EntityFieldConstant.DEVICE_SERIAL_NUMBER));
		//查询安全卫士类型
		requestVO.setDeviceType(AppSetupTypeEnum.GUARDIAN_ALARM_SETUP.getDeviceType());
		//告警设置
		requestVO.setSetCategory(AppSetupTypeEnum.GUARDIAN_ALARM_SETUP.getNumType());
		String currentLanguage = CommonUtil.getCurrentLanguage();
		requestVO.setLanguage(currentLanguage);
		return R.data(appSetupService.getSetupItem(requestVO));
	}

	@GetMapping("/powerSetup/queryAll")
	@ApiOperation(value = "查询安全卫士功率设置", notes = "查询安全卫士功率设置")
	@ApiLog("app查询安全卫士功率设置")
	public R<JSONObject> getGuardianPowerSet(@ApiIgnore @RequestParam Map<String, String> appSetRequestVO) {
		AppSetRequestVO requestVO = new AppSetRequestVO();
		requestVO.setPlantId(Long.parseLong(appSetRequestVO.get(EntityFieldConstant.PLANT_ID)));
		requestVO.setDeviceSerialNumber(appSetRequestVO.get(EntityFieldConstant.DEVICE_SERIAL_NUMBER));
		//查询安全卫士类型
		requestVO.setDeviceType(AppSetupTypeEnum.GUARDIAN_POWER_SETUP.getDeviceType());
		//功率设置
		requestVO.setSetCategory(AppSetupTypeEnum.GUARDIAN_POWER_SETUP.getNumType());
		String currentLanguage = CommonUtil.getCurrentLanguage();
		requestVO.setLanguage(currentLanguage);
		return R.data(appSetupService.getSetupItem(requestVO));
	}

	@GetMapping("/switchGateSetup/queryAll")
	@ApiOperation(value = "查询安全卫士定时开关闸设置", notes = "查询安全卫士定时开关闸设置")
	@ApiLog("app查询安全卫士定时开关闸设置")
	public R<JSONObject> getGuardianSwitchGateSetup(@ApiIgnore @RequestParam Map<String, String> appSetRequestVO) {
		AppSetRequestVO requestVO = new AppSetRequestVO();
		requestVO.setPlantId(Long.parseLong(appSetRequestVO.get(EntityFieldConstant.PLANT_ID)));
		requestVO.setDeviceSerialNumber(appSetRequestVO.get(EntityFieldConstant.DEVICE_SERIAL_NUMBER));
		//查询安全卫士类型
		requestVO.setDeviceType(AppSetupTypeEnum.GUARDIAN_SWITCH_GATE_SETUP.getDeviceType());
		//开关闸设置
		requestVO.setSetCategory(AppSetupTypeEnum.GUARDIAN_SWITCH_GATE_SETUP.getNumType());
		String currentLanguage = CommonUtil.getCurrentLanguage();
		requestVO.setLanguage(currentLanguage);
		return R.data(appSetupService.getSetupItem(requestVO));
	}

	@GetMapping("/GatePositionSetup/queryAll")
	@ApiOperation(value = "查询安全卫士闸位状态设置", notes = "查询安全卫士闸位状态设置")
	@ApiLog("app查询安全卫士闸位状态设置")
	public R<JSONObject> getGuardianGatePositionSetup(@ApiIgnore @RequestParam Map<String, String> appSetRequestVO) {
		AppSetRequestVO requestVO = new AppSetRequestVO();
		requestVO.setPlantId(Long.parseLong(appSetRequestVO.get(EntityFieldConstant.PLANT_ID)));
		requestVO.setDeviceSerialNumber(appSetRequestVO.get(EntityFieldConstant.DEVICE_SERIAL_NUMBER));
		//查询安全卫士类型
		requestVO.setDeviceType(AppSetupTypeEnum.GUARDIAN_GATE_POSITION_SETUP.getDeviceType());
		//闸位状态设置
		requestVO.setSetCategory(AppSetupTypeEnum.GUARDIAN_GATE_POSITION_SETUP.getNumType());
		String currentLanguage = CommonUtil.getCurrentLanguage();
		requestVO.setLanguage(currentLanguage);
		return R.data(appSetupService.getSetupItem(requestVO));
	}
}
