/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.battery.mapper;

import org.skyworth.ess.battery.entity.BatteryStatusExtendEntity;
import org.skyworth.ess.battery.vo.BatteryStatusExtendVO;
import org.skyworth.ess.battery.excel.BatteryStatusExtendExcel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 电池状态扩展表，每个储能一行 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
public interface BatteryStatusExtendMapper extends BaseMapper<BatteryStatusExtendEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param statusExtend
	 * @return
	 */
	List<BatteryStatusExtendVO> selectBatteryStatusExtendPage(IPage page, BatteryStatusExtendVO statusExtend);


	/**
	 * 获取导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<BatteryStatusExtendExcel> exportBatteryStatusExtend(@Param("ew") Wrapper<BatteryStatusExtendEntity> queryWrapper);

}
