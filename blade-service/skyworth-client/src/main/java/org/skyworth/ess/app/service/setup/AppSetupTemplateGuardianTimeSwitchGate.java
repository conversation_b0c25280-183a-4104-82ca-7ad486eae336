package org.skyworth.ess.app.service.setup;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.app.vo.AppAdvancedSetup;
import org.skyworth.ess.app.vo.AppSetRequestVO;
import org.skyworth.ess.guardian.guardianplant.service.IGuardianPlantService;
import org.skyworth.ess.guardian.issue.GuardianIssueStrategy;
import org.skyworth.ess.guardian.issue.IssueStrategy4TimeSwitchGate;
import org.skyworth.ess.guardian.issue.IssueStrategyEntity;
import org.skyworth.ess.guardian.timedswitchgate.entity.GuardianTimedSwitchGateEntity;
import org.skyworth.ess.setItem.entity.SetItemEntity;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.utils.BinaryToHexUtils;
import org.springblade.core.tool.api.R;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> - xuehongjiang
 * @version 1.0
 * @project
 * @description 高级设置子类-安全卫士定时开关闸设置
 * @create-time 2024年8月6日15:06:41
 */
@Slf4j
public class AppSetupTemplateGuardianTimeSwitchGate extends AppSetupTemplate {
	private IGuardianPlantService guardianService = SpringUtil.getBean(IGuardianPlantService.class);

	@Override
	public Map<String, Object> getSetupDataByDb(Long plantId, String serialNumber) {
		Map<String, Object> allGuardianSwitchGateSetup = guardianService.getAllGuardianSwitchGateSetup(plantId, serialNumber);
		if (allGuardianSwitchGateSetup.containsKey("timingTypeGateCustom") && null != allGuardianSwitchGateSetup.get("timingTypeGateCustom")){
			allGuardianSwitchGateSetup.compute("timingTypeGateCustom", (k, timingTypeGateCustom) -> BinaryToHexUtils.binarySetToGeneralSet(timingTypeGateCustom.toString()));
		}
		Object timedSwitchGateData = new HashMap<>();
		if (allGuardianSwitchGateSetup.containsKey("timedSwitchGateData") && null != allGuardianSwitchGateSetup.get("timedSwitchGateData")){
			timedSwitchGateData = allGuardianSwitchGateSetup.get("timedSwitchGateData");
		}
		if (ObjectUtil.isNotEmpty(timedSwitchGateData)) {
			String jsonData = String.valueOf(timedSwitchGateData);
			//将子表数据转换为List，然后散列到不同的设置项字段中
			List<GuardianTimedSwitchGateEntity> list = JSON.parseArray(jsonData).toJavaList(GuardianTimedSwitchGateEntity.class);
			List<GuardianTimedSwitchGateEntity> sortedList = list.stream()
				.sorted(Comparator.comparingInt(GuardianTimedSwitchGateEntity::getSetSort))
				.collect(Collectors.toList());
			int listSize = sortedList.size();
			for (int i = 0; i < 4; i++) {
				int index = i + 1;
				// 最多情况下有4组开关闸设置，如果数据库只设置了2组，需要将多余的设置项置空
				if (index > listSize) {
					allGuardianSwitchGateSetup.put("closingTime" + index, null);
					allGuardianSwitchGateSetup.put("openingTime" + index, null);
					allGuardianSwitchGateSetup.put("setSort", index);
				} else {
					GuardianTimedSwitchGateEntity guardianTimedPowerEntity = sortedList.get(i);
					allGuardianSwitchGateSetup.put("closingTime" + index, guardianTimedPowerEntity.getClosingTime());
					allGuardianSwitchGateSetup.put("openingTime" + index, guardianTimedPowerEntity.getOpeningTime());
					allGuardianSwitchGateSetup.put("setSort", guardianTimedPowerEntity.getSetSort());
				}
			}
		}
		return allGuardianSwitchGateSetup;
	}

	@Override
	public void completeConfigItemBySubTemplate(Map<String, Object> setupDataByDb, SetItemEntity setupItemConfig, JSONObject dataObject, AppSetRequestVO appSetRequestVO) {
		if (ObjectUtil.isNotNull(setupDataByDb) && !setupDataByDb.isEmpty()) {
			for (Map.Entry<String, Object> entry : setupDataByDb.entrySet()) {
				super.buildSetupItemInfo(entry, setupItemConfig, dataObject);
			}
		}
	}

	// ==================================================================================

	/**
	 * 安全卫士下发，需要重写抽象父类的此方法
	 * @param deviceAdvancedSetup
	 * @return
	 */
	@Override
	public R issueSetupCommon(AppAdvancedSetup deviceAdvancedSetup) {
		IssueStrategyEntity issueStrategyEntity = new IssueStrategyEntity(deviceAdvancedSetup.getDeviceSerialNumber(), CommonConstant.BIZ_TYPE_ISSUE_DATA, deviceAdvancedSetup);
		GuardianIssueStrategy strategy = new IssueStrategy4TimeSwitchGate();
		return strategy.executeStrategy(issueStrategyEntity);
	}

}
