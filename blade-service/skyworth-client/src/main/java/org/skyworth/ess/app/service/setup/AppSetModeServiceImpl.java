package org.skyworth.ess.app.service.setup;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.app.service.IWeatherService;
import org.skyworth.ess.constant.SourceEnum;
import org.skyworth.ess.device.entity.DeviceCurrentStatusEntity;
import org.skyworth.ess.device.entity.DeviceCustomModeEntity;
import org.skyworth.ess.device.entity.TimeZoneDevice;
import org.skyworth.ess.device.service.IDevice23Service;
import org.skyworth.ess.device.service.IDeviceCurrentStatusService;
import org.skyworth.ess.device.service.IDeviceCustomModeService;
import org.skyworth.ess.device.service.TimeZoneDeviceService;
import org.skyworth.ess.operationlog.entity.AdvancedSettingsOperationLogEntity;
import org.skyworth.ess.operationlog.service.IAdvancedSettingsOperationLogService;
import org.skyworth.ess.plant.entity.WifiStickPlantEntity;
import org.skyworth.ess.plant.service.IWifiStickPlantService;
import org.springblade.common.constant.I18nMsgCode;
import org.springblade.common.utils.CommonUtil;
import org.springblade.common.utils.DateUtil;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.tool.api.R;
import org.springblade.system.constant.DictConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AppSetModeServiceImpl {
	@Autowired
	private IDevice23Service device23Service;
	@Autowired
	private IWifiStickPlantService wifiStickPlantService;
	@Autowired
	private IDeviceCustomModeService deviceCustomModeService;
	@Autowired
	private IDeviceCurrentStatusService deviceCurrentStatusService;
	@Autowired
	private IWeatherService weatherService;
	@Autowired
	private TimeZoneDeviceService timeZoneDeviceService;
	@Autowired
	private IAdvancedSettingsOperationLogService operationLogService;
	public R inverterModeIssue(DeviceCustomModeEntity deviceCustomModeEntity) {
		// 如果是智能模式，根据天气计算实际模式
		weatherService.calHybridWorkMode(deviceCustomModeEntity);
		// 发送mqtt消息
		R<String> result = device23Service.workModeIssueToDevice(deviceCustomModeEntity);
//		result.setCode(200);
		log.info("set inverter mode result : {}", result);
		if (result.getCode() == 200) {
			LambdaQueryWrapper<WifiStickPlantEntity> wifiEq = Wrappers.<WifiStickPlantEntity>query().lambda()
				.eq(WifiStickPlantEntity::getPlantId, deviceCustomModeEntity.getPlantId())
				.eq(WifiStickPlantEntity::getDeviceSerialNumber,deviceCustomModeEntity.getDeviceSerialNumber());
			List<WifiStickPlantEntity> stickPlantEntityList = wifiStickPlantService.list(wifiEq);
			if (!stickPlantEntityList.isEmpty()) {
				LambdaUpdateWrapper<DeviceCurrentStatusEntity> eq = Wrappers.<DeviceCurrentStatusEntity>lambdaUpdate().set(DeviceCurrentStatusEntity::getHybridWorkMode, deviceCustomModeEntity.getHybridWorkMode())
					.eq(DeviceCurrentStatusEntity::getPlantId, deviceCustomModeEntity.getPlantId())
					.eq(DeviceCurrentStatusEntity::getDeviceSerialNumber,deviceCustomModeEntity.getDeviceSerialNumber());
				if (cn.hutool.core.util.ObjectUtil.isNotNull(deviceCustomModeEntity.getHybridWorkMode())) {
					deviceCurrentStatusService.update(eq);
				}
				// 并机需要根据站点id和智能能量变换器sn
				device23Service.updateByPlantId(deviceCustomModeEntity);

			}
			DeviceCustomModeEntity entity = new DeviceCustomModeEntity();
			entity.setPlantId(deviceCustomModeEntity.getPlantId());
			entity.setDeviceSerialNumber(deviceCustomModeEntity.getDeviceSerialNumber());
			List<DeviceCustomModeEntity> modelist = deviceCustomModeService.list(Condition.getQueryWrapper(entity));
			// 如果选择的是AI模式，则模式记录表 记录选择的 模式
			if(DictConstant.INVERTER_MODE_AI.equals(deviceCustomModeEntity.getSelectHybridWorkMode())) {
				log.info("issueSetupCommon update deviceCustomModeService is ai : {} ",deviceCustomModeEntity.getDeviceSerialNumber());
				deviceCustomModeEntity.setHybridWorkMode(deviceCustomModeEntity.getSelectHybridWorkMode());
			}

			if (CollectionUtils.isEmpty(modelist)) {
				deviceCustomModeService.save(deviceCustomModeEntity);
			} else {
				DeviceCustomModeEntity dbEntity = modelist.get(0);
				deviceCustomModeEntity.setId(dbEntity.getId());
				deviceCustomModeService.updateCustomMode(deviceCustomModeEntity);
			}
			String currentLanguage = CommonUtil.getCurrentLanguage();
			return R.success(I18nMsgCode.SKYWORTH_CLIENT_DEVICE_100123.autoGetMessage(currentLanguage));
		}
		return result;
	}

	public void saveLog(DeviceCustomModeEntity deviceCustomModeEntity,R result) throws ParseException {

		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
		TimeZoneDevice timeZoneDevice = timeZoneDeviceService.getOne(Condition.getQueryWrapper(new TimeZoneDevice(deviceCustomModeEntity.getPlantId())));
		AdvancedSettingsOperationLogEntity insertLogEntity = new AdvancedSettingsOperationLogEntity();
		insertLogEntity.setPlantId(deviceCustomModeEntity.getPlantId());
		insertLogEntity.setDeviceSerialNumber(deviceCustomModeEntity.getDeviceSerialNumber());
		insertLogEntity.setSource(SourceEnum.AI_MODE.name());
		insertLogEntity.setStatus(0);
		insertLogEntity.setRequestBody(JSON.toJSONString(insertLogEntity).substring(0, Math.min(JSON.toJSONString(insertLogEntity).length(), 512)));
		insertLogEntity.setResponseBody(JSON.toJSONString(result).substring(0, Math.min(JSON.toJSONString(result).length(), 512)));
		if (ObjectUtil.isNotNull(timeZoneDevice)) {
			String deviceTimeZone = timeZoneDevice.getTimeZone();
			String currentUtcTimeZone = DateUtil.getCurrentUtcTimeZone();
			//比较时差
			int compareUtc = DateUtil.compareUtc(currentUtcTimeZone, deviceTimeZone);
			LocalDateTime currentDateTime = LocalDateTime.now();
			LocalDateTime localDateTime = currentDateTime.plusHours(compareUtc);

			String formatted = localDateTime.format(formatter);
			Date date = format.parse(formatted);

			insertLogEntity.setCreateTime(date);
			insertLogEntity.setUpdateTime(date);
			insertLogEntity.setTimeZone(deviceTimeZone);
		}
		operationLogService.save(insertLogEntity);

		log.info("inverterModeSet saveLog end : {}",deviceCustomModeEntity.getDeviceSerialNumber());
	}
}
