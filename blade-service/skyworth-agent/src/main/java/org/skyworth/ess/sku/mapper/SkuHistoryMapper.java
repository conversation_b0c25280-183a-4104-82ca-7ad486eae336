/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.sku.mapper;

import org.skyworth.ess.sku.entity.SkuHistoryEntity;
import org.skyworth.ess.sku.vo.SkuHistoryVO;
import org.skyworth.ess.sku.excel.SkuHistoryExcel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 物料记录表，每次新增写入一条 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-10-31
 */
public interface SkuHistoryMapper extends BaseMapper<SkuHistoryEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param skuHistory
	 * @return
	 */
	List<SkuHistoryVO> selectSkuHistoryPage(IPage page, SkuHistoryVO skuHistory);


	/**
	 * 获取导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<SkuHistoryExcel> exportSkuHistory(@Param("ew") SkuHistoryEntity queryWrapper);

	/**
	 * 查询入库数量
	 * @param skuHistory
	 * @param skuCodeList
	 * @return
	 */
	List<SkuHistoryVO> querySkuInQuantity(@Param("params")SkuHistoryVO skuHistory, @Param("list")List<String> skuCodeList);


}
