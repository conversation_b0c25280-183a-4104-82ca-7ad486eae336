<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.sku.mapper.SkuHistoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="skuHistoryResultMap" type="org.skyworth.ess.sku.entity.SkuHistoryEntity">
        <result column="id" property="id"/>
        <result column="sku_code" property="skuCode"/>
        <result column="quantity" property="quantity"/>
        <result column="create_user" property="createUser"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_dept" property="createDept"/>
    </resultMap>


    <select id="selectSkuHistoryPage" resultMap="skuHistoryVOMap">
        select sku_code,sum(ifnull(quantity,0)) as init_quantity from sku_history where is_deleted = 0
        <if test="param2.skuCode != null and param2.skuCode!=''">
            and (sku_code like CONCAT(#{param2.skuCode}, '%') )
        </if>
        group by sku_code
    </select>


    <select id="exportSkuHistory" resultType="org.skyworth.ess.sku.excel.SkuHistoryExcel">
        select sku_code from sku_history where is_deleted = 0
        <if test="ew.skuCode != null and ew.skuCode!=''">
            and sku_code = #{ew.skuCode}
        </if>
        group by sku_code
    </select>

    <select id="querySkuInQuantity" resultMap="skuHistoryVOMap">
        SELECT sku_code,sum(ifnull(quantity,0)) as in_quantity FROM sku_history where is_deleted = 0
        <if test="params.beginDate != null">
             <![CDATA[ and DATE_FORMAT(create_time,'%Y-%m-%d') >= DATE_FORMAT(#{params.beginDate},'%Y-%m-%d')  ]]>
        </if>
        <if test="params.endDate != null">
            <![CDATA[ and DATE_FORMAT(create_time,'%Y-%m-%d') <= DATE_FORMAT(#{params.endDate},'%Y-%m-%d')  ]]>
        </if>
        <if test="params.beforeBeginDate != null">
            <![CDATA[ and DATE_FORMAT(create_time,'%Y-%m-%d') < DATE_FORMAT(#{params.beforeBeginDate},'%Y-%m-%d')  ]]>
        </if>
        <if test="list != null and list.size() > 0">
            and sku_code in
            <foreach collection="list" item="item" open="(" close=")" separator="," >
                #{item}
            </foreach>
        </if>
        group by sku_code
    </select>

    <resultMap id="skuHistoryVOMap" type="org.skyworth.ess.sku.vo.SkuHistoryVO">
        <result column="sku_code" property="skuCode"/>
        <result column="init_quantity" property="initQuantity"/>
        <result column="in_quantity" property="inQuantity"/>

    </resultMap>
</mapper>
