/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.sku.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.skyworth.ess.sku.entity.SkuStockLogEntity;
import org.skyworth.ess.sku.vo.SkuStockLogVO;
import org.skyworth.ess.sku.excel.SkuStockLogExcel;
import org.skyworth.ess.sku.mapper.SkuStockLogMapper;
import org.skyworth.ess.sku.service.ISkuStockLogService;
import org.springblade.common.constant.DictBizCodeEnum;
import org.springblade.common.utils.CommonUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.system.entity.DictBiz;
import org.springblade.system.feign.IDictBizClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 库存日志 服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-12
 */
@Service
public class SkuStockLogServiceImpl extends BaseServiceImpl<SkuStockLogMapper, SkuStockLogEntity> implements ISkuStockLogService {
	@Autowired
	private IDictBizClient dictBizClient;
	@Override
	public IPage<SkuStockLogVO> selectSkuStockLogPage(IPage<SkuStockLogVO> page, SkuStockLogVO stockLog) {
		if(StringUtil.isBlank(stockLog.getSkuCode())) {
			return page;
		}
		List<SkuStockLogVO> skuStockLogEntities = baseMapper.selectSkuStockLogPage(page, stockLog);
		if(CollectionUtil.isEmpty(skuStockLogEntities)) {
			return page;
		}
		R<List<DictBiz>> logTypeDict = dictBizClient.getListAllLang(DictBizCodeEnum.AGENT_SKU_STOCK_LOG_TYPE.getDictCode());
		List<DictBiz> logTypeList = logTypeDict.getData();
		if (CollectionUtil.isNotEmpty(logTypeList)) {
			String currentLanguage = CommonUtil.getCurrentLanguage();
			Map<String, String> logTypeMap = logTypeList.stream().filter(p -> p.getLanguage().equals(currentLanguage)).collect(Collectors.toMap(DictBiz::getDictKey, DictBiz::getDictValue));
			for (SkuStockLogEntity dbVo : skuStockLogEntities) {
				dbVo.setLogTypeName(logTypeMap.get(dbVo.getLogType()));
				dbVo.setTimeZone("UTC" + dbVo.getTimeZone());
			}
		}
		return page.setRecords(skuStockLogEntities);
	}


	@Override
	public List<SkuStockLogExcel> exportSkuStockLog(Wrapper<SkuStockLogEntity> queryWrapper) {
		List<SkuStockLogExcel> stockLogList = baseMapper.exportSkuStockLog(queryWrapper);
		//stockLogList.forEach(stockLog -> {
		//	stockLog.setTypeName(DictCache.getValue(DictEnum.YES_NO, SkuStockLog.getType()));
		//});
		return stockLogList;
	}

	@Override
	public List<SkuStockLogEntity> queryStockLog(SkuStockLogEntity skuStockLogEntity) {
		return baseMapper.selectList(Wrappers.<SkuStockLogEntity>lambdaQuery()
				.eq(SkuStockLogEntity::getSkuCode, skuStockLogEntity.getSkuCode()));
	}

}
