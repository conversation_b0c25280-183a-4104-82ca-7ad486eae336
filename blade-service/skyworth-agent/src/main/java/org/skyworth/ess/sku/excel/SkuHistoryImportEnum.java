package org.skyworth.ess.sku.excel;

import org.springblade.common.constant.CommonConstant;

import java.util.LinkedHashSet;
import java.util.Set;

/**
 * <AUTHOR>
 */

public enum SkuHistoryImportEnum {
	skuCode("Item Code","SKU编码"),
	quantity("Quantity","数量");
	private final String columnEn;
	private final String columnCn;
	SkuHistoryImportEnum(String columnEn, String columnCn) {
		this.columnEn = columnEn;
		this.columnCn = columnCn;
	}

	public static Set<String> getColumn(String language) {
		Set<String> result = new LinkedHashSet<>();
		for(SkuHistoryImportEnum item : SkuHistoryImportEnum.values()) {
			if(CommonConstant.CURRENT_LANGUAGE_ZH.equalsIgnoreCase(language)) {
				result.add(item.columnCn);
			} else {
				result.add(item.columnEn);
			}
		}
		return result;
	}
}
