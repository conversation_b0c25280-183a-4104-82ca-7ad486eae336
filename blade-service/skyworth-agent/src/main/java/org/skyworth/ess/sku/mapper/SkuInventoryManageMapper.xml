<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.sku.mapper.SkuInventoryManageMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="skuInventoryManageResultMap" type="org.skyworth.ess.sku.entity.SkuInventoryManageEntity">
        <result column="id" property="id"/>
        <result column="sku_code" property="skuCode"/>
        <result column="quantity" property="quantity"/>
        <result column="create_user" property="createUser"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_dept" property="createDept"/>
    </resultMap>


    <select id="selectSkuInventoryManagePage" resultMap="skuInventoryManageResultMap">
        select * from sku_inventory_manage where is_deleted = 0
    </select>


    <select id="exportSkuInventoryManage" resultType="org.skyworth.ess.sku.excel.SkuInventoryManageExcel">
        SELECT * FROM sku_inventory_manage ${ew.customSqlSegment}
    </select>

    <select id="queryBySkuCodeList" resultMap="skuInventoryManageResultMap">
        select * from sku_inventory_manage where is_deleted = 0
        <if test="list != null and list.size() > 0">
            and sku_code in
            <foreach collection="list" item="item" open="(" close=")" separator="," >
                #{item}
            </foreach>
        </if>
    </select>
</mapper>
