/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.sku.excel;


import com.alibaba.excel.annotation.ExcelIgnore;
import lombok.Data;

import java.util.Date;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;


/**
 * 物料记录表，每次新增写入一条 Excel实体类
 *
 * <AUTHOR>
 * @since 2023-10-31
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class SkuHistoryExcel implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * sku编码
	 */
	@ColumnWidth(20)
	@ExcelProperty("sku编码")
	private String skuCode;
	/**
	 * 物料名称
	 */
	private String skuName;
	// 设备类型
	@ExcelIgnore
	private String skuDeviceType;
	// 设备类型名称
	private String skuDeviceTypeName;
	/**
	 * 规格
	 */
	private String standards;
	/**
	 * 物料厂商（sku_company）
	 */
	private String skuCompany;
	/**
	 * 单位
	 */
	private String unit;
	// 期初数量
	private Long initQuantity = 0L;
	// 入库数量
	private Long inQuantity = 0L;
	// 出库数量
	private Long outQuantity = 0L;
	// 结存数量
	private Long surplusQuantity = 0L;

}
