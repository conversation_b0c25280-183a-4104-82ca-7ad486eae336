/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.sku.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.constant.SkuStockLogTypeEnum;
import org.skyworth.ess.design.deviceItem.service.IDeviceItemService;
import org.skyworth.ess.design.deviceItem.vo.DeviceItemVO;
import org.skyworth.ess.sku.entity.SkuBaseInfoEntity;
import org.skyworth.ess.sku.entity.SkuHistoryEntity;
import org.skyworth.ess.sku.entity.SkuInventoryManageEntity;
import org.skyworth.ess.sku.entity.SkuStockLogEntity;
import org.skyworth.ess.sku.excel.SkuHistoryExcel;
import org.skyworth.ess.sku.excel.SkuStockImportExcel;
import org.skyworth.ess.sku.mapper.SkuHistoryMapper;
import org.skyworth.ess.sku.service.ISkuBaseInfoService;
import org.skyworth.ess.sku.service.ISkuHistoryService;
import org.skyworth.ess.sku.service.ISkuInventoryManageService;
import org.skyworth.ess.sku.service.ISkuStockLogService;
import org.skyworth.ess.sku.vo.SkuHistoryVO;
import org.skyworth.ess.utils.DateUtils;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.DictBizCodeEnum;
import org.springblade.common.constant.I18nMsgCode;
import org.springblade.common.utils.CommonUtil;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.redis.lock.RedisLock;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.system.entity.DictBiz;
import org.springblade.system.feign.IDictBizClient;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 物料记录表，每次新增写入一条 服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-31
 */
@Service
@AllArgsConstructor
@Slf4j
public class SkuHistoryServiceImpl extends BaseServiceImpl<SkuHistoryMapper, SkuHistoryEntity> implements ISkuHistoryService {
    private ISkuInventoryManageService skuInventoryManageServiceImpl;
    private IDeviceItemService deviceItemService;
	private ISkuBaseInfoService skuBaseInfoService;
	private IDictBizClient dictBizClient;
	private ISkuStockLogService skuStockLogServiceImpl;
	// 如果选了时间：
	// 期初为 选择开始时间之前的 即前一日的结余数量=开始日期之前所有入库 - 开始日期之前所有出库
	// 入库数量是我们筛选的时间内添加的库存量。
	// 出库数量是我们筛选的时间内库存管理员确认订单后扣除的库存数量。
	// 结余数量是我们筛选的时间内最后一天剩下的库存量。 期初 + 入库 -出库
	// 如果没选时间：
	// 期初应该是0
	// 入库就是全部入库加起来
	// 出库就是全部出库加起来
	// 结余就是现在有多少库存
    @Override
    public IPage<SkuHistoryVO> selectSkuHistoryPage(IPage<SkuHistoryVO> page, SkuHistoryVO skuHistory) {
        // 查询所有 sku 期初数量
        List<SkuHistoryVO> skuHistoryVOS = baseMapper.selectSkuHistoryPage(page, skuHistory);
        if (CollectionUtil.isEmpty(skuHistoryVOS)) {
            return page.setRecords(new ArrayList<>());
        }
        List<String> skuCodeList = skuHistoryVOS.stream().map(SkuHistoryVO::getSkuCode).filter(StringUtil::isNotBlank).collect(Collectors.toList());
		// 根据时间范围 查询入库数量
		List<SkuHistoryVO> inQuantityList = baseMapper.querySkuInQuantity(skuHistory, skuCodeList);
		// 根据 订单仓库审批完成 查询出库数量
		DeviceItemVO query = new DeviceItemVO();
		query.setBeginDate(skuHistory.getBeginDate());
		query.setEndDate(skuHistory.getEndDate());
		List<DeviceItemVO> outQuantityList = deviceItemService.querySkuOutQuantity(query, skuCodeList);
		String currentLanguage = CommonUtil.getCurrentLanguage();
		R<List<DictBiz>> listByLang = dictBizClient.getListByLang(DictBizCodeEnum.AGENT_SKU_DEVICE_TYPE.getDictCode(), currentLanguage);
		Map<String, String> skuTypeMap = listByLang.getData().stream().collect(Collectors.toMap(DictBiz::getDictValue, DictBiz::getDictKey, (p, p1) -> p));
		if (skuHistory.getBeginDate() == null) {
			this.setNoDateRang(skuHistoryVOS, inQuantityList, outQuantityList);
		} else {
			this.setDateRang(skuHistory,skuCodeList,skuHistoryVOS, inQuantityList, outQuantityList);
		}

		this.setSkuBaseInfo(skuCodeList, skuHistoryVOS,skuTypeMap);
		return page.setRecords(skuHistoryVOS);
    }

	/**
	 *
	 * @param skuHistory 前端入参
	 * @param skuCodeList 分页sku集合
	 * @param skuHistoryVOS 分页查询sku数据
	 * @param inQuantityList 时间范围内入库数量
	 * @param outQuantityList 时间范围内出库数量
	 */
	private void setDateRang(SkuHistoryVO skuHistory,List<String> skuCodeList,List<SkuHistoryVO> skuHistoryVOS,
							 List<SkuHistoryVO> inQuantityList, List<DeviceItemVO> outQuantityList) {
		SkuHistoryVO beforeBeginDateQuery = new SkuHistoryVO();
		beforeBeginDateQuery.setBeforeBeginDate(skuHistory.getBeginDate());
		List<SkuHistoryVO> beforeInQuantityList = baseMapper.querySkuInQuantity(beforeBeginDateQuery, skuCodeList);
		DeviceItemVO beforeQuery = new DeviceItemVO();
		beforeQuery.setBeforeBeginDate(skuHistory.getBeginDate());
		List<DeviceItemVO> beforeOutQuantityList = deviceItemService.querySkuOutQuantity(beforeQuery, skuCodeList);
		for (SkuHistoryVO vo : skuHistoryVOS) {
			String skuCode = vo.getSkuCode();
			Long beforeInQty = 0L;
			Long beforeOutQty = 0L;
			for (SkuHistoryVO beforeInVO : beforeInQuantityList) {
				if (StringUtil.isNotBlank(skuCode) && skuCode.equalsIgnoreCase(beforeInVO.getSkuCode())) {
					beforeInQty = beforeInVO.getInQuantity();
					log.info("dataRang skuCode :{} , beforeInQty : {} ",skuCode,beforeInQty);
				}
			}
			for (DeviceItemVO beforeDeviceItemVO : beforeOutQuantityList) {
				if (StringUtil.isNotBlank(skuCode) && skuCode.equalsIgnoreCase(beforeDeviceItemVO.getItemCode())) {
					beforeOutQty = beforeDeviceItemVO.getOutQuantity();
					log.info("dataRang skuCode :{} , beforeOutQty : {} ",skuCode,beforeOutQty);
				}
			}
			vo.setInitQuantity(beforeInQty - beforeOutQty);
			// 入库
			for (SkuHistoryVO inVO : inQuantityList) {
				if (StringUtil.isNotBlank(skuCode) && skuCode.equalsIgnoreCase(inVO.getSkuCode())) {
					vo.setInQuantity(inVO.getInQuantity());
					log.info("dataRang skuCode :{} , InQuantity : {} ",skuCode,inVO.getInQuantity());
					break;
				}
			}
			//出库
			for (DeviceItemVO outVO : outQuantityList) {
				if (StringUtil.isNotBlank(skuCode) && skuCode.equalsIgnoreCase(outVO.getItemCode())) {
					vo.setOutQuantity(outVO.getOutQuantity());
					log.info("dataRang skuCode :{} , OutQuantity : {} ",skuCode,outVO.getOutQuantity());
					break;
				}
			}
			vo.setSurplusQuantity(vo.getInitQuantity() + vo.getInQuantity() - vo.getOutQuantity());
		}
	}

	private void setSkuBaseInfo(List<String> skuCodeList, List<SkuHistoryVO> skuHistoryVoS,Map<String, String> skuTypeMap) {
		List<SkuBaseInfoEntity> skuBaseInfoEntities = skuBaseInfoService.querySkuBaseInfoBySkuCodeList(skuCodeList);
		for (SkuHistoryVO vo : skuHistoryVoS) {
            String skuCode = vo.getSkuCode();
			for(SkuBaseInfoEntity base : skuBaseInfoEntities) {
				if (StringUtil.isNotBlank(skuCode) && skuCode.equalsIgnoreCase(base.getSkuCode())) {
					vo.setSkuCompany(base.getSkuCompany());
					vo.setSkuName(base.getSkuName());
					vo.setModel(base.getModel());
					vo.setUnit(base.getUnit());
					vo.setStandards(base.getStandards());
					vo.setSkuDeviceType(base.getSkuDeviceType());
					break;
				}
			}
			if(StringUtil.isNotBlank(skuTypeMap.get(vo.getSkuDeviceType()))) {
				vo.setSkuDeviceTypeName(skuTypeMap.get(vo.getSkuDeviceType()));
			} else {
				vo.setSkuDeviceTypeName(vo.getSkuDeviceType());
			}

        }
	}

	private void setNoDateRang(List<SkuHistoryVO> skuHistoryVOS, List<SkuHistoryVO> inQuantityList, List<DeviceItemVO> outQuantityList) {
		for (SkuHistoryVO vo : skuHistoryVOS) {
			String skuCode = vo.getSkuCode();
			vo.setInitQuantity(CommonConstant.TOP_PARENT_ID);
			for (SkuHistoryVO inVO : inQuantityList) {
				if (StringUtil.isNotBlank(skuCode) && skuCode.equalsIgnoreCase(inVO.getSkuCode())) {
					vo.setInQuantity(inVO.getInQuantity());
					break;
				}
			}
			for (DeviceItemVO outVO : outQuantityList) {
				if (StringUtil.isNotBlank(skuCode) && skuCode.equalsIgnoreCase(outVO.getItemCode())) {
					vo.setOutQuantity(outVO.getOutQuantity());
					break;
				}
			}
			vo.setSurplusQuantity(vo.getInQuantity() - vo.getOutQuantity());
		}
	}


	@Override
    public List<SkuHistoryExcel> exportSkuHistory(SkuHistoryEntity queryWrapper) {
        List<SkuHistoryExcel> skuHistoryList = baseMapper.exportSkuHistory(queryWrapper);
		if(CollectionUtil.isEmpty(skuHistoryList)) {
			return new ArrayList<>();
		}
		List<String> skuCodeList = skuHistoryList.stream().map(SkuHistoryExcel::getSkuCode).filter(StringUtil::isNotBlank).collect(Collectors.toList());
		// 根据时间范围 查询入库数量
		List<SkuHistoryVO> inQuantityList = baseMapper.querySkuInQuantity(new SkuHistoryVO(), skuCodeList);
		// 根据 订单仓库审批完成 查询出库数量
		DeviceItemVO query = new DeviceItemVO();
		List<DeviceItemVO> outQuantityList = deviceItemService.querySkuOutQuantity(query, skuCodeList);
		String currentLanguage = CommonUtil.getCurrentLanguage();
		R<List<DictBiz>> listByLang = dictBizClient.getListByLang(DictBizCodeEnum.AGENT_SKU_DEVICE_TYPE.getDictCode(), currentLanguage);
		Map<String, String> skuTypeMap = listByLang.getData().stream().collect(Collectors.toMap(DictBiz::getDictValue, DictBiz::getDictKey, (p, p1) -> p));
		this.setExportNoDateRang(skuHistoryList, inQuantityList, outQuantityList);
		this.setExportSkuBaseInfo(skuCodeList,skuHistoryList,skuTypeMap);
        return skuHistoryList;
    }

	private void setExportNoDateRang(List<SkuHistoryExcel> skuHistoryVOS, List<SkuHistoryVO> inQuantityList, List<DeviceItemVO> outQuantityList) {
		for (SkuHistoryExcel vo : skuHistoryVOS) {
			String skuCode = vo.getSkuCode();
			vo.setInitQuantity(CommonConstant.TOP_PARENT_ID);
			for (SkuHistoryVO inVO : inQuantityList) {
				if (StringUtil.isNotBlank(skuCode) && skuCode.equalsIgnoreCase(inVO.getSkuCode())) {
					vo.setInQuantity(inVO.getInQuantity());
					break;
				}
			}
			for (DeviceItemVO outVO : outQuantityList) {
				if (StringUtil.isNotBlank(skuCode) && skuCode.equalsIgnoreCase(outVO.getItemCode())) {
					vo.setOutQuantity(outVO.getOutQuantity());
					break;
				}
			}
			vo.setSurplusQuantity(vo.getInQuantity() - vo.getOutQuantity());
		}
	}

	private void setExportSkuBaseInfo(List<String> skuCodeList, List<SkuHistoryExcel> skuHistoryVOS,Map<String, String> skuTypeMap) {
		List<SkuBaseInfoEntity> skuBaseInfoEntities = skuBaseInfoService.querySkuBaseInfoBySkuCodeList(skuCodeList);
		for (SkuHistoryExcel vo : skuHistoryVOS) {
			String skuCode = vo.getSkuCode();
			for(SkuBaseInfoEntity base : skuBaseInfoEntities) {
				if (StringUtil.isNotBlank(skuCode) && skuCode.equalsIgnoreCase(base.getSkuCode())) {
					vo.setSkuCompany(base.getSkuCompany());
					vo.setSkuName(base.getSkuName());
					vo.setUnit(base.getUnit());
					vo.setStandards(base.getStandards());
					vo.setSkuDeviceType(base.getSkuDeviceType());
					break;
				}
			}
			if(StringUtil.isNotBlank(skuTypeMap.get(vo.getSkuDeviceType()))) {
				vo.setSkuDeviceTypeName(skuTypeMap.get(vo.getSkuDeviceType()));
			} else {
				vo.setSkuDeviceTypeName(vo.getSkuDeviceType());
			}
		}
	}
    @Override
    public R<String> saveSkuHistory(SkuHistoryEntity skuHistoryEntity) {
		String currentLanguage = CommonUtil.getCurrentLanguage();
        R<String> r = new R<>();
        if (StringUtil.isBlank(skuHistoryEntity.getSkuCode()) || skuHistoryEntity.getQuantity() == null) {
            r.setCode(I18nMsgCode.SKYWORTH_AGENT_SKU_100029.getCode());
            r.setMsg(I18nMsgCode.SKYWORTH_AGENT_SKU_100029.autoGetMessage(currentLanguage));
            return r;
        }
        // 保存入库记录
        this.save(skuHistoryEntity);
        // 查询库存台账是否存在，如果存在 则 库存 累加，不存在则新写入
        QueryWrapper<SkuInventoryManageEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("sku_code", skuHistoryEntity.getSkuCode());
        List<SkuInventoryManageEntity> list = skuInventoryManageServiceImpl.list(queryWrapper);
        if (CollectionUtil.isNotEmpty(list)) {
            SkuInventoryManageEntity dbEntity = list.get(0);
            UpdateWrapper<SkuInventoryManageEntity> updateWrapper = new UpdateWrapper<>();
            Long add = (dbEntity.getQuantity() == null ? 0L : dbEntity.getQuantity()) + skuHistoryEntity.getQuantity();
            BladeUser user = AuthUtil.getUser();
            updateWrapper.set("quantity", add).set("update_user_account", user.getAccount()).eq("id", dbEntity.getId());
            skuInventoryManageServiceImpl.update(updateWrapper);
        } else {
            SkuInventoryManageEntity insertEntity = new SkuInventoryManageEntity();
            insertEntity.setSkuCode(skuHistoryEntity.getSkuCode());
            insertEntity.setQuantity(skuHistoryEntity.getQuantity());
            skuInventoryManageServiceImpl.save(insertEntity);
        }
		// 增加库存日志
		this.saveStockLog(skuHistoryEntity);
		return R.success("操作成功");
    }

	private void saveStockLog(SkuHistoryEntity skuHistoryEntity) {

		SkuStockLogEntity insertEntity = new SkuStockLogEntity();
		insertEntity.setSkuCode(skuHistoryEntity.getSkuCode());
		insertEntity.setQuantity(skuHistoryEntity.getQuantity());
		insertEntity.setLogType(SkuStockLogTypeEnum.ADD.getLogTypeCode());
		insertEntity.setSkuHistoryId(skuHistoryEntity.getId());
		insertEntity.setCancelStockFlag(CommonConstant.FLAG_N);
		insertEntity.setCreateUserAccount(AuthUtil.getUserAccount());
		insertEntity.setTimeZone(DateUtils.getTimeZone());
		skuStockLogServiceImpl.save(insertEntity);
	}

	@Override
	public List<SkuHistoryVO> getSurplusQuantityBySku(Set<String> skuList) {
		List<SkuHistoryVO> result = new ArrayList<>();
		if(CollectionUtil.isEmpty(skuList)) {
			return result;
		}
		List<String> skuCodeList = skuList.stream().filter(StringUtil::isNotBlank).collect(Collectors.toList());
		// 查询入库数量
		List<SkuHistoryVO> inQuantityList = baseMapper.querySkuInQuantity(new SkuHistoryVO(), skuCodeList);
		log.info("order submit get in qty : {} ",inQuantityList);
		// 根据 订单仓库审批完成 查询出库数量
		List<DeviceItemVO> outQuantityList = deviceItemService.querySkuOutQuantity(new DeviceItemVO(), skuCodeList);
		log.info("order submit get out qty : {} ",outQuantityList);
		for(String skuCode : skuCodeList) {
			SkuHistoryVO vo = new SkuHistoryVO();
			Long inQty = 0L;
			Long outQty = 0L;
			for(SkuHistoryVO in : inQuantityList) {
				if(skuCode.equals(in.getSkuCode())) {
					inQty = in.getInQuantity();
				}
			}
			for(DeviceItemVO out : outQuantityList) {
				if(skuCode.equals(out.getItemCode())) {
					outQty = out.getOutQuantity();
				}
			}
			// 单个sku剩余库存数量
			vo.setSkuCode(skuCode);
			vo.setSurplusQuantity(inQty - outQty);
			result.add(vo);
		}

		return result;
	}

	public String addImportExcel(List<SkuStockImportExcel> data, boolean isCovered) {
		BladeUser user = AuthUtil.getUser();
		List<SkuHistoryEntity> insert = new ArrayList<>();
		data.forEach(userExcel -> {
			SkuHistoryEntity entity = Objects.requireNonNull(BeanUtil.copy(userExcel, SkuHistoryEntity.class));
			entity.setCreateUserAccount(user.getAccount());
			entity.setCreateUser(user.getUserId());
			entity.setStatus(CommonConstant.NOT_SEALED_ID);
			entity.setQuantity(Long.parseLong(userExcel.getQuantity()));
			insert.add(entity);
		});
		this.saveBatch(insert);
		List<String> skuCodeList = data.stream().map(SkuStockImportExcel::getSkuCode).distinct().collect(Collectors.toList());
		List<SkuInventoryManageEntity> skuInventoryManageEntities = skuInventoryManageServiceImpl.queryBySkuCodeList(skuCodeList);
		List<SkuInventoryManageEntity> insertOrUpdateList = new ArrayList<>();
		for(SkuStockImportExcel excel : data) {
			boolean flag = false;
			for(SkuInventoryManageEntity dbEntity : skuInventoryManageEntities) {
				if(excel.getSkuCode().equals(dbEntity.getSkuCode())) {
					flag = true;
					Long sum = dbEntity.getQuantity() + Long.parseLong(excel.getQuantity());
					dbEntity.setQuantity(sum);
					insertOrUpdateList.add(dbEntity);
				}
			}
			if(!flag) {
				SkuInventoryManageEntity insertEntity = new SkuInventoryManageEntity();
				insertEntity.setSkuCode(excel.getSkuCode());
				insertEntity.setQuantity(Long.parseLong(excel.getQuantity()));
				insertOrUpdateList.add(insertEntity);
			}
		}
		skuInventoryManageServiceImpl.saveOrUpdateBatch(insertOrUpdateList);
		// 保存库存日志
		this.batchSaveStockLog(insert);
		return "";
	}
	@Override
	public Long skuSurplusQuantity(Long stockLogId) {
		SkuStockLogEntity stockLogEntity = skuStockLogServiceImpl.getById(stockLogId);
		if(stockLogEntity == null) {
			return 0L;
		}
		Set<String> skuList = new HashSet<>();
		skuList.add(stockLogEntity.getSkuCode());
		// 查询库存
		List<SkuHistoryVO> surplusQuantityBySku = this.getSurplusQuantityBySku(skuList);
		if(CollectionUtil.isEmpty(surplusQuantityBySku)) {
			return 0L;
		}
		SkuHistoryVO surplusQuantity = surplusQuantityBySku.get(0);
		log.info("skuSurplusQuantity : {}" ,surplusQuantity);
		return surplusQuantity.getSurplusQuantity() - stockLogEntity.getQuantity();
	}
	@Override
	@RedisLock(value = CommonConstant.REDIS_SKU_STOCK_LOCK)
	public boolean cancelSkuHistory(Long stockLogId,String remark) {
		SkuStockLogEntity stockLogEntity = skuStockLogServiceImpl.getById(stockLogId);
		if(stockLogEntity == null) {
			return true;
		}
		Set<String> skuList = new HashSet<>();
		skuList.add(stockLogEntity.getSkuCode());
		// 查询库存是否足够
		List<SkuHistoryVO> surplusQuantityBySku = this.getSurplusQuantityBySku(skuList);
		if(CollectionUtil.isEmpty(surplusQuantityBySku)) {
			return true;
		}
		// 只有一个sku
		SkuHistoryVO surplusQuantity = surplusQuantityBySku.get(0);
		if (stockLogEntity.getQuantity() > surplusQuantity.getSurplusQuantity()) {
			throw new BusinessException("stock.surplusQuantity.invalid", surplusQuantity.getSkuCode());
		}
		// 删除库存
		this.deleteLogic(Lists.newArrayList(stockLogEntity.getSkuHistoryId()));
		// 将增加库存 标识修改为 已点击
		LambdaUpdateWrapper<SkuStockLogEntity> updateLogWrapper = Wrappers.<SkuStockLogEntity>lambdaUpdate()
				.set(SkuStockLogEntity::getCancelStockFlag, CommonConstant.FLAG_Y)
				.eq(SkuStockLogEntity::getId, stockLogId);
		skuStockLogServiceImpl.update(updateLogWrapper);
		// 取消静态台账
		this.cancelStockManage(stockLogEntity);
		// 增加操作日志
		this.addStockLog(stockLogEntity,remark);
		return true;
	}

	private void addStockLog(SkuStockLogEntity stockLogEntity,String remark) {
		SkuStockLogEntity insertStockLog = new SkuStockLogEntity();
		insertStockLog.setSkuCode(stockLogEntity.getSkuCode());
		insertStockLog.setQuantity(stockLogEntity.getQuantity());
		insertStockLog.setLogType(SkuStockLogTypeEnum.CANCEL.getLogTypeCode());
		insertStockLog.setSkuHistoryId(stockLogEntity.getSkuHistoryId());
		insertStockLog.setCancelStockFlag(CommonConstant.FLAG_NA);
		insertStockLog.setCreateUserAccount(AuthUtil.getUserAccount());
		insertStockLog.setTimeZone(DateUtils.getTimeZone());
		insertStockLog.setRemark(remark);
		skuStockLogServiceImpl.save(insertStockLog);
	}

	private void cancelStockManage(SkuStockLogEntity stockLogEntity) {
		QueryWrapper<SkuInventoryManageEntity> queryWrapper = new QueryWrapper<>();
		queryWrapper.eq("sku_code", stockLogEntity.getSkuCode());
		List<SkuInventoryManageEntity> list = skuInventoryManageServiceImpl.list(queryWrapper);
		if (CollectionUtil.isNotEmpty(list)) {
			SkuInventoryManageEntity dbEntity = list.get(0);
			UpdateWrapper<SkuInventoryManageEntity> updateWrapper = new UpdateWrapper<>();
			Long add = (dbEntity.getQuantity() == null ? 0L : dbEntity.getQuantity()) - stockLogEntity.getQuantity();
			BladeUser user = AuthUtil.getUser();
			updateWrapper.set("quantity", add).set("update_user_account", user.getAccount()).eq("id", dbEntity.getId());
			skuInventoryManageServiceImpl.update(updateWrapper);
		}
	}

	private void batchSaveStockLog(List<SkuHistoryEntity> insert) {
		List<SkuStockLogEntity> insertStockLogList = new ArrayList<>();
		BladeUser user = AuthUtil.getUser();
		String timeZone = DateUtils.getTimeZone();
		for (SkuHistoryEntity entity : insert) {
			SkuStockLogEntity insertStockLog = new SkuStockLogEntity();
			insertStockLog.setSkuCode(entity.getSkuCode());
			insertStockLog.setQuantity(entity.getQuantity());
			insertStockLog.setLogType(SkuStockLogTypeEnum.ADD.getLogTypeCode());
			insertStockLog.setSkuHistoryId(entity.getId());
			insertStockLog.setCancelStockFlag(CommonConstant.FLAG_N);
			insertStockLog.setCreateUserAccount(user.getAccount());
			insertStockLog.setTimeZone(timeZone);
			insertStockLogList.add(insertStockLog);
		}
		skuStockLogServiceImpl.saveBatch(insertStockLogList);
	}
}
