/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.sku.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.commons.text.StringEscapeUtils;
import org.jetbrains.annotations.Nullable;
import org.skyworth.ess.sku.entity.SkuBaseInfoEntity;
import org.skyworth.ess.sku.excel.SkuBaseInfoExcel;
import org.skyworth.ess.sku.mapper.SkuBaseInfoMapper;
import org.skyworth.ess.sku.service.ISkuBaseInfoService;
import org.skyworth.ess.sku.vo.SkuBaseInfoResultVO;
import org.skyworth.ess.sku.vo.SkuBaseInfoVO;
import org.springblade.common.constant.DictBizCodeEnum;
import org.springblade.common.constant.I18nMsgCode;
import org.springblade.common.utils.CommonUtil;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.system.entity.DictBiz;
import org.springblade.system.feign.IDictBizClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 物料基础信息表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-31
 */
@Service
public class SkuBaseInfoServiceImpl extends BaseServiceImpl<SkuBaseInfoMapper, SkuBaseInfoEntity> implements ISkuBaseInfoService {
	@Autowired
	private IDictBizClient dictBizClient;

	@Override
	public IPage<SkuBaseInfoVO> selectSkuBaseInfoPage(IPage<SkuBaseInfoVO> page, SkuBaseInfoVO skuBaseInfo) {
		return page.setRecords(baseMapper.selectSkuBaseInfoPage(page, skuBaseInfo));
	}


	@Override
	public List<SkuBaseInfoExcel> exportSkuBaseInfo(Wrapper<SkuBaseInfoEntity> queryWrapper) {
		List<SkuBaseInfoExcel> skuBaseInfoList = baseMapper.exportSkuBaseInfo(queryWrapper);
		//skuBaseInfoList.forEach(skuBaseInfo -> {
		//	skuBaseInfo.setTypeName(DictCache.getValue(DictEnum.YES_NO, SkuBaseInfo.getType()));
		//});
		return skuBaseInfoList;
	}

	@Override
	public List<SkuBaseInfoEntity> querySkuBaseInfoBySkuCodeList(List<String> skuCodeList) {
		return baseMapper.querySkuBaseInfoBySkuCodeList(skuCodeList);
	}

	@Override
	public R<String> saveSku(SkuBaseInfoEntity skuBaseInfoEntity) {
		R<String> notNull = this.validateFieldNotNull(skuBaseInfoEntity);
		if (notNull != null) {
			return notNull;
		}
		R<String> repeat = this.validateDataRepeat(skuBaseInfoEntity, "add");
		if (repeat != null) {
			return repeat;
		}
//        skuBaseInfoEntity.setSkuCode(StringUtil.random(20));
		this.save(skuBaseInfoEntity);
		return R.success("操作成功");
	}

	@Override
	public R<String> updateSku(SkuBaseInfoEntity skuBaseInfoEntity) {
		R<String> notNull = this.validateFieldNotNull(skuBaseInfoEntity);
		if (notNull != null) {
			return notNull;
		}
		R<String> repeat = this.validateDataRepeat(skuBaseInfoEntity, "update");
		if (repeat != null) {
			return repeat;
		}
		this.updateById(skuBaseInfoEntity);
		return R.success("操作成功");
	}

	@Override
	public R<List<SkuBaseInfoResultVO>> pullDown(SkuBaseInfoVO skuBaseInfo) {
		List<SkuBaseInfoResultVO> skuBaseInfoResultVoList = baseMapper.pullDown(skuBaseInfo);
		String currentLanguage = CommonUtil.getCurrentLanguage();
		R<List<DictBiz>> listByLang = dictBizClient.getListByLang(DictBizCodeEnum.AGENT_SKU_DEVICE_TYPE.getDictCode(), currentLanguage);
		Map<String, String> skuTypeMap = listByLang.getData().stream().collect(Collectors.toMap(DictBiz::getDictValue, DictBiz::getDictKey, (p, p1) -> p));
		for (SkuBaseInfoResultVO e : skuBaseInfoResultVoList) {
			e.setSkuDeviceTypeName(skuTypeMap.get(e.getSkuDeviceType()));
		}
		return R.data(skuBaseInfoResultVoList);

	}

	@Override
	public R<SkuBaseInfoVO> basePackageSkuInfo(String basePackage) {
		SkuBaseInfoVO skuBaseInfoVO = new SkuBaseInfoVO();
		List<SkuBaseInfoResultVO> result = new ArrayList<>();
		R<List<DictBiz>> agentItemBasePackage = dictBizClient.queryChildByDictKey(DictBizCodeEnum.AGENT_ITEM_BASE_PACKAGE.getDictCode(),
			basePackage, CommonUtil.getCurrentLanguage());
		List<DictBiz> data = agentItemBasePackage.getData();
		if (CollectionUtil.isEmpty(data)) {
			return R.data(skuBaseInfoVO);
		}
		List<String> skuCodeList = data.stream().map(DictBiz::getDictKey).filter(p -> !basePackage.equals(p)).collect(Collectors.toList());
		List<SkuBaseInfoEntity> skuBaseInfoEntities = new ArrayList<>();
		if(CollectionUtil.isNotEmpty(skuCodeList)) {
			skuBaseInfoEntities = baseMapper.querySkuBaseInfoBySkuCodeList(skuCodeList);
		}
		R<List<DictBiz>> listByLang = dictBizClient.getListByLang(DictBizCodeEnum.AGENT_SKU_DEVICE_TYPE.getDictCode(), CommonUtil.getCurrentLanguage());
		Map<String, String> skuTypeMap = listByLang.getData().stream().collect(Collectors.toMap(DictBiz::getDictValue, DictBiz::getDictKey, (p, p1) -> p));
		// 取 业务字典上的 每个物料的数量
		Map<String, String> skuCodeMapQuantity = data.stream().filter(p -> !basePackage.equals(p.getDictKey())).collect(Collectors.toMap(DictBiz::getDictKey, DictBiz::getAttribute1));
		Map<String, Integer> skuSortMap = data.stream().collect(Collectors.toMap(DictBiz::getDictKey, DictBiz::getSort));
		for (SkuBaseInfoEntity skuBaseInfoEntity : skuBaseInfoEntities) {
			SkuBaseInfoResultVO vo = new SkuBaseInfoResultVO();
			BeanUtil.copy(skuBaseInfoEntity, vo);
			Integer sort = skuSortMap.get(skuBaseInfoEntity.getSkuCode());
			vo.setQuantity(skuCodeMapQuantity.get(skuBaseInfoEntity.getSkuCode()));
			vo.setSkuDeviceTypeName(skuTypeMap.get(skuBaseInfoEntity.getSkuDeviceType()));
			vo.setItemCode(skuBaseInfoEntity.getSkuCode());
			vo.setSort(sort);
			result.add(vo);
		}
		String basePackagePrice = data.stream().filter(p -> basePackage.equals(p.getDictKey())).map(DictBiz::getAttribute1).findFirst().orElse("0");
		result.sort(Comparator.comparing(SkuBaseInfoResultVO::getSort));
		skuBaseInfoVO.setBasePackageList(result);
		skuBaseInfoVO.setBasePackagePrice(new BigDecimal(basePackagePrice));
		return R.data(skuBaseInfoVO);
	}

	@Nullable
	private R<String> validateDataRepeat(SkuBaseInfoEntity skuBaseInfoEntity, String addOrUpdate) {
		R<String> r = new R<>();
		QueryWrapper<SkuBaseInfoEntity> queryWrapper = new QueryWrapper<>();
		queryWrapper.eq("sku_code", skuBaseInfoEntity.getSkuCode());
//			.eq("sku_device_type", skuBaseInfoEntity.getSkuDeviceType())
//                .eq("sku_name", skuBaseInfoEntity.getSkuName())
//                .eq("sku_company", skuBaseInfoEntity.getSkuCompany())
//                .eq("standards", skuBaseInfoEntity.getStandards())
//                .eq("unit", skuBaseInfoEntity.getUnit());
		List<SkuBaseInfoEntity> skuBaseInfoEntities = baseMapper.selectList(queryWrapper);

		if (CollectionUtil.isNotEmpty(skuBaseInfoEntities)) {
			if ("update".equalsIgnoreCase(addOrUpdate)) {
				// 如果存在 id不相同的记录，则表示有重复数据
				boolean present = skuBaseInfoEntities.stream().anyMatch(p -> !p.getId().equals(skuBaseInfoEntity.getId()));
				// present为true表示 存在相同 数据，取反没有相同数据
				if (!present) {
					return null;
				}
			}
			String currentLanguage = CommonUtil.getCurrentLanguage();
			r.setCode(I18nMsgCode.SKYWORTH_AGENT_SKU_100033.getCode());
			r.setMsg(I18nMsgCode.SKYWORTH_AGENT_SKU_100033.autoGetMessage(currentLanguage));
			throw new BusinessException("agent.sku.base.info.save.can.not.empty");
		}
		return null;
	}

	private R<String> validateFieldNotNull(SkuBaseInfoEntity skuBaseInfoEntity) {
		R<String> r = new R<>();
		if ( StringUtil.isAnyBlank(skuBaseInfoEntity.getSkuCode(), skuBaseInfoEntity.getSkuName(), skuBaseInfoEntity.getSkuCompany()
			, skuBaseInfoEntity.getUnit(), skuBaseInfoEntity.getStandards(), skuBaseInfoEntity.getSkuDeviceType())) {
			String currentLanguage = CommonUtil.getCurrentLanguage();
			r.setCode(I18nMsgCode.SKYWORTH_AGENT_SKU_100030.getCode());
			r.setMsg(I18nMsgCode.SKYWORTH_AGENT_SKU_100030.autoGetMessage(currentLanguage));
			throw new BusinessException("agent.sku.base.info.save.can.not.empty");
		}
		return null;
	}
}
