/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.sku.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.skyworth.ess.sku.entity.SkuStockLogEntity;
import org.skyworth.ess.sku.excel.SkuHistoryExportEnum;
import org.skyworth.ess.sku.excel.SkuHistoryImportEnum;
import org.skyworth.ess.sku.excel.SkuStockImportExcel;
import org.springblade.common.constant.BizConstant;
import org.springblade.common.excel.ExcelBuildUtil;
import org.springblade.common.excel.ExcelImportServiceInterface;
import org.springblade.common.utils.CommonUtil;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringUtil;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.sku.entity.SkuHistoryEntity;
import org.skyworth.ess.sku.vo.SkuHistoryVO;
import org.skyworth.ess.sku.excel.SkuHistoryExcel;
import org.skyworth.ess.sku.wrapper.SkuHistoryWrapper;
import org.skyworth.ess.sku.service.ISkuHistoryService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import java.util.*;
import java.util.function.BiFunction;
import javax.servlet.http.HttpServletResponse;

/**
 * 物料记录表，每次新增写入一条 控制器
 *
 * <AUTHOR>
 * @since 2023-10-31
 */
@RestController
@AllArgsConstructor
@RequestMapping("/skuHistory")
@Api(value = "物料记录表，每次新增写入一条", tags = "物料记录表，每次新增写入一条接口")
public class SkuHistoryController extends BladeController {

	private final ISkuHistoryService skuHistoryService;
	private final ExcelImportServiceInterface<SkuStockImportExcel> skuHistoryImportImpl;

	/**
	 * 物料记录表，每次新增写入一条 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入skuHistory")
	@PreAuth("hasPermission('agent:skuHistory:detail')")
	public R<SkuHistoryVO> detail(SkuHistoryEntity skuHistory) {
		SkuHistoryEntity detail = skuHistoryService.getOne(Condition.getQueryWrapper(skuHistory));
		return R.data(SkuHistoryWrapper.build().entityVO(detail));
	}
	/**
	 * 物料记录表，每次新增写入一条 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入skuHistory")
	@PreAuth("hasPermission('agent:skuHistory:list')")
	public R<IPage<SkuHistoryVO>> list(@ApiIgnore @RequestParam Map<String, Object> skuHistory, Query query) {
		IPage<SkuHistoryEntity> pages = skuHistoryService.page(Condition.getPage(query), Condition.getQueryWrapper(skuHistory, SkuHistoryEntity.class));
		return R.data(SkuHistoryWrapper.build().pageVO(pages));
	}

	/**
	 * 物料记录表，每次新增写入一条 自定义分页
	 */
	@PostMapping("/page/{size}/{current}")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入skuHistory")
	@PreAuth("hasPermission('agent:skuHistory:page')")
	public R<IPage<SkuHistoryVO>> page(@RequestBody SkuHistoryVO skuHistory, Query query) {
		IPage<SkuHistoryVO> pages = skuHistoryService.selectSkuHistoryPage(Condition.getPage(query), skuHistory);
		return R.data(pages);
	}

	/**
	 * 物料记录表，每次新增写入一条 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入skuHistory")
	@PreAuth("hasPermission('agent:skuHistory:save')")
	public R save(@Valid @RequestBody SkuHistoryEntity skuHistory) {
		return skuHistoryService.saveSkuHistory(skuHistory);
	}

	/**
	 * 物料记录表，每次新增写入一条 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入skuHistory")
	@PreAuth("hasPermission('agent:skuHistory:update')")
	public R update(@Valid @RequestBody SkuHistoryEntity skuHistory) {
		return R.status(skuHistoryService.updateById(skuHistory));
	}

	/**
	 * 物料记录表，每次新增写入一条 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入skuHistory")
	@PreAuth("hasPermission('agent:skuHistory:submit')")
	public R submit(@Valid @RequestBody SkuHistoryEntity skuHistory) {
		return R.status(skuHistoryService.saveOrUpdate(skuHistory));
	}

	/**
	 * 物料记录表，每次新增写入一条 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	@PreAuth("hasPermission('agent:skuHistory:remove')")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(skuHistoryService.deleteLogic(Func.toLongList(ids)));
	}

	/**
	 * 导出数据
	 */
	@GetMapping("/export-skuHistory")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出数据", notes = "传入skuHistory")
//	@PreAuth("hasPermission('agent:skuHistory:export-skuHistory')")
	public void exportSkuHistory(@ApiIgnore @RequestParam Map<String, Object> skuHistory, BladeUser bladeUser, HttpServletResponse response) {
		SkuHistoryEntity queryWrapper = new SkuHistoryEntity();
		if(Objects.nonNull(skuHistory.get("skuCode"))) {
			queryWrapper.setSkuCode((String)skuHistory.get("skuCode"));
		}
		List<SkuHistoryExcel> list = skuHistoryService.exportSkuHistory(queryWrapper);
		String currentLanguage = CommonUtil.getCurrentLanguage();
		Set<String> column = SkuHistoryExportEnum.getColumn(currentLanguage);
		List<List<String>> headList = ExcelBuildUtil.buildI18HeadList(column);
		ExcelUtil.export(response, "skuStockExcel" + DateUtil.time(), "skuStockExcel",
			list, SkuHistoryExcel.class,headList);
	}

	@GetMapping("/import-template")
	@ApiOperationSupport(order = 14)
	@ApiOperation(value = "导入模板")
//	@PreAuth("hasPermission('agent:skuHistoryExcel:importTemplate')")
	public void exportImportTemplate(HttpServletResponse response) {
		List<SkuStockImportExcel> list = new ArrayList<>();
		String currentLanguage = CommonUtil.getCurrentLanguage();
		Set<String> column = SkuHistoryImportEnum.getColumn(currentLanguage);
		List<List<String>> headList = ExcelBuildUtil.buildI18HeadList(column);
		ExcelUtil.export(response, "template", "SkuStockExcel", list, SkuStockImportExcel.class,headList);
	}

	@PostMapping("/import-add-skuHistory")
	@ApiOperationSupport(order = 15)
	@ApiOperation(value = "导入")
//	@PreAuth("hasPermission('agent:skuHistoryExcel:import')")
	public R addImport(MultipartFile file, Integer isCovered, HttpServletRequest request) {
		String currentLanguage = CommonUtil.getCurrentLanguage();
		BiFunction<List<SkuStockImportExcel>, Boolean, String> fun = skuHistoryService::addImportExcel;
		String result = skuHistoryImportImpl.imporExcel(file, SkuHistoryImportEnum.getColumn(currentLanguage),
			SkuStockImportExcel.class, fun);
		if(StringUtil.isNotBlank(result)) {
			return R.fail(result);
		} else {
			return R.data(result);
		}
	}

	@GetMapping("/sku-surplus-quantity")
	@ApiOperationSupport(order = 15)
	@ApiOperation(value = "sku剩余数量")
//	@PreAuth("hasPermission('agent:skuHistory:skuSurplusQuantity')")
	public R skuSurplusQuantity(@RequestParam Long stockLogId) {
		return R.data(skuHistoryService.skuSurplusQuantity(stockLogId));
	}

	@GetMapping("/cancel-skuHistory")
	@ApiOperationSupport(order = 15)
	@ApiOperation(value = "取消库存")
//	@PreAuth("hasPermission('agent:skuHistory:cancelSkuHistory')")
	public R cancelSkuHistory(@RequestParam Long stockLogId,@RequestParam String remark) {
		return R.data(skuHistoryService.cancelSkuHistory(stockLogId,remark));
	}

}
