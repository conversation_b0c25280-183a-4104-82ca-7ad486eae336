<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.sku.mapper.SkuBaseInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="skuBaseInfoResultMap" type="org.skyworth.ess.sku.entity.SkuBaseInfoEntity">
        <result column="id" property="id"/>
        <result column="sku_code" property="skuCode"/>
        <result column="sku_device_type" property="skuDeviceType"/>
        <result column="sku_name" property="skuName"/>
        <result column="sku_company" property="skuCompany"/>
        <result column="standards" property="standards"/>
        <result column="model" property="model"/>
        <result column="unit" property="unit"/>
        <result column="create_user" property="createUser"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_dept" property="createDept"/>
        <result column="category" property="category"/>
    </resultMap>


    <select id="selectSkuBaseInfoPage" resultMap="skuBaseInfoResultMap">
        select * from sku_base_info where is_deleted = 0
        <if test="param2.skuCodeOrName != null and param2.skuCodeOrName!=''">
            and (sku_code like CONCAT(#{param2.skuCodeOrName}, '%') or sku_name like CONCAT(#{param2.skuCodeOrName},
            '%') )
        </if>
    </select>


    <select id="exportSkuBaseInfo" resultType="org.skyworth.ess.sku.excel.SkuBaseInfoExcel">
        SELECT * FROM sku_base_info ${ew.customSqlSegment}
    </select>

    <select id="querySkuBaseInfoBySkuCodeList" resultMap="skuBaseInfoResultMap">
        SELECT * FROM sku_base_info where is_deleted = 0
        <if test="list != null and list.size() > 0">
            and sku_code in
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="pullDown" resultMap="skuBaseInfoVoResultMap">
        select * from sku_base_info where is_deleted = 0
        <if test="params.skuCodeOrName != null and params.skuCodeOrName!=''">
            and (sku_code like CONCAT('%', #{params.skuCodeOrName}, '%') or sku_name like CONCAT('%',
            #{params.skuCodeOrName}, '%') )
        </if>
        limit 100
    </select>

    <resultMap id="skuBaseInfoVoResultMap" type="org.skyworth.ess.sku.vo.SkuBaseInfoResultVO">
        <result column="sku_code" property="skuCode"/>
        <result column="sku_name" property="skuName"/>
        <result column="sku_device_type" property="skuDeviceType"/>
        <result column="sku_company" property="skuCompany"/>
        <result column="standards" property="standards"/>
        <result column="model" property="model"/>
        <result column="unit" property="unit"/>
        <result column="price" property="price"/>
    </resultMap>
</mapper>
