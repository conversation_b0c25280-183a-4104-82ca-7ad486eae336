/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.sku.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.SkyWorthEntity;

/**
 * 物料基础信息表 实体类
 *
 * <AUTHOR>
 * @since 2023-10-31
 */
@Data
@TableName("sku_base_info")
@ApiModel(value = "SkuBaseInfo对象", description = "物料基础信息表")
@EqualsAndHashCode(callSuper = true)
public class SkuBaseInfoEntity extends SkyWorthEntity {

	/**
	 * sku编码
	 */
	@ApiModelProperty(value = "sku编码")
	private String skuCode;
	/**
	 * 设备类型（取数据字典sku_device_type）
	 */
	@ApiModelProperty(value = "设备类型（取数据字典sku_device_type）")
	private String skuDeviceType;
	/**
	 * 物料名称
	 */
	@ApiModelProperty(value = "物料名称")
	private String skuName;
	/**
	 * 物料厂商（sku_company）
	 */
	@ApiModelProperty(value = "物料厂商（sku_company）")
	private String skuCompany;
	/**
	 * 规格
	 */
	@ApiModelProperty(value = "规格")
	private String standards;
	/**
	 * 型号
	 */
	@ApiModelProperty(value = "型号")
	private String model;
	/**
	 * 单位
	 */
	@ApiModelProperty(value = "单位")
	private String unit;
	/**
	 * 价格
	 */
//	@ApiModelProperty(value = "价格")
//	private BigDecimal price;
	/**
	 * 类别
	 */
	@ApiModelProperty(value = "类别")
	private String category;

}
