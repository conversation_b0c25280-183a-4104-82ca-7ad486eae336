package org.skyworth.ess.exitfactoryquality.excel;

import lombok.Getter;
import org.springblade.common.constant.CommonConstant;

import java.util.*;

/**
 * <AUTHOR>
 */
@Getter
public enum ExitFactoryBaseColumnEnum {
    deviceSerialNumber(ExitFactorySheetEnum.base,"Device SN","设备SN"),
    deviceTypeName(ExitFactorySheetEnum.base,"Device Model","设备型号"),
    companyName(ExitFactorySheetEnum.base,"Manufacturer","厂家"),
    qualityGuaranteeYear(ExitFactorySheetEnum.base,"Warranty Period","质保年限"),
    exitFactoryDate(ExitFactorySheetEnum.base,"Factory Date","出厂日期"),
    batteryRatedCapacity(ExitFactorySheetEnum.base,"Rated Capacity of Battery(kwh)","电池额定容量(kWh)"),
    batteryRatedAcDischargeCapacity(ExitFactorySheetEnum.base,"Rated Total AC Discharge Power of Battery(W)","电池额定交流放电总功率(W)"),
    batteryRatedDischargeCapacity(ExitFactorySheetEnum.base,"Rated Total Discharge Power of Battery(W)","电池额定放电总功率(W)"),
    batteryRatedDcDischargeCapacity(ExitFactorySheetEnum.base,"Rated Total DC Discharge Power of Battery(W)","电池额定直流放电总功率(W)"),
    batteryRatedChargeCapacity(ExitFactorySheetEnum.base,"Rated Total Charging Power of Battery(W)","电池额定充电总功率(W)"),
    batteryRatedAndersonChargeCapacity(ExitFactorySheetEnum.base,"Rated Anderson Interface Charging Total Power of Battery(W)","电池额定安德森接口充电总功率(W)"),
    bluetoothProtocolVersion(ExitFactorySheetEnum.base,"Bluetooth Protocol Version","蓝牙协议版本"),
    motherboardHardwareVersion(ExitFactorySheetEnum.base,"Motherboard Hardware Version","主板硬件版本"),
    ;
    private final ExitFactorySheetEnum sheetEnum;
    private final String columnEn;
    private final String columnCn;
    ExitFactoryBaseColumnEnum(ExitFactorySheetEnum sheetEnum,String columnEn, String columnCn) {
        this.sheetEnum = sheetEnum;
        this.columnEn = columnEn;
        this.columnCn = columnCn;
    }

    public static Set<String> getColumn(String language) {
        Set<String> result = new LinkedHashSet<>();
        for(ExitFactoryBaseColumnEnum item : ExitFactoryBaseColumnEnum.values()) {
            if(CommonConstant.CURRENT_LANGUAGE_ZH.equalsIgnoreCase(language)) {
                result.add(item.columnCn);
            } else {
                result.add(item.columnEn);
            }
        }
        return result;
    }

    public static Map<String,ExitFactorySheetEnum> getColumnAndSheetEnum(String language) {
        Map<String,ExitFactorySheetEnum> result = new LinkedHashMap<>();
        for(ExitFactoryBaseColumnEnum item : ExitFactoryBaseColumnEnum.values()) {
            if(CommonConstant.CURRENT_LANGUAGE_ZH.equalsIgnoreCase(language)) {
                result.put(item.getColumnCn(),item.getSheetEnum());
            } else {
                result.put(item.getColumnEn(),item.getSheetEnum());
            }
        }
        return result;
    }

    public static List<List<String>> getMultipleHeader(String language) {
        List<List<String>> result = new ArrayList<>();
        List<String> header0 = new ArrayList<>();
        List<String> header1 = new ArrayList<>();
        for(ExitFactoryBaseColumnEnum item : ExitFactoryBaseColumnEnum.values()) {
            if(CommonConstant.CURRENT_LANGUAGE_ZH.equalsIgnoreCase(language)) {
                header0.add(item.getSheetEnum().getColumnCn());
                header1.add(item.getColumnCn());
            } else {
                header0.add(item.getSheetEnum().getColumnEn());
                header1.add(item.getColumnEn());
            }
        }
        result.add(header0);
        result.add(header1);
        return result;
    }


}
