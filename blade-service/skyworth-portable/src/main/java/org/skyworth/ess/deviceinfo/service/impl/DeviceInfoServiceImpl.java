package org.skyworth.ess.deviceinfo.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import org.skyworth.ess.constant.DictBizCodeEnum;
import org.skyworth.ess.deviceinfo.entity.DeviceInfoEntity;
import org.skyworth.ess.deviceinfo.mapper.DeviceInfoMapper;
import org.skyworth.ess.deviceinfo.service.IDeviceInfoService;
import org.skyworth.ess.deviceinfo.vo.DeviceInfoVO;
import org.skyworth.ess.exitfactoryquality.entity.DeviceExitFactoryInfoEntity;
import org.skyworth.ess.exitfactoryquality.service.IDeviceExitFactoryInfoService;
import org.skyworth.ess.ota.feign.IDistributeUpgradeClient;
import org.springblade.common.constant.BizConstant;
import org.springblade.common.utils.CommonUtil;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.system.entity.DictBiz;
import org.springblade.system.entity.Region;
import org.springblade.system.entity.User;
import org.springblade.system.entity.UserApp;
import org.springblade.system.feign.IDictBizClient;
import org.springblade.system.feign.ISysClient;
import org.springblade.system.feign.IUserSearchClient;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 设备信息表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-01-23
 */
@Service
@AllArgsConstructor
public class DeviceInfoServiceImpl extends BaseServiceImpl<DeviceInfoMapper, DeviceInfoEntity> implements IDeviceInfoService {
	private final ISysClient sysClient;
	private final IUserSearchClient userSearchClient;
	private final IDeviceExitFactoryInfoService deviceExitFactoryInfoService;
	private final IDistributeUpgradeClient distributeUpgradeClient;
	private final IDictBizClient dictBizClient;

	/**
	 * 分页查询设备信息
	 *
	 * @param page       分页参数
	 * @param deviceInfo 入参
	 * @return IPage<DeviceInfoVO>
	 * <AUTHOR>
	 * @since 2024/1/24 16:12
	 **/
	@Override
	public IPage<DeviceInfoVO> queryPage(IPage<DeviceInfoVO> page, Map<String, Object> deviceInfo) {
		List<DeviceInfoVO> deviceInfoVOList = baseMapper.queryPage(page, deviceInfo);
		if (CollectionUtils.isEmpty(deviceInfoVOList)) {
			return page;
		}
		// 设置所属区域
		assignmentDistrict(deviceInfoVOList);
		// 设置用户信息
		assignmentUserInfo(deviceInfoVOList);
		page.setRecords(deviceInfoVOList);
		return page;
	}

	/**
	 * 数据统计
	 *
	 * @param deviceInfo 入参
	 * @return JSONObject
	 * <AUTHOR>
	 * @since 2024/1/24 16:33
	 **/
	@Override
	public JSONObject queryPageStatistics(Map<String, Object> deviceInfo) {
		JSONObject jsonObject = baseMapper.queryPageStatistics(deviceInfo);
		if (jsonObject == null) {
			jsonObject = new JSONObject();
			jsonObject.put("sumBatteryRatedCapacity", 0);
			jsonObject.put("activatedDevicesNums", 0);
		}
		return jsonObject;
	}

	/**
	 * 设置用户信息
	 *
	 * @param deviceInfoVOList 入参
	 * <AUTHOR>
	 * @since 2024/1/24 16:11
	 **/
	private void assignmentUserInfo(List<DeviceInfoVO> deviceInfoVOList) {
		// 设置手机号和姓名
		List<Long> userIds = deviceInfoVOList.stream().map(DeviceInfoVO::getCreateUser).filter(Objects::nonNull).collect(Collectors.toList());
		List<User> users = userSearchClient.listByUserIds(userIds).getData();
		if (CollectionUtils.isNotEmpty(users)) {
			Map<Long, User> userMap = users.stream().collect(Collectors.toMap(User::getId, Function.identity(), (a, b) -> a));
			deviceInfoVOList.forEach(a -> {
				User user = userMap.get(a.getCreateUser());
				if (user != null) {
					a.setUserName(user.getRealName());
					a.setUserPhone(user.getPhone());
				}
			});
		}
	}

	/**
	 * 设置所属区域
	 *
	 * @param deviceInfoVOList 入参
	 * <AUTHOR>
	 * @since 2024/1/24 15:49
	 **/
	private void assignmentDistrict(List<DeviceInfoVO> deviceInfoVOList) {
		// 设置所属区域
		List<String> countryCodes = deviceInfoVOList.stream().map(DeviceInfoVO::getCountryCode).filter(StringUtil::isNotBlank).collect(Collectors.toList());
		List<String> provinceCodes = deviceInfoVOList.stream().map(DeviceInfoVO::getProvinceCode).filter(StringUtil::isNotBlank).collect(Collectors.toList());
		List<String> cityCodes = deviceInfoVOList.stream().map(DeviceInfoVO::getCityCode).filter(StringUtil::isNotBlank).collect(Collectors.toList());
		countryCodes.addAll(provinceCodes);
		countryCodes.addAll(cityCodes);
		List<Region> regionCodeList = sysClient.getRegionList(countryCodes).getData();
		if (CollectionUtils.isNotEmpty(regionCodeList)) {
			Map<String, String> regionMap = regionCodeList.stream().collect(Collectors.toMap(Region::getCode, Region::getName, (a, b) -> a));
			deviceInfoVOList.forEach(a -> {
				String countryName = regionMap.get(a.getCountryCode());
				String provinceName = regionMap.get(a.getProvinceCode());
				String cityName = regionMap.get(a.getCityCode());
				StringBuilder district = new StringBuilder();
				if (StringUtil.isNotBlank(cityName)) {
					district.append(cityName).append(" ");
				}
				if (StringUtil.isNotBlank(provinceName)) {
					district.append(provinceName).append(" ");
				}
				if (StringUtil.isNotBlank(countryName)) {
					district.append(countryName);
				}
				a.setDistrict(district.toString());
			});
		}
	}

	/**
	 * 设备详情
	 *
	 * @param id 入参
	 * @return JSONObject
	 * <AUTHOR>
	 * @since 2024/1/25 14:41
	 **/
	@Override
	public JSONObject detail(Long id) {
		JSONObject jsonObject = new JSONObject();
		DeviceInfoEntity deviceInfo = baseMapper.selectById(id);
		jsonObject.put("numberOfBatteryCycles", deviceInfo.getNumberOfBatteryCycles());
		jsonObject.put("batterySoh", deviceInfo.getBatterySoh());
		// 查询当前用户和激活用户信息
		List<Long> userIds = new ArrayList<>();
		Long currentUserId = deviceInfo.getCreateUser();
		Long buyUseId = queryDeviceExitFactoryInfo(deviceInfo, jsonObject);
		if (currentUserId != null) {
			userIds.add(currentUserId);
		}
		if (buyUseId != null) {
			userIds.add(buyUseId);
		}
		Map<Long, JSONObject> userJsonMap = userSearchClient.userAndReginInfo(userIds).getData();
		if (userJsonMap.containsKey(currentUserId)) {
			JSONObject userJsonObject = userJsonMap.get(currentUserId);
			jsonObject.put("userName", userJsonObject.getString("userName"));
			jsonObject.put("userPhone", userJsonObject.getString("userPhone"));
			jsonObject.put("countryName", userJsonObject.getString("countryName"));
			jsonObject.put("cityName", userJsonObject.getString("cityName"));
		}
		if (userJsonMap.containsKey(buyUseId)) {
			JSONObject userJsonObject = userJsonMap.get(buyUseId);
			jsonObject.put("activationUserName", userJsonObject.getString("userName"));
			jsonObject.put("activationUserPhone", userJsonObject.getString("userPhone"));
			jsonObject.put("activationUserCountryName", userJsonObject.getString("countryName"));
			jsonObject.put("activationUserCityName", userJsonObject.getString("cityName"));
		}
		return jsonObject;
	}


	/**
	 * 查设备出场信息
	 *
	 * @param deviceInfo 设备信息
	 * @param jsonObject 入参
	 * <AUTHOR>
	 * @since 2024/5/28 16:50
	 **/
	private Long queryDeviceExitFactoryInfo(DeviceInfoEntity deviceInfo, JSONObject jsonObject) {
		// 查询设备信息
		DeviceExitFactoryInfoEntity deviceExitFactoryInfoEntity = deviceExitFactoryInfoService.getOne(Wrappers.<DeviceExitFactoryInfoEntity>lambdaQuery().eq(DeviceExitFactoryInfoEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED).eq(DeviceExitFactoryInfoEntity::getDeviceSerialNumber, deviceInfo.getPowerSupplySn()));
		// 设置公司和设备类型名称
		queryDictInfo(jsonObject, deviceExitFactoryInfoEntity);
		jsonObject.put("bluetoothProtocolVersion", deviceExitFactoryInfoEntity.getBluetoothProtocolVersion());
		jsonObject.put("motherboardHardwareVersion", deviceExitFactoryInfoEntity.getMotherboardHardwareVersion());
		jsonObject.put("batteryRatedCapacity", deviceExitFactoryInfoEntity.getBatteryRatedCapacity());
		jsonObject.put("batteryRatedAcDischargeCapacity", deviceExitFactoryInfoEntity.getBatteryRatedAcDischargeCapacity());
		jsonObject.put("batteryRatedDischargeCapacity", deviceExitFactoryInfoEntity.getBatteryRatedDischargeCapacity());
		jsonObject.put("batteryRatedDcDischargeCapacity", deviceExitFactoryInfoEntity.getBatteryRatedDcDischargeCapacity());
		jsonObject.put("batteryRatedChargeCapacity", deviceExitFactoryInfoEntity.getBatteryRatedChargeCapacity());
		jsonObject.put("batteryRatedAndersonChargeCapacity", deviceExitFactoryInfoEntity.getBatteryRatedAndersonChargeCapacity());
		return deviceExitFactoryInfoEntity.getBuyerUserId();
	}

	/**
	 * 设置公司和设备类型名称
	 *
	 * @param jsonObject                  json
	 * @param deviceExitFactoryInfoEntity 入参
	 * <AUTHOR>
	 * @since 2024/5/28 16:51
	 **/
	private void queryDictInfo(JSONObject jsonObject, DeviceExitFactoryInfoEntity deviceExitFactoryInfoEntity) {
		// 数据字典
		Map<String, List<DictBiz>> mapDict = dictBizClient.batchGetList(Lists.newArrayList(DictBizCodeEnum.PORTABLE_DEVICE_TYPE.getCode(),
			DictBizCodeEnum.PORTABLE_COMPANY.getCode())).getData();
		String language = CommonUtil.getCurrentLanguage();
		String deviceTypeCode = deviceExitFactoryInfoEntity.getDeviceType(), companyName = deviceExitFactoryInfoEntity.getCompany();
		List<DictBiz> deviceTypeList = mapDict.get(DictBizCodeEnum.PORTABLE_DEVICE_TYPE.getCode());
		List<DictBiz> companyList = mapDict.get(DictBizCodeEnum.PORTABLE_COMPANY.getCode());
		deviceTypeList.forEach(a -> {
			if (language.equalsIgnoreCase(a.getLanguage()) && a.getDictKey().equals(deviceTypeCode)) {
				jsonObject.put("deviceType", a.getDictValue());
			}
		});
		companyList.forEach(a -> {
			if (language.equalsIgnoreCase(a.getLanguage()) && a.getDictKey().equals(companyName)) {
				jsonObject.put("company", a.getDictValue());
			}
		});
	}

	/**
	 * 绑定蓝牙
	 *
	 * @param deviceInfoEntity 入参
	 * @return Long
	 * <AUTHOR>
	 * @since 2024/1/25 14:31
	 **/
	@Override
	public Long saveDevice(DeviceInfoEntity deviceInfoEntity) {
		String powerSupplySn = deviceInfoEntity.getPowerSupplySn();
		if (StringUtil.isBlank(powerSupplySn)) {
			throw new BusinessException("portable.device.exit.factory.deviceSerialNumber.notNull");
		}
		// 查询该SN是否在设备出厂信息存在
		DeviceExitFactoryInfoEntity deviceExitFactoryInfoEntity = deviceExitFactoryInfoService.getOne(Wrappers.<DeviceExitFactoryInfoEntity>lambdaQuery().eq(DeviceExitFactoryInfoEntity::getDeviceSerialNumber, powerSupplySn).eq(DeviceExitFactoryInfoEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED));
		if (deviceExitFactoryInfoEntity == null) {
			throw new BusinessException("portable.device.exit.factory.not.exists");
		}
		Long deviceId = 0L;
		// 查询当前用户是否已经存在有该设备
		// 有设备：1.如果存在该设备有效，则提示已绑定  2.如果存在该设备无效，则直接把设备改为有效
		// 无设备：则把存在该设备的用户踢下线，数据删除，mqtt通知手机端刷新列表，然后新增插入数据
		BladeUser bladeUser = AuthUtil.getUser();
		Long userId = bladeUser.getUserId();
		// 设置型号
		deviceInfoEntity.setEquipmentModel(deviceExitFactoryInfoEntity.getDeviceType());
		deviceInfoEntity.setCreateUser(userId);
		deviceInfoEntity.setUpdateUser(userId);
		List<DeviceInfoEntity> deviceInfoEntityList = baseMapper.selectDeviceByUserAndSn(userId, powerSupplySn);
		// 当前用户下面蓝牙的SN存在1条以上的记录
		if (deviceInfoEntityList.size() > BizConstant.NUMBER_ONE) {
			throw new BusinessException("portable.device.exist.multiple.records", powerSupplySn);
		}
		// 当前用户下面扫码的SN存在1条记录，需要判断该记录是否删除，如果删除，则改为未删除;如果没有删除，则提示已存在;同时判断是否被其他用户绑定过，绑定过需要把其他设备人员踢下线；
		else if (deviceInfoEntityList.size() == BizConstant.NUMBER_ONE) {
			deviceId = getDeviceId(deviceInfoEntityList, powerSupplySn, userId);
		}
		// 当前用户下不存在在sn，但sn被其他用户绑定过，重试绑定蓝牙的时候，需要把其他用户该设备下线
		else {
			// 远程获取用户国家/省份/城市信息
			Map<Long, UserApp> userAppMap = userSearchClient.userAppInfoList(Collections.singletonList(userId)).getData();
			if(null != userAppMap && null != userAppMap.get(userId)){
				UserApp userApp = userAppMap.get(userId);
				deviceInfoEntity.setCountryCode(userApp.getCountryCode());
				deviceInfoEntity.setProvinceCode(userApp.getProvinceCode());
				deviceInfoEntity.setCityCode(userApp.getCityCode());
				deviceInfoEntity.setCountyCode(userApp.getCountyCode());
			}
			deviceId = getDeviceId(deviceInfoEntity, powerSupplySn, userId);
		}
		// 初始化激活日期
		initActivationDate(deviceExitFactoryInfoEntity);
		return deviceId;
	}

	/**
	 * app端查询设备列表
	 *
	 * @param page       分页参数
	 * @param deviceInfo 条件入参
	 * @return IPage<DeviceInfoVO>
	 * <AUTHOR>
	 * @since 2024/1/25 14:20
	 **/
	@Override
	public IPage<DeviceInfoVO> appQueryPage(IPage<DeviceInfoVO> page, Map<String, Object> deviceInfo) {
		deviceInfo.put("userId", AuthUtil.getUserId());
		return page.setRecords(baseMapper.appQueryPage(page, deviceInfo));
	}


	/**
	 * 初始化激活日期
	 *
	 * @param deviceExitFactoryInfoEntity 入参
	 * <AUTHOR>
	 * @since 2024/1/25 11:05
	 **/
	private void initActivationDate(DeviceExitFactoryInfoEntity deviceExitFactoryInfoEntity) {
		// 激活日期
		LocalDate activationDate = deviceExitFactoryInfoEntity.getActivationDate();
		if (null == activationDate) {
			deviceExitFactoryInfoEntity.setActivationDate(LocalDate.now());
			deviceExitFactoryInfoEntity.setStatus(BizConstant.NUMBER_ONE);
			deviceExitFactoryInfoService.updateById(deviceExitFactoryInfoEntity);
		}
	}

	/**
	 * 当前用户下面扫码的SN存在1条记录，需要判断该记录是否删除，如果删除，则改为未删除，如果没有删除，则提示已存在
	 *
	 * @param deviceInfoEntityList 其他用户
	 * @param powerSupplySn        入参
	 * @return Long
	 * <AUTHOR>
	 * @since 2024/1/25 11:05
	 **/
	private Long getDeviceId(List<DeviceInfoEntity> deviceInfoEntityList, String powerSupplySn, Long userId) {
		DeviceInfoEntity deviceInfo = deviceInfoEntityList.get(0);
		Integer isDelete = deviceInfo.getIsDeleted();
		// 设备已被该用户关联的情况
		if (BizConstant.NUMBER_ZERO.equals(isDelete)) {
			throw new BusinessException("portable.device.not.duplicate.association", powerSupplySn);
		}
		// 当前用户下已删除记录，需要重新激活
		else if (BizConstant.NUMBER_ONE.equals(isDelete)) {
			baseMapper.stateRestoration(Collections.singletonList(deviceInfo.getId()));
		}
		// 下线该SN下的其他用户设备
		offLineOtherUserDevice(powerSupplySn, userId);
		return deviceInfo.getId();
	}

	/**
	 * 当前用户下不存在在sn，但sn被其他用户绑定过，重试绑定蓝牙的时候，需要把其他用户该设备下线
	 *
	 * @param deviceInfoEntity 入参
	 * @param powerSupplySn    入参
	 * @return Long
	 * <AUTHOR>
	 * @since 2024/1/25 11:06
	 **/
	private Long getDeviceId(DeviceInfoEntity deviceInfoEntity, String powerSupplySn, Long userId) {
		// 下线该SN下的其他用户设备
		offLineOtherUserDevice(powerSupplySn, userId);
		// 插入新设备信息
		baseMapper.insert(deviceInfoEntity);
		return deviceInfoEntity.getId();
	}

	/**
	 * 下线该SN下的其他用户设备
	 *
	 * @param powerSupplySn 入参
	 * <AUTHOR>
	 * @since 2024/4/15 15:05
	 **/
	private void offLineOtherUserDevice(String powerSupplySn, Long userId) {
		// 把其他用户下面同SN的设备信息删除
		List<DeviceInfoEntity> powerSupplySnList = baseMapper.selectList(Wrappers.<DeviceInfoEntity>lambdaQuery().eq(DeviceInfoEntity::getPowerSupplySn, powerSupplySn).eq(DeviceInfoEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED).ne(DeviceInfoEntity::getCreateUser, userId));
		if (CollectionUtils.isNotEmpty(powerSupplySnList)) {
			List<Long> ids = powerSupplySnList.stream().map(DeviceInfoEntity::getId).collect(Collectors.toList());
			super.update(Wrappers.<DeviceInfoEntity>lambdaUpdate().set(DeviceInfoEntity::getIsDeleted, BladeConstant.DB_IS_DELETED).in(DeviceInfoEntity::getId, ids));
			// mqtt通知手机端刷新菜单，并下线设备
			List<JSONObject> jsonObjectList = new ArrayList<>();
			powerSupplySnList.forEach(a -> {
				JSONObject jsonObject = new JSONObject();
				jsonObject.put("userId", a.getCreateUser());
				jsonObject.put("deviceSn", a.getPowerSupplySn());
				jsonObject.put("content", "The device is bound by another user and will be immediately taken offline");
				jsonObjectList.add(jsonObject);
			});
			distributeUpgradeClient.portableDeviceOffline(jsonObjectList);
		}
	}

	/**
	 * 修改人员行政区划信息，同步刷新设备表行政区划信息
	 *
	 * @param jsonObject 入参
	 * <AUTHOR>
	 * @since 2024/1/26 10:25
	 **/
	@Override
	public void updateDeviceDistrict(JSONObject jsonObject) {
		baseMapper.updateDeviceDistrict(jsonObject);
	}
}
