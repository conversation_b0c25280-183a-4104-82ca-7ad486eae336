package org.skyworth.ess.report.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.jetbrains.annotations.NotNull;
import org.skyworth.ess.constant.DictBizCodeEnum;
import org.skyworth.ess.exceptionlog.service.IExceptionLogService;
import org.skyworth.ess.exitfactoryquality.service.IDeviceExitFactoryInfoService;
import org.skyworth.ess.report.excel.DeviceReportHeadEnum;
import org.skyworth.ess.report.excel.UserReportHeadEnum;
import org.skyworth.ess.report.service.IDataStatisticsService;
import org.skyworth.ess.report.vo.BaseColumn;
import org.skyworth.ess.report.vo.DeviceActivationAndAlarmExcel;
import org.skyworth.ess.report.vo.UserRegistrationExcel;
import org.springblade.common.constant.BizConstant;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.utils.CollectionUtils;
import org.springblade.common.utils.CommonUtil;
import org.springblade.common.utils.CustomCellWriteWidthConfig;
import org.springblade.common.utils.CustomMergeStrategy;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.system.entity.DictBiz;
import org.springblade.system.entity.Region;
import org.springblade.system.feign.IDictBizClient;
import org.springblade.system.feign.ISysClient;
import org.springblade.system.feign.IUserSearchClient;
import org.springblade.system.vo.UserRegistrationVO;
import org.springblade.system.vo.UserStatisticsVO;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 数据报表
 *
 * @description:
 * @author: SDT50545
 * @since: 2024-01-26 11:11
 **/
@Service
@Slf4j
@AllArgsConstructor
public class DataStatisticsServiceImpl implements IDataStatisticsService {
	private final IDeviceExitFactoryInfoService deviceExitFactoryInfoService;
	private final IExceptionLogService exceptionLogService;
	private final IUserSearchClient userSearchClient;
	private final ISysClient sysClient;
	private final ThreadPoolExecutor commonThreadPool;
	private final IDictBizClient dictBizClient;

	@Override
	public JSONObject deviceReport(JSONObject jsonObject) {
		JSONObject jsonResult = new JSONObject();
		// 初始化坐标
		String beginDateStr = jsonObject.getString("beginDate");
		String endDateStr = jsonObject.getString("endDate");
		if (StringUtil.isAnyBlank(beginDateStr, endDateStr)) {
			throw new BusinessException("parameter.notEmpty");
		}
		// 设备激活数统计
		deviceActivationCountStatistics(jsonObject, beginDateStr, endDateStr, jsonResult);
		// 设备异常数统计
		deviceHeteroConstantStatistics(jsonObject, beginDateStr, endDateStr, jsonResult);
		return jsonResult;
	}


	/**
	 * 用户注册数量报表
	 *
	 * @param jsonObject 入参
	 * @return JSONObject
	 * <AUTHOR>
	 * @since 2024/1/27 14:06
	 **/
	@Override
	public JSONObject userReport(JSONObject jsonObject) {
		// 初始化坐标
		String beginDateStr = jsonObject.getString("beginDate");
		String endDateStr = jsonObject.getString("endDate");
		if (StringUtil.isAnyBlank(beginDateStr, endDateStr)) {
			throw new BusinessException("parameter.notEmpty");
		}
		jsonObject.put("tenantId", CommonConstant.PORTABLE_TENANT_ID);
		JSONObject jsonResult = new JSONObject();
		LinkedHashMap<String, Long> initializedDataMap = initNullDate(beginDateStr, endDateStr);
		Map<String, Object> map = jsonObject.toJavaObject(Map.class);
		List<UserStatisticsVO> userStatisticsVOList = userSearchClient.singleDayRegisterInfo(map).getData();
		// 单日设备激活数为空，则初始化数据
		if (CollectionUtils.isNullOrEmpty(userStatisticsVOList)) {
			jsonResult.put("totalNumberOfUserRegistrations", BizConstant.NUMBER_ZERO);
			jsonResult.put("maximumNumberOfRegistrationsPerDay", BizConstant.NUMBER_ZERO);
		} else {
			userStatisticsVOList.forEach(a -> {
				String activationDate = a.getRegistrationDate();
				Long quantity = a.getQuantity();
				if (initializedDataMap.containsKey(activationDate)) {
					initializedDataMap.put(activationDate, quantity);
				}
			});
			Long maximumNumberOfRegistrationsPerDay = userStatisticsVOList.stream().map(UserStatisticsVO::getQuantity).max(Long::compareTo).orElse(BizConstant.NUMBER_ZERO.longValue());
			jsonResult.put("maximumNumberOfRegistrationsPerDay", maximumNumberOfRegistrationsPerDay);
			Long totalNumberOfUserRegistrations = userStatisticsVOList.stream().mapToLong(UserStatisticsVO::getQuantity).sum();
			jsonResult.put("totalNumberOfUserRegistrations", totalNumberOfUserRegistrations);
		}
		jsonResult.put("dailyRegisteredQuantity", mapTransferJsonArray(initializedDataMap));
		return jsonResult;
	}

	/**
	 * 导出设备激活报表
	 *
	 * @param map      入参
	 * @param response 入参
	 * <AUTHOR>
	 * @since 2024/2/1 9:20
	 **/
	@Override
	public void exportDeviceReport(Map<String, Object> map, HttpServletResponse response) throws Exception {
		JSONObject jsonObject = new JSONObject(map);
		// 初始化坐标
		String beginDateStr = jsonObject.getString("beginDate");
		String endDateStr = jsonObject.getString("endDate");
		if (StringUtil.isAnyBlank(beginDateStr, endDateStr)) {
			throw new BusinessException("parameter.notEmpty");
		}
		// 获取报表数据
		List<DeviceActivationAndAlarmExcel> totalDataList = getReportData(jsonObject);
		// 获取区域数据
		if (!CollectionUtils.isNullOrEmpty(totalDataList)) {
			totalDataList = fillInAreaInformation(totalDataList);
		}
		// excel头信息
		List<List<String>> baseHeaderList = this.getHeadColumn(DeviceReportHeadEnum::getColumn);
		int[] mergeColumnIndex = {0, 1, 2, 3};
		//设置第几行开始合并
		int mergeRowIndex = 1;
		export(response, mergeRowIndex, mergeColumnIndex, baseHeaderList, totalDataList, "Equipment_Information_Statistics");
	}

	/**
	 * 用户注册信息导出
	 *
	 * @param map      参数
	 * @param response 入参
	 * <AUTHOR>
	 * @since 2024/2/2 10:16
	 **/
	@Override
	public void exportUserReport(Map<String, Object> map, HttpServletResponse response) throws IOException {
		JSONObject jsonObject = new JSONObject(map);
		// 初始化坐标
		String beginDateStr = jsonObject.getString("beginDate");
		String endDateStr = jsonObject.getString("endDate");
		if (StringUtil.isAnyBlank(beginDateStr, endDateStr)) {
			throw new BusinessException("parameter.notEmpty");
		}
		jsonObject.put("tenantId", CommonConstant.PORTABLE_TENANT_ID);
		List<UserRegistrationExcel> totalDataList = new ArrayList<>();
		List<UserRegistrationVO> userRegistrationVOList = userSearchClient.singleDayRegisterExcelInfo(jsonObject).getData();
		if (!CollectionUtils.isNullOrEmpty(userRegistrationVOList)) {
			totalDataList = getUserRegistrationExcels(userRegistrationVOList);
		}
		// excel头信息
		List<List<String>> baseHeaderList = this.getHeadColumn(UserReportHeadEnum::getColumn);
		int[] mergeColumnIndex = {0, 1, 2};
		//设置第几行开始合并
		int mergeRowIndex = 1;
		export(response, mergeRowIndex, mergeColumnIndex, baseHeaderList, totalDataList, "App_User_Registration_Statistics");
	}

	/**
	 * 累加设备激活数
	 *
	 * @param jsonObject 入参
	 * @return JSONArray
	 * <AUTHOR>
	 * @since 2024/5/27 9:06
	 **/
	@Override
	public JSONArray accumulatedActivationsReport(JSONObject jsonObject) {
		return queryAccumulatedSummaryReport(jsonObject, deviceExitFactoryInfoService::queryAccumulatedActivations, deviceExitFactoryInfoService::queryDeviceActivationsInfo);
	}

	/**
	 * 累计异常数
	 *
	 * @param jsonObject 入参
	 * @return JSONArray
	 * <AUTHOR>
	 * @since 2024/5/27 9:06
	 **/
	@Override
	public JSONArray accumulatedAlarmReport(JSONObject jsonObject) {
		return queryAccumulatedSummaryReport(jsonObject, exceptionLogService::queryAccumulateAbnormal, exceptionLogService::queryNumberOfDailyAnomalies);
	}

	@Override
	public JSONArray accumulatedRegistrationReport(JSONObject jsonObject) {
		// 初始化坐标
		String beginDateStr = jsonObject.getString("beginDate");
		String endDateStr = jsonObject.getString("endDate");
		if (StringUtil.isAnyBlank(beginDateStr, endDateStr)) {
			throw new BusinessException("parameter.notEmpty");
		}
		jsonObject.put("tenantId", CommonConstant.PORTABLE_TENANT_ID);
		LinkedHashMap<String, Long> initializedDataMap = initNullDate(beginDateStr, endDateStr);
		Map<String, Object> map = jsonObject.toJavaObject(Map.class);
		List<UserStatisticsVO> userStatisticsVOList = userSearchClient.singleDayRegisterInfo(map).getData();
		Long sum = userSearchClient.accumulatedRegistrationData(map).getData();
		// 单日设备激活数为空，则初始化数据
		if (CollectionUtils.isNullOrEmpty(userStatisticsVOList)) {
			// 设置为累加值
			Long finalSum = sum;
			initializedDataMap.forEach((key, value) -> {
				initializedDataMap.put(key, finalSum);
			});
		} else {
			Map<String, Long> deviceActivationsMap = userStatisticsVOList.stream().collect(Collectors.toMap(UserStatisticsVO::getRegistrationDate, UserStatisticsVO::getQuantity, (a, b) -> a));
			String key = "";
			for (Map.Entry<String, Long> entry : initializedDataMap.entrySet()) {
				key = entry.getKey();
				if (deviceActivationsMap.containsKey(key)) {
					sum += deviceActivationsMap.get(key);
				}
				initializedDataMap.put(key, sum);
			}
		}
		return mapTransferJsonArray(initializedDataMap);
	}

	/**
	 * 统计报表
	 *
	 * @param jsonObject            入参
	 * @param querySumFunction      查询历史总和(不包括查询时间范围内的数据)
	 * @param queryDateDataFunction 查询当天总和
	 * @return JSONArray
	 * <AUTHOR>
	 * @since 2024/5/24 17:16
	 **/
	private JSONArray queryAccumulatedSummaryReport(JSONObject jsonObject, Function<JSONObject, Long> querySumFunction, Function<JSONObject, List<JSONObject>> queryDateDataFunction) {
		// 初始化坐标
		String beginDateStr = jsonObject.getString("beginDate");
		String endDateStr = jsonObject.getString("endDate");
		if (StringUtil.isAnyBlank(beginDateStr, endDateStr)) {
			throw new BusinessException("parameter.notEmpty");
		}
		// 计算开始日期之前的总数量
		Long sum = querySumFunction.apply(jsonObject);
		LinkedHashMap<String, Long> initializedDataMap = initNullDate(beginDateStr, endDateStr);
		// 查询查询时间范围内每日的汇总数据
		List<JSONObject> dailyDataList = queryDateDataFunction.apply(jsonObject);
		// 单日设备激活数为空，则初始化数据
		if (CollectionUtils.isNullOrEmpty(dailyDataList)) {
			// 设置为累加值
			Long finalSum = sum;
			initializedDataMap.forEach((key, value) -> {
				initializedDataMap.put(key, finalSum);
			});
		} else {
			Map<String, Long> deviceActivationsMap = dailyDataList.stream().collect(Collectors.toMap(a -> a.getString("statisticalDate"), a -> a.getLongValue("quantity"), (a, b) -> a));
			String key;
			for (Map.Entry<String, Long> entry : initializedDataMap.entrySet()) {
				key = entry.getKey();
				if (deviceActivationsMap.containsKey(key)) {
					sum += deviceActivationsMap.get(key);
					initializedDataMap.put(key, sum);
				}
				initializedDataMap.put(key, sum);
			}
		}
		return mapTransferJsonArray(initializedDataMap);
	}


	/**
	 * 设备激活数统计
	 *
	 * @param jsonObject   查询条件
	 * @param beginDateStr 查询条件开始时间
	 * @param endDateStr   查询条件结束时间
	 * @param jsonResult   入参
	 * <AUTHOR>
	 * @since 2024/1/26 15:23
	 **/
	private void deviceActivationCountStatistics(JSONObject jsonObject, String beginDateStr, String endDateStr, JSONObject jsonResult) {
		LinkedHashMap<String, Long> initializedDataMap = initNullDate(beginDateStr, endDateStr);
		List<JSONObject> deviceActivationsList = deviceExitFactoryInfoService.queryDeviceActivationsInfo(jsonObject);
		// 单日设备激活数为空，则初始化数据
		if (CollectionUtils.isNullOrEmpty(deviceActivationsList)) {
			jsonResult.put("totalNumberOfActivations", BizConstant.NUMBER_ZERO);
			jsonResult.put("maximumDailyActivations", BizConstant.NUMBER_ZERO);
		} else {
			resetMapValue(initializedDataMap, deviceActivationsList);
			Long maximumDailyActivations = deviceActivationsList.stream().map(a -> a.getLongValue("quantity")).max(Long::compareTo).orElse(BizConstant.NUMBER_ZERO.longValue());
			jsonResult.put("maximumDailyActivations", maximumDailyActivations);
			Long totalNumberOfActivations = deviceActivationsList.stream().mapToLong(a -> a.getLongValue("quantity")).sum();
			jsonResult.put("totalNumberOfActivations", totalNumberOfActivations);
		}
		jsonResult.put("dailyActivationQuantity", mapTransferJsonArray(initializedDataMap));
	}

	/**
	 * 设备异常数统计
	 *
	 * @param jsonObject   查询条件
	 * @param beginDateStr 查询条件开始时间
	 * @param endDateStr   查询条件结束时间
	 * @param jsonResult   入参
	 * <AUTHOR>
	 * @since 2024/1/26 15:23
	 **/
	private void deviceHeteroConstantStatistics(JSONObject jsonObject, String beginDateStr, String endDateStr, JSONObject jsonResult) {
		LinkedHashMap<String, Long> initializedDataMap = initNullDate(beginDateStr, endDateStr);
		List<JSONObject> deviceDailyAnomaliesList = exceptionLogService.queryNumberOfDailyAnomalies(jsonObject);
		// 单日设备激活数为空，则初始化数据
		if (CollectionUtils.isNullOrEmpty(deviceDailyAnomaliesList)) {
			jsonResult.put("totalNumberQfDeviceAlarms", BizConstant.NUMBER_ZERO);
			jsonResult.put("maximumNumberOfAlarmsPerDay", BizConstant.NUMBER_ZERO);
		} else {
			resetMapValue(initializedDataMap, deviceDailyAnomaliesList);
			Long maximumNumberOfAlarmsPerDay = deviceDailyAnomaliesList.stream().map(a -> a.getLongValue("quantity")).max(Long::compareTo).orElse(BizConstant.NUMBER_ZERO.longValue());
			jsonResult.put("maximumNumberOfAlarmsPerDay", maximumNumberOfAlarmsPerDay);
			Long totalNumberQfDeviceAlarms = deviceDailyAnomaliesList.stream().mapToLong(a -> a.getLongValue("quantity")).sum();
			jsonResult.put("totalNumberQfDeviceAlarms", totalNumberQfDeviceAlarms);
		}
		jsonResult.put("dailyAlarmsQuantity", mapTransferJsonArray(initializedDataMap));
	}

	private void resetMapValue(LinkedHashMap<String, Long> initializedDataMap, List<JSONObject> deviceDailyAnomaliesList) {
		deviceDailyAnomaliesList.forEach(a -> {
			String activationDate = a.getString("statisticalDate");
			Long quantity = a.getLongValue("quantity");
			if (initializedDataMap.containsKey(activationDate)) {
				initializedDataMap.put(activationDate, quantity);
			}
		});
	}

	/**
	 * Map转JSON
	 *
	 * @param map 入参
	 * @return JSONArray
	 * <AUTHOR>
	 * @since 2024/1/27 15:31
	 **/
	private JSONArray mapTransferJsonArray(LinkedHashMap<String, Long> map) {
		JSONArray jsonArray = new JSONArray();
		for (Map.Entry<String, Long> entry : map.entrySet()) {
			JSONObject obj = new JSONObject();
			obj.put("abscissa", entry.getKey());
			obj.put("ordinate", entry.getValue());
			jsonArray.add(obj);
		}
		return jsonArray;
	}

	/**
	 * 初始化空数据
	 *
	 * @param beginDateStr 开始日期
	 * @param endDateStr   结束日期
	 * @return Map<Date, Long>
	 * <AUTHOR>
	 * @since 2024/1/26 16:29
	 **/
	@NotNull
	private LinkedHashMap<String, Long> initNullDate(String beginDateStr, String endDateStr) {
		LinkedHashMap<String, Long> deviceActivationsMap = new LinkedHashMap<>();
		Date nextDay = DateUtil.parse(beginDateStr, DateUtil.PATTERN_DATE);
		Date endDate = DateUtil.parse(endDateStr, DateUtil.PATTERN_DATE);
		while (nextDay.compareTo(endDate) < BizConstant.NUMBER_ONE) {
			deviceActivationsMap.put(DateUtil.format(nextDay, DateUtil.PATTERN_DATE), BizConstant.NUMBER_ZERO.longValue());
			nextDay = DateUtil.plusDays(nextDay, BizConstant.NUMBER_ONE);
		}
		return deviceActivationsMap;
	}

	/**
	 * 合并行导出
	 *
	 * @param response         响应
	 * @param mergeRowIndex    合并行开始
	 * @param mergeColumnIndex 合并具体列
	 * @param baseHeaderList   头信息
	 * @param totalDataList    入参
	 * <AUTHOR>
	 * @since 2024/2/1 19:07
	 **/
	private <T> void export(HttpServletResponse response, int mergeRowIndex, int[] mergeColumnIndex, List<List<String>> baseHeaderList, List<T> totalDataList, String fileName) throws IOException {
		try {
			response.setContentType("application/vnd.ms-excel");
			response.setCharacterEncoding(StandardCharsets.UTF_8.name());
			String fileNameEncode = URLEncoder.encode(fileName, StandardCharsets.UTF_8);
			response.setHeader("Content-disposition", "attachment;filename=" + fileNameEncode + System.currentTimeMillis() + ".xlsx");
			((ExcelWriterBuilder) EasyExcel.write(response.getOutputStream(), DeviceActivationAndAlarmExcel.class)).sheet("sheet1").registerWriteHandler(new CustomMergeStrategy(mergeRowIndex, mergeColumnIndex))
				.registerWriteHandler(style())
				.registerWriteHandler(new CustomCellWriteWidthConfig())
				.head(baseHeaderList)
				.doWrite(totalDataList);
		} catch (Throwable var7) {
			log.error("exportDeviceReport error,====", var7);
			throw var7;
		}
	}

	/**
	 * 获取行政区域
	 *
	 * @param totalDataList 入参
	 * @return List<DeviceActivationAndAlarmExcel>
	 * <AUTHOR>
	 * @since 2024/2/1 16:40
	 **/
	@NotNull
	private List<DeviceActivationAndAlarmExcel> fillInAreaInformation(List<DeviceActivationAndAlarmExcel> totalDataList) {
		List<String> regionCodeList = new ArrayList<>();
		// 获取字典
		String language = CommonUtil.getCurrentLanguage();
		List<DictBiz> dictBizList = dictBizClient.getListByLang(DictBizCodeEnum.PORTABLE_DEVICE_TYPE.getCode(), language).getData();
		Map<String, String> dictMap = Optional.ofNullable(dictBizList).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(DictBiz::getDictKey, DictBiz::getDictValue, (a, b) -> a));
		totalDataList.forEach(a -> {
			String groupUniqueKey = a.getGroupUniqueKey();
			if(StringUtil.isNotBlank(groupUniqueKey)){
				String[] params = groupUniqueKey.split("@");
				String equipmentModel = params[0], countryCode = params[1], provinceCode = params[2], cityCode = params[3];
				if (dictMap.containsKey(equipmentModel)) {
					a.setEquipmentModel(dictMap.get(equipmentModel));
				}
				a.setCountryName(countryCode);
				a.setProvinceName(provinceCode);
				a.setCityName(cityCode);
				a.setStatisticalDate(params[4]);
				if (StringUtil.isNotBlank(countryCode)) {
					regionCodeList.add(countryCode);
				}
				if (StringUtil.isNotBlank(provinceCode)) {
					regionCodeList.add(provinceCode);
				}
				if (StringUtil.isNotBlank(cityCode)) {
					regionCodeList.add(cityCode);
				}
			}
		});
		// 行政区划赋值
		obtainRegionalInformation(regionCodeList, totalDataList);
		// 排序
		totalDataList = totalDataList.stream().sorted(Comparator.comparing(DeviceActivationAndAlarmExcel::getGroupUniqueKey)).collect(Collectors.toList());
		return totalDataList;
	}

	/**
	 * 获取设备报表数据
	 *
	 * @param jsonObject 入参
	 * @return List<DeviceActivationAndAlarmExcel>
	 * <AUTHOR>
	 * @since 2024/2/1 16:39
	 **/
	@NotNull
	private List<DeviceActivationAndAlarmExcel> getReportData(JSONObject jsonObject) throws Exception {
		// 设备激活数
		Future<List<DeviceActivationAndAlarmExcel>> futureActivation = commonThreadPool.submit(() -> deviceExitFactoryInfoService.queryDeviceActivationsExcelInfo(jsonObject));
		// 设备告警数
		Future<List<DeviceActivationAndAlarmExcel>> futureAlarm = commonThreadPool.submit(() -> exceptionLogService.queryNumberOfDailyAnomaliesExcelInfo(jsonObject));
		List<DeviceActivationAndAlarmExcel> deviceActivationsList = futureActivation.get(BizConstant.NUMBER_TWO, TimeUnit.MINUTES);
		List<DeviceActivationAndAlarmExcel> deviceDailyAnomaliesList = futureAlarm.get(BizConstant.NUMBER_TWO, TimeUnit.MINUTES);
		List<DeviceActivationAndAlarmExcel> totalDataList = new ArrayList<>();
		// 单日设备激活数为空，则初始化数据
		if (!CollectionUtils.isNullOrEmpty(deviceActivationsList) && !CollectionUtils.isNullOrEmpty(deviceDailyAnomaliesList)) {
			List<DeviceActivationAndAlarmExcel> dataList = new ArrayList<>();
			Map<String, DeviceActivationAndAlarmExcel> deviceActivationsMap = deviceActivationsList.stream().collect(Collectors.toMap(DeviceActivationAndAlarmExcel::getGroupUniqueKey, Function.identity(), (a, b) -> a));
			Map<String, DeviceActivationAndAlarmExcel> deviceDailyAnomaliesMap = deviceDailyAnomaliesList.stream().collect(Collectors.toMap(DeviceActivationAndAlarmExcel::getGroupUniqueKey, Function.identity(), (a, b) -> a));
			// 封装设备激活数据
			deviceActivationsList.forEach(a -> {
				if (deviceDailyAnomaliesMap.containsKey(a.getGroupUniqueKey())) {
					a.setNumberOfFaults(deviceDailyAnomaliesMap.get(a.getGroupUniqueKey()).getNumberOfFaults());
				}
				dataList.add(a);
			});
			// 封装设备告警数据
			deviceDailyAnomaliesList.forEach(a -> {
				if (!deviceActivationsMap.containsKey(a.getGroupUniqueKey())) {
					dataList.add(a);
				}
			});
			totalDataList = dataList.stream().distinct().collect(Collectors.toList());
		} else if (CollectionUtils.isNullOrEmpty(deviceActivationsList) && !CollectionUtils.isNullOrEmpty(deviceDailyAnomaliesList)) {
			totalDataList = deviceDailyAnomaliesList;
		} else if (!CollectionUtils.isNullOrEmpty(deviceActivationsList) && CollectionUtils.isNullOrEmpty(deviceDailyAnomaliesList)) {
			totalDataList = deviceActivationsList;
		}
		return totalDataList;
	}

	/**
	 * 设置样式
	 *
	 * @return HorizontalCellStyleStrategy
	 * <AUTHOR>
	 * @since 2024/2/1 16:44
	 **/
	private HorizontalCellStyleStrategy style() {
		// 头的样式
		WriteCellStyle headStyle = new WriteCellStyle();
		headStyle.setFillForegroundColor(IndexedColors.GREY_40_PERCENT.getIndex());
		WriteFont headFont = new WriteFont();
		headFont.setBold(true);
		headFont.setFontHeightInPoints((short) 11);
		headStyle.setWriteFont(headFont);
		headStyle.setWrapped(true);
		// 内容的样式
		WriteCellStyle contentStyle = new WriteCellStyle();
		contentStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
		contentStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
		WriteFont contentWriteFont = new WriteFont();
		contentWriteFont.setFontHeightInPoints((short) 10);
		contentStyle.setWriteFont(contentWriteFont);
		contentStyle.setBorderTop(BorderStyle.THIN);
		contentStyle.setBorderBottom(BorderStyle.THIN);
		contentStyle.setBorderLeft(BorderStyle.THIN);
		contentStyle.setBorderRight(BorderStyle.THIN);
		contentStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
		contentStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		contentStyle.setWrapped(true);
		return new HorizontalCellStyleStrategy(headStyle, contentStyle);
	}


	/**
	 * 获取excel导出头信息
	 *
	 * @param function 入参
	 * @return List<List < String>>
	 * <AUTHOR>
	 * @since 2024/1/31 16:22
	 **/
	private List<List<String>> getHeadColumn(Function<String, Set<String>> function) {
		List<List<String>> headerList = new ArrayList<>();
		// 获取当前
		Set<String> columnSet = function.apply(CommonUtil.getCurrentLanguage());
		columnSet.forEach(a -> {
			headerList.add(Collections.singletonList(a));
		});
		return headerList;
	}

	/**
	 * 封装区域信息
	 *
	 * @param userRegistrationVOList 入参
	 * @return List<UserRegistrationExcel>
	 * <AUTHOR>
	 * @since 2024/2/2 10:27
	 **/
	@NotNull
	private List<UserRegistrationExcel> getUserRegistrationExcels(List<UserRegistrationVO> userRegistrationVOList) {
		List<UserRegistrationExcel> totalDataList;
		List<String> regionCodeList = new ArrayList<>();
		List<UserRegistrationExcel> userRegistrationExcelList = new ArrayList<>();
		userRegistrationVOList.forEach(a -> {
			String groupUniqueKey = a.getGroupUniqueKey();
			if (StringUtil.isNotBlank(groupUniqueKey)) {
				UserRegistrationExcel userRegistrationExcel = new UserRegistrationExcel();
				String[] params = groupUniqueKey.split("@");
				String countryCode = params[0], provinceCode = params[1], cityCode = params[2];
				userRegistrationExcel.setCountryName(countryCode);
				userRegistrationExcel.setProvinceName(provinceCode);
				userRegistrationExcel.setCityName(cityCode);
				userRegistrationExcel.setStatisticalDate(params[3]);
				userRegistrationExcel.setNumberOfAppUserRegistrations(a.getNumberOfAppUserRegistrations());
				userRegistrationExcel.setGroupUniqueKey(a.getGroupUniqueKey());
				if (StringUtil.isNotBlank(countryCode)) {
					regionCodeList.add(countryCode);
				}
				if (StringUtil.isNotBlank(provinceCode)) {
					regionCodeList.add(provinceCode);
				}
				if (StringUtil.isNotBlank(cityCode)) {
					regionCodeList.add(cityCode);
				}
				userRegistrationExcelList.add(userRegistrationExcel);
			}
		});
		obtainRegionalInformation(regionCodeList, userRegistrationExcelList);
		// 排序
		totalDataList = userRegistrationExcelList.stream().sorted(Comparator.comparing(UserRegistrationExcel::getGroupUniqueKey)).collect(Collectors.toList());
		return totalDataList;
	}

	/**
	 * 调用远程接口获取行政区域信息
	 *
	 * @param regionCodeList            区域code集合
	 * @param userRegistrationExcelList 入参
	 * <AUTHOR>
	 * @since 2024/2/1 17:53
	 **/
	private void obtainRegionalInformation(List<String> regionCodeList, List<? extends BaseColumn> userRegistrationExcelList) {
		if (!CollectionUtils.isNullOrEmpty(regionCodeList)) {
			List<String> regionCodeUniqueList = regionCodeList.stream().distinct().collect(Collectors.toList());
			List<Region> regionList = sysClient.getRegionList(regionCodeUniqueList).getData();
			if (!CollectionUtils.isNullOrEmpty(regionList)) {
				Map<String, String> regionMap = regionList.stream().collect(Collectors.toMap(Region::getCode, Region::getName, (a, b) -> a));
				userRegistrationExcelList.forEach(a -> {
					if (StringUtil.isNotBlank(a.getCountryName())) {
						a.setCountryName(regionMap.get(a.getCountryName()));
					}
					if (StringUtil.isNotBlank(a.getProvinceName())) {
						a.setProvinceName(regionMap.get(a.getProvinceName()));
					}
					if (StringUtil.isNotBlank(a.getCityName())) {
						a.setCityName(regionMap.get(a.getCityName()));
					}
				});
			}
		}
	}
}
