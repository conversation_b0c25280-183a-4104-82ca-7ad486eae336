package org.skyworth.ess.exitfactoryquality.excel;

import lombok.Getter;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.tool.utils.StringUtil;

import java.util.LinkedHashSet;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Getter
public enum ExitFactorySheetEnum {
    base("Base Information","基本信息"),
    activation("Activation Information","激活信息"),
    buy("Sales Information","销售信息");

    private final String columnEn;
    private final String columnCn;
    ExitFactorySheetEnum(String columnEn, String columnCn) {
        this.columnEn = columnEn;
        this.columnCn = columnCn;
    }

    public static Set<String> getColumn(String language) {
        Set<String> result = new LinkedHashSet<>();
        for(ExitFactorySheetEnum item : ExitFactorySheetEnum.values()) {
            if(CommonConstant.CURRENT_LANGUAGE_ZH.equalsIgnoreCase(language)) {
                result.add(item.columnCn);
            } else {
                result.add(item.columnEn);
            }
        }
        return result;
    }

    public static String getColumnByCode(String language,ExitFactorySheetEnum code) {
        for(ExitFactorySheetEnum item : ExitFactorySheetEnum.values()) {
            if (item.equals(code)) {
                if(CommonConstant.CURRENT_LANGUAGE_ZH.equalsIgnoreCase(language)) {
                    return item.getColumnCn();
                } else {
                    return item.getColumnEn();
                }
            }

        }
        return "";
    }
}
