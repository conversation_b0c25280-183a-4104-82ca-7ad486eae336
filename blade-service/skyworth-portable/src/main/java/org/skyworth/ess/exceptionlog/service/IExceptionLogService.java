package org.skyworth.ess.exceptionlog.service;

import com.alibaba.fastjson.JSONObject;
import org.skyworth.ess.exceptionlog.entity.ExceptionLogEntity;
import org.skyworth.ess.report.vo.DeviceActivationAndAlarmExcel;
import org.springblade.core.mp.base.BaseService;

import java.util.List;

/**
 * 异常日志表 服务类
 *
 * <AUTHOR>
 * @since 2024-01-23
 */
public interface IExceptionLogService extends BaseService<ExceptionLogEntity> {
	/**
	 * 查询单日异常设备数量
	 *
	 * @param jsonObject 入参
	 * @return List<JSONObject>
	 * <AUTHOR>
	 * @since 2024/1/26 15:41
	 **/
	List<JSONObject> queryNumberOfDailyAnomalies(JSONObject jsonObject);
	/**
	 * 查询某个时间段之前的累计激活数
	 * @param jsonObject 入参
	 * @return Long
	 * <AUTHOR>
	 * @since 2024/5/24 17:31
	 **/
	Long queryAccumulateAbnormal(JSONObject jsonObject);

	/**
	 * 查询单日异常设备数量详情
	 *
	 * @param jsonObject 入参
	 * @return List<DeviceActivationAndAlarmExcel>
	 * <AUTHOR>
	 * @since 2024/2/1 13:57
	 **/
	List<DeviceActivationAndAlarmExcel> queryNumberOfDailyAnomaliesExcelInfo(JSONObject jsonObject);


}
