<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.exceptionlog.mapper.ExceptionLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="exceptionLogResultMap" type="org.skyworth.ess.exceptionlog.entity.ExceptionLogEntity">
        <result column="id" property="id"/>
        <result column="device_info_id" property="deviceInfoId"/>
        <result column="serial_number" property="serialNumber"/>
        <result column="device_date_time" property="deviceDateTime"/>
        <result column="exception_message" property="exceptionMessage"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <select id="queryNumberOfDailyAnomalies" resultType="com.alibaba.fastjson.JSONObject">
        select
        DATE_FORMAT(del.create_time, '%Y-%m-%d') as statisticalDate,
        count(1) as quantity
        from
        device_exception_log del
        left join device_info di on
        del.serial_number = di.power_supply_sn
        and del.device_info_id = di.id
        and di.is_deleted = 0
        where
        del.is_deleted = 0
        <if test="ew.deviceType != null and ew.deviceType!=''">
            and di.equipment_model = #{ew.deviceType}
        </if>
        <if test="ew.countryCode != null and ew.countryCode!=''">
            and di.country_code = #{ew.countryCode}
        </if>
        <if test="ew.provinceCode != null and ew.provinceCode!=''">
            and di.province_code = #{ew.provinceCode}
        </if>
        <if test="ew.cityCode != null and ew.cityCode!=''">
            and di.city_code = #{ew.cityCode}
        </if>
        <if test="ew.beginDate != null and ew.beginDate!=''">
            and del.create_time <![CDATA[ >= ]]> DATE_FORMAT(#{ew.beginDate}, '%Y-%m-%d 00:00:00')
        </if>
        <if test="ew.endDate != null and ew.endDate!=''">
            and del.create_time <![CDATA[ <= ]]> DATE_FORMAT(#{ew.endDate}, '%Y-%m-%d 23:59:59')
        </if>
        group by
        DATE_FORMAT(del.create_time, '%Y-%m-%d')
    </select>

    <select id="queryNumberOfDailyAnomaliesExcelInfo"
            resultType="org.skyworth.ess.report.vo.DeviceActivationAndAlarmExcel">
        select
        concat(ifnull(di.equipment_model,''),
        '@',ifnull(di.country_code,''),'@',ifnull(di.province_code,''),'@',ifnull(di.city_code,''),'@',DATE_FORMAT(del.create_time,
        '%Y-%m-%d')) as groupUniqueKey,
        count(1) as numberOfFaults
        from
        device_exception_log del
        left join device_info di on
        del.serial_number = di.power_supply_sn
        and del.device_info_id = di.id
        where
        del.is_deleted = 0
        <if test="ew.deviceType != null and ew.deviceType!=''">
            and di.equipment_model = #{ew.deviceType}
        </if>
        <if test="ew.countryCode != null and ew.countryCode!=''">
            and di.country_code = #{ew.countryCode}
        </if>
        <if test="ew.provinceCode != null and ew.provinceCode!=''">
            and di.province_code = #{ew.provinceCode}
        </if>
        <if test="ew.cityCode != null and ew.cityCode!=''">
            and di.city_code = #{ew.cityCode}
        </if>
        <if test="ew.beginDate != null and ew.beginDate!=''">
            and del.create_time <![CDATA[ >= ]]> DATE_FORMAT(#{ew.beginDate}, '%Y-%m-%d 00:00:00')
        </if>
        <if test="ew.endDate != null and ew.endDate!=''">
            and del.create_time <![CDATA[ <= ]]> DATE_FORMAT(#{ew.endDate}, '%Y-%m-%d 23:59:59')
        </if>
        group by groupUniqueKey
    </select>

    <select id="queryAccumulateAbnormal" resultType="java.lang.Long">
        select
        count(1)
        from
        device_exception_log del
        left join device_info di on
        del.serial_number = di.power_supply_sn
        and del.device_info_id = di.id
        where
        del.is_deleted = 0
        <if test="ew.deviceType != null and ew.deviceType!=''">
            and di.equipment_model = #{ew.deviceType}
        </if>
        <if test="ew.countryCode != null and ew.countryCode!=''">
            and di.country_code = #{ew.countryCode}
        </if>
        <if test="ew.provinceCode != null and ew.provinceCode!=''">
            and di.province_code = #{ew.provinceCode}
        </if>
        <if test="ew.cityCode != null and ew.cityCode!=''">
            and di.city_code = #{ew.cityCode}
        </if>
        <if test="ew.beginDate != null">
            and del.create_time <![CDATA[ < ]]> DATE_FORMAT(#{ew.beginDate}, '%Y-%m-%d 00:00:00')
        </if>
    </select>
</mapper>
