package org.skyworth.ess.report.excel;

import lombok.Getter;
import org.skyworth.ess.exitfactoryquality.excel.ExitFactorySheetEnum;
import org.springblade.common.constant.CommonConstant;

import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Getter
public enum UserReportHeadEnum {
	/**
	 * 国家
	 */
	countryName("Country", "国家"),
	/**
	 * 一级行政区域
	 */
	provinceName("First Level Administrative Region", "一级行政区域"),
	/**
	 * 二级行政区域
	 */
	cityName("Secondary Administrative Region", "二级行政区域"),
	/**
	 * 日期
	 */
	statisticalDate("Date", "日期"),
	/**
	 * App用户注册数
	 */
	numberOfUserRegistrations("App user registration count", "App用户注册数");
	private final String columnEn;
	private final String columnCn;

	UserReportHeadEnum(String columnEn, String columnCn) {
		this.columnEn = columnEn;
		this.columnCn = columnCn;
	}

	public static Set<String> getColumn(String language) {
		Set<String> result = new LinkedHashSet<>();
		for (UserReportHeadEnum item : UserReportHeadEnum.values()) {
			if (CommonConstant.CURRENT_LANGUAGE_ZH.equalsIgnoreCase(language)) {
				result.add(item.columnCn);
			} else {
				result.add(item.columnEn);
			}
		}
		return result;
	}
}
