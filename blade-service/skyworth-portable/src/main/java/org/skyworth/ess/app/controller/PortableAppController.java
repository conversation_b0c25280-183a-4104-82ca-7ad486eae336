package org.skyworth.ess.app.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.skyworth.ess.deviceinfo.entity.DeviceInfoEntity;
import org.skyworth.ess.deviceinfo.service.IDeviceInfoService;
import org.skyworth.ess.deviceinfo.vo.DeviceInfoVO;
import org.skyworth.ess.exceptionlog.entity.ExceptionLogEntity;
import org.skyworth.ess.exceptionlog.service.IExceptionLogService;
import org.skyworth.ess.exceptionlog.vo.ExceptionLogVO;
import org.skyworth.ess.exceptionlog.wrapper.ExceptionLogWrapper;
import org.skyworth.ess.exitfactoryquality.entity.DeviceExitFactoryInfoEntity;
import org.skyworth.ess.exitfactoryquality.service.IDeviceExitFactoryInfoService;
import org.skyworth.ess.exitfactoryquality.vo.DeviceExitFactoryInfoVO;
import org.springblade.common.valid.ValidGroups;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.StringUtil;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 便携式app对外接口
 *
 * <AUTHOR>
 * @since 2024-01-23
 */
@RestController
@AllArgsConstructor
@RequestMapping("app")
@Api(value = "设备信息表", tags = "设备信息表接口")
public class PortableAppController extends BladeController {
	private final IDeviceInfoService deviceInfoService;
	private final IExceptionLogService exceptionLogService;

	private final IDeviceExitFactoryInfoService deviceExitFactoryInfoService;

	/**
	 * 设备信息表 新增
	 */
	@PostMapping("/deviceInfo/save")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "新增", notes = "传入deviceInfo")
	public R<Long> save(@Valid @RequestBody DeviceInfoEntity deviceInfo) {
		return R.data(deviceInfoService.saveDevice(deviceInfo));
	}

	/**
	 * 设备信息表 修改
	 */
	@PostMapping("/deviceInfo/update")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "修改", notes = "传入deviceInfo")
	public R<Boolean> update(@Valid @RequestBody DeviceInfoEntity deviceInfo) {
		Long id = deviceInfo.getId();
		BigDecimal remainingBatteryPercentage = deviceInfo.getRemainingBatteryPercentage();
		Integer batterySoh = deviceInfo.getBatterySoh();
		Integer numberOfBatteryCycles = deviceInfo.getNumberOfBatteryCycles();
		if (id == null || remainingBatteryPercentage == null || batterySoh == null || numberOfBatteryCycles == null) {
			throw new BusinessException("parameter.notEmpty");
		}
		boolean result = deviceInfoService.update(Wrappers.<DeviceInfoEntity>lambdaUpdate().set(DeviceInfoEntity::getRemainingBatteryPercentage, remainingBatteryPercentage).set(DeviceInfoEntity::getBatterySoh, batterySoh).set(DeviceInfoEntity::getNumberOfBatteryCycles, numberOfBatteryCycles).eq(DeviceInfoEntity::getId, deviceInfo.getId()));
		return R.status(result);
	}

	/**
	 * 设备信息表 修改
	 */
	@PostMapping("/deviceInfo/updateDeviceName")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "修改", notes = "传入deviceInfo")
	public R<Boolean> updateDeviceName(@Valid @RequestBody DeviceInfoEntity deviceInfo) {
		Long id = deviceInfo.getId();
		String deviceName = deviceInfo.getDeviceName();
		if (id == null || StringUtil.isBlank(deviceName)) {
			throw new BusinessException("parameter.notEmpty");
		}
		boolean result = deviceInfoService.update(Wrappers.<DeviceInfoEntity>lambdaUpdate().set(DeviceInfoEntity::getDeviceName, deviceName).eq(DeviceInfoEntity::getId, deviceInfo.getId()));
		return R.status(result);
	}


	/**
	 * 设备信息表 分页
	 */
	@GetMapping("/deviceInfo/page")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "分页", notes = "传入deviceInfo")
	public R<IPage<DeviceInfoVO>> appQueryPage(@ApiIgnore @RequestParam Map<String, Object> deviceInfo, Query query) {
		IPage<DeviceInfoVO> pages = deviceInfoService.appQueryPage(Condition.getPage(query), deviceInfo);
		return R.data(pages);
	}

	/**
	 * 异常日志表 新增
	 */
	@PostMapping("/exceptionLog/save")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "异常日志新增", notes = "传入exceptionLog")
	public R save(@Valid @RequestBody ExceptionLogEntity exceptionLog) {
		return R.status(exceptionLogService.save(exceptionLog));
	}

	/**
	 * 异常日志列表查询
	 *
	 * @param exceptionLog 查询条件
	 * @param query        分页查询条件
	 */
	@GetMapping("/exceptionLog/list")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "异常日志分页查询", notes = "传入exceptionLog")
	public R<IPage<ExceptionLogVO>> list(@ApiIgnore @RequestParam Map<String, Object> exceptionLog, Query query) {
		IPage<ExceptionLogEntity> pages = exceptionLogService.page(Condition.getPage(query), Condition.getQueryWrapper(exceptionLog, ExceptionLogEntity.class).orderByDesc("create_time"));
		return R.data(ExceptionLogWrapper.build().pageVO(pages));
	}

	@PostMapping("/save/qualityGuarantee")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "保存质保信息", notes = "传入")
	public R<Boolean> appSaveQualityGuarantee(@Validated(value = ValidGroups.EditGroup.class) @RequestBody DeviceExitFactoryInfoEntity exitFactoryInfo) {
		return R.status(deviceExitFactoryInfoService.updateDeviceByDeviceSerialNumber(exitFactoryInfo));
	}

	@GetMapping("/query/qualityGuarantee")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "查询质保信息", notes = "传入")
	public R<DeviceExitFactoryInfoEntity> appQueryQualityGuarantee(@RequestParam String sn) {
		return R.data(deviceExitFactoryInfoService.appQueryQualityGuarantee(sn));
	}

	@PostMapping("/query/qualityGuaranteeList")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "查询质保信息列表", notes = "传入")
	public R<List<DeviceExitFactoryInfoVO>> appQueryQualityGuaranteeList(@RequestBody DeviceExitFactoryInfoVO vo) {
		return R.data(deviceExitFactoryInfoService.appQueryQualityGuaranteeList(vo));
	}

	/**
	 * 设备信息表 解绑
	 */
	@GetMapping("/deviceInfo/unbind")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "设备解绑", notes = "传入sn")
	public R<Boolean> unbindDeviceInfo(@RequestParam String sn) {
		if (StringUtil.isBlank(sn)) {
			throw new BusinessException("parameter.notEmpty");
		}
		boolean result = deviceInfoService.update(Wrappers.<DeviceInfoEntity>lambdaUpdate().set(DeviceInfoEntity::getIsDeleted, BladeConstant.DB_IS_DELETED).eq(DeviceInfoEntity::getPowerSupplySn, sn).eq(DeviceInfoEntity::getCreateUser, AuthUtil.getUserId()));
		if (!result) {
			throw new BusinessException("portable.device.unbind.fail");
		}
		return R.status(true);
	}
}
