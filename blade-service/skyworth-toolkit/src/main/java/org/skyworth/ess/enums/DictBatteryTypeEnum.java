package org.skyworth.ess.enums;

import java.util.Objects;

public enum DictBatteryTypeEnum {
	Unavailable("0000","Unavailable"),
	Lead_battery("0001","Lead-Acid battery"),
	PYLON("0002","PYLON Lithium-ion"),
	Lithium("0003","Dyness Lithium-ion"),
	Aobo("0004","Aobo Lithium-ion"),
	UZ("0005","UZ Lithium-ion"),
	VestMoods("0006","VestMoods Lithium-ion"),
	XinYi("0007","XinYi Lithium-ion");
	private String code;
	private String message;

	private DictBatteryTypeEnum(String code, String message) {
		this.code = code;
		this.message = message;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public static DictBatteryTypeEnum match(String key) {

		DictBatteryTypeEnum result = null;

		for (DictBatteryTypeEnum s : values()) {
			if (Objects.equals(s.getCode(), key)) {
				result = s;
				break;
			}
		}

		return result;
	}

}
