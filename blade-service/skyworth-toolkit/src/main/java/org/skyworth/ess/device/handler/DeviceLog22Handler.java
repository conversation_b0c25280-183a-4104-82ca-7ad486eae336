package org.skyworth.ess.device.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.incrementer.DefaultIdentifierGenerator;
import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.device.BinaryConvertUtils2;
import org.skyworth.ess.device.DeviceLogService;
import org.skyworth.ess.device.client.IErrorInfoClient;
import org.skyworth.ess.device.client.ITimeZoneDeviceServiceClient;
import org.skyworth.ess.device.entity.AlarmTypeEnum;
import org.skyworth.ess.device.entity.Constants;
import org.skyworth.ess.device.entity.DeviceLog22;
import org.skyworth.ess.log.entity.OriginalDataRecord;
import org.skyworth.ess.log.service.OriginalDataRecordService;
import org.skyworth.ess.threadpool.ThreadPoolCustom;
import org.skyworth.ess.timeUtil.TimeZoneInverter;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.sink.DorisSinkService;
import org.springblade.common.utils.CollectionUtils;
import org.springblade.common.utils.DateUtil;
import org.springblade.common.utils.tool.StringUtils;
import org.springblade.common.utils.tool.TimeUtils;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springblade.core.redis.cache.BladeRedis;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.*;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

/**
 * 处理所上报2.2的数据
 * */
@Component("DeviceLog2_" + Constants.SECOND)
@Slf4j
@RequiredArgsConstructor
public class DeviceLog22Handler implements DeviceLogService {

	@Autowired
	private BinaryConvertUtils2 binaryConvertUtils;

	@Autowired
	private DorisSinkService dorisSinkService;

	@Autowired
	private OriginalDataRecordService recordService;

	@Autowired
	private IErrorInfoClient errorInfoClient;

	@Value("${blade.switch.reserve}")
	private String switchFlag;
	@Autowired
	private BladeRedis bladeRedis;

	private static final String DEVICE_TIME_ADJUSTMENT = "device_time_adjustment_";

	private final Lock redisLock = new ReentrantLock();
	@Autowired
	private ITimeZoneDeviceServiceClient timeZoneDeviceServiceClient;

	public static void main(String[] args) {
		TimeZoneInverter.timeInverterToString("UTC-0",1749051478*1000);
	}
	/**
	 * 处理业务逻辑
	 * */
	@Override
	public void handler(List<JSONObject> dataList) {
		log.info("DeviceLog22Handler handler -> {}",dataList);
		    //业务数据
			List<JSONObject> jsonObjects=new ArrayList<>();
			//预留数据
		    List<JSONObject> jsonObjectsRes=new ArrayList<>();
		    List<Long> plantIdList = dataList.stream()
			      .filter(jsonObject -> jsonObject.containsKey("plantID"))
			      .map(jsonObject -> Long.parseLong(jsonObject.getString("plantID")))
			      .collect(Collectors.toList());
            List<Map<String,Object>> plantMapList=recordService.isParallelModeList(plantIdList);
		    Map<Long, String> resultMap = plantMapList.stream()
		    	.filter(map -> map.containsKey("plantId") && map.containsKey("modeType"))
		    	.collect(Collectors.toMap(
		    		map -> (Long) map.get("plantId"),
		    		map -> (String) map.get("modeType")
		    	));
			dataList.stream().parallel().forEach(data->{
				try {
					Long plantId = Long.parseLong(data.getString("plantID"));

					String modbusProtocolVersion = data.getString("commuVersion");
					String deviceSerialNumber = data.getString("deviceSn");
					String content = data.getString("content");
					String deviceDateTime= TimeZoneInverter.timeInverterToString("UTC-0",data.getLong("timeStamp")*1000);
					JSONObject tmp = binaryConvertUtils.getData(content, Constants.SECOND, modbusProtocolVersion);
					log.info("device_log22 binaryConvertUtils.getData  -> {} " ,tmp);
					if (tmp != null) {
						DeviceLog22 deviceLog22 = JSON.toJavaObject(tmp, DeviceLog22.class);   //将多余的字段去除
						JSONObject result = (JSONObject) JSON.toJSON(deviceLog22);
						JSONObject newObj = convertKeysToUnderscore(result); //将驼峰转化为下划线
						IdentifierGenerator identifierGenerator = new DefaultIdentifierGenerator();
						Number number = identifierGenerator.nextId(new Object());
						newObj.put("id", number.longValue());
						newObj.put("modbus_protocol_version", modbusProtocolVersion);
						newObj.put("device_serial_number", deviceSerialNumber);
						newObj.put("device_date_time", deviceDateTime);
						newObj.put("plant_id", plantId);
						String currentTime=TimeUtils.getCurrentTime();
						newObj.put("create_time", currentTime);
						newObj.put("update_time", currentTime);
						String modeType=resultMap.get(plantId);
						newObj.put("is_parallel_mode",modeType);
						//非并机数据
						if(ValidationUtil.isNotEmpty(modeType)&&!Constants.ONE.equals(modeType)){
							JSONObject notParallel=JSONObject.parseObject(newObj.toJSONString());
							//判断开关是否打开
							if(ValidationUtil.isNotEmpty(switchFlag)&&Constants.ONE.equals(switchFlag)){
								jsonObjectsRes.add(notParallel);
							}
                            processNonParallel(newObj);
						}
						jsonObjects.add(newObj);
					}
				}catch (Exception e){
					log.error(e.getMessage());
					log.error("insert data device_log22 error -> " ,e);
					//将数据插入记录表中  保留异常信息
					OriginalDataRecord record=new OriginalDataRecord();
					record.setData(data.toJSONString());
					record.setType("1");
					record.setExceptionInfo(e.getMessage());
					recordService.save(record);
				}
			});
			try{
				log.info("dorisSinkService device_log22 -> {}",jsonObjects);
				if(!jsonObjects.isEmpty()){
					dorisSinkService.write(jsonObjects,"device_log22");
				}
				if(!jsonObjectsRes.isEmpty()){
					dorisSinkService.write(jsonObjectsRes,"device_log22_res");
				}
			}catch (Exception e){
				throw new RuntimeException("deviceLog22Handler is exception");
			}
		// 解决逆变器上报时间不正确问题
		ThreadPoolCustom.getCustomThreadPool().submit(() -> checkEquipmentTime(dataList));
	}

	public  void updateDeviceStatus(String errorCode, String deviceSerialNumber, String deviceDateTime, Map<String, Map<String, Object>> errorResult,
									String errorCode2, String errorCode3, String errorCode4, String errorCode5,
									String batteryCurrent,Long plantId,String timeZone) {
		if(errorCode!=null && StringUtils.isNotEmpty(errorCode)){
			Map<String,Object> errorCodeData=new HashMap<>(8);
			errorCodeData.put("exceptionMessage", errorCode);
			errorCodeData.put("deviceSn", deviceSerialNumber);
			errorCodeData.put("deviceDateTime", deviceDateTime);
			errorCodeData.put("type", AlarmTypeEnum.DEVICE.getType());
			errorCodeData.put("plantId",plantId);
			errorCodeData.put("timeZone",timeZone);
			errorResult.put(Constants.ERROR_CODE,errorCodeData);
		}
		if(errorCode2!=null && StringUtils.isNotEmpty(errorCode2)){
			Map<String,Object> errorCode2Data=new HashMap<>(8);
			errorCode2Data.put("exceptionMessage", errorCode2);
			errorCode2Data.put("deviceSn", deviceSerialNumber);
			errorCode2Data.put("deviceDateTime", deviceDateTime);
			errorCode2Data.put("type", AlarmTypeEnum.DEVICE.getType());
			errorCode2Data.put("plantId",plantId);
			errorCode2Data.put("timeZone",timeZone);
			errorResult.put(Constants.ERROR_CODE2,errorCode2Data);
		}
		if(errorCode3!=null && StringUtils.isNotEmpty(errorCode3)){
			Map<String,Object> errorCode3Data=new HashMap<>(8);
			errorCode3Data.put("exceptionMessage", errorCode3);
			errorCode3Data.put("deviceSn", deviceSerialNumber);
			errorCode3Data.put("deviceDateTime", deviceDateTime);
			errorCode3Data.put("type", AlarmTypeEnum.DEVICE.getType());
			errorCode3Data.put("plantId",plantId);
			errorCode3Data.put("timeZone",timeZone);
			errorResult.put(Constants.ERROR_CODE3,errorCode3Data);
		}
		if(errorCode4!=null && StringUtils.isNotEmpty(errorCode4)){
			Map<String,Object> errorCode4Data=new HashMap<>(8);
			errorCode4Data.put("exceptionMessage", errorCode4);
			errorCode4Data.put("deviceSn", deviceSerialNumber);
			errorCode4Data.put("deviceDateTime", deviceDateTime);
			errorCode4Data.put("type", AlarmTypeEnum.BATTERY.getType());
			errorCode4Data.put("batteryCurrent",batteryCurrent);
			errorCode4Data.put("plantId",plantId);
			errorCode4Data.put("timeZone",timeZone);
			errorResult.put(Constants.ERROR_CODE_MESSAGE,errorCode4Data);
		}
		if(errorCode5!=null && StringUtils.isNotEmpty(errorCode5)){
			Map<String,Object> errorCode5Data=new HashMap<>(8);
			errorCode5Data.put("exceptionMessage", errorCode5);
			errorCode5Data.put("deviceSn", deviceSerialNumber);
			errorCode5Data.put("deviceDateTime", deviceDateTime);
			errorCode5Data.put("type",AlarmTypeEnum.BATTERY.getType());
			errorCode5Data.put("batteryCurrent",batteryCurrent);
			errorCode5Data.put("plantId",plantId);
			errorCode5Data.put("timeZone",timeZone);
			errorResult.put(Constants.ERROR_CODE_MESSAGE2,errorCode5Data);
		}
		errorInfoClient.errorInfo(errorResult);
	}

	private static JSONObject convertKeysToUnderscore(JSONObject jsonObject) {
		JSONObject convertedObject = new JSONObject();

		for (String key : jsonObject.keySet()) {
			String convertedKey = convertToUnderscore(key);
			Object value = jsonObject.get(key);
			if (value instanceof JSONObject) {
				value = convertKeysToUnderscore((JSONObject) value);
			}
			convertedObject.put(convertedKey, value);
		}
		return convertedObject;
	}

	private static String convertToUnderscore(String input) {
		StringBuilder result = new StringBuilder();
		boolean isFirstChar = true;

		for (char c : input.toCharArray()) {
			if (Character.isUpperCase(c)) {
				if (!isFirstChar) {
					result.append('_');
				}
				result.append(Character.toLowerCase(c));
			} else {
				result.append(c);
			}

			isFirstChar = false;
		}

		return result.toString();
	}

	/**
	 * 将非并机下的并机数据置0
	 * */
	private static void processNonParallel(JSONObject obj){
		obj.put("phase_l1_watt_of_grid_sum",0);
		obj.put("phase_l2_watt_of_grid_sum",0);
		obj.put("phase_l3_watt_of_grid_sum",0);
		obj.put("phase_l1_watt_of_load_sum",0);
		obj.put("phase_l2_watt_of_load_sum",0);
		obj.put("phase_l3_watt_of_load_sum",0);
		obj.put("daily_energy_of_load_sum",0);
		obj.put("monthly_energy_of_load_sum",0);
		obj.put("accumulated_energy_of_load_sum",0);
		obj.put("phase_l1_watt_sum_of_backup",0);
		obj.put("phase_l2_watt_sum_of_backup",0);
		obj.put("phase_l3_watt_sum_of_backup",0);
		obj.put("phase_l1_apparent_power_sum_of_backup",0);
		obj.put("phase_l2_apparent_power_sum_of_backup",0);
		obj.put("phase_l3_apparent_power_sum_of_backup",0);
		obj.put("daily_support_energy_sum_to_backup",0);
		obj.put("accumulated_support_energy_sum_to_backup",0);
		obj.put("phase_l1_watt_sum_of_generator",0);
		obj.put("phase_l2_watt_sum_of_generator",0);
		obj.put("phase_l3_watt_sum_of_generator",0);
		obj.put("phase_l1_apparent_power_sum_of_generator",0);
		obj.put("phase_l2_apparent_power_sum_of_generator",0);
		obj.put("phase_l3_apparent_power_sum_of_generator",0);
		obj.put("generator_today_energy_sum",0);
		obj.put("generator_total_energy_sum",0);
		obj.put("pvl_daily_generating_energy_sum",0);
		obj.put("pvl_accumulated_energy_sum",0);
		obj.put("totally_input_dc_watt_sum",0);
		obj.put("battery_power_sum",0);
		obj.put("battery_daily_charge_energy_parallel",0);
		obj.put("battery_accumulated_charge_energy_parallel",0);
		obj.put("battery_daily_discharge_energy_parallel",0);
		obj.put("battery_accumulated_discharge_energy_parallel",0);
	}
	/**
	 * 检查设备时间是否需要调整
	 * 遍历给定的设备信息列表，对于每个设备，如果其时间与当前时间相差超过10分钟，则将其添加到待调整列表中
	 * 如果待调整列表不为空，则更新这些设备的时间
	 *
	 * @param jsonObjectList 包含设备信息的JSON对象列表
	 */
	public void checkEquipmentTime(List<JSONObject> jsonObjectList) {
		// 检查输入列表是否为空
		if (CollectionUtils.isNullOrEmpty(jsonObjectList)) {
			return;
		}
		long currentDateTimeLong = Instant.now().toEpochMilli();
		log.info("Current time (milliseconds): {}", currentDateTimeLong);
		List<JSONObject> pendingJsonObjectList = new CopyOnWriteArrayList<>();
		for (JSONObject jsonObject : jsonObjectList) {
			if (!isValidJsonObject(jsonObject)) {
				continue;
			}
			try {
				processJsonObject(jsonObject, currentDateTimeLong, pendingJsonObjectList);
			} catch (JSONException e) {
				log.error("Invalid JSON object: {}", e.getMessage(), e);
			} catch (Exception e) {
				log.error("Error processing equipment time check: {}", e.getMessage(), e);
			}
		}
		if (!CollectionUtils.isNullOrEmpty(pendingJsonObjectList)) {
			log.info("Pending equipment time adjustment: {}", pendingJsonObjectList.size());
			updateDeviceTime(pendingJsonObjectList);
		}
	}

	/**
	 * 处理JSON对象，根据时间差决定是否将设备数据添加到待处理列表中
	 * 此方法的主要目的是为了处理来自不同设备的JSON数据，并根据设备时间和当前时间的差异
	 * 来决定是否需要将数据添加到待处理数据列表中如果设备的时间与当前时间相差不超过十分钟，
	 * 则认为数据是及时的，否则数据将被加入到待处理列表中，以便后续处理
	 *
	 * @param jsonObject            设备数据的JSON对象，包含设备ID和时间戳
	 * @param currentDateTimeLong   当前的日期和时间，以毫秒为单位
	 * @param pendingJsonObjectList 待处理的JSON对象列表，如果设备数据不及时，将被添加到此列表中
	 * @throws Exception 如果在处理过程中遇到任何异常，将抛出此异常
	 */
	private void processJsonObject(JSONObject jsonObject, long currentDateTimeLong,
								   List<JSONObject> pendingJsonObjectList) throws Exception {
		// 获取设备ID
		String plantId = jsonObject.getString("plantID");
		// 获取时间戳
		Long timeStamp = jsonObject.getLong("timeStamp");
		log.info("processJsonObject->plantId:{},timeStamp:{}", plantId, timeStamp);
		// 构造Redis键名
		String redisKey = DEVICE_TIME_ADJUSTMENT + plantId;
		// 加锁以确保线程安全
		redisLock.lock();
		try {
			// 如果Redis中已存在该设备的记录，则直接返回，避免重复处理
			if (bladeRedis.exists(redisKey)) {
				return;
			}
			// 将设备时间戳根据时区转换为毫秒
			long deviceDateTimeLong = timeStamp * 1000;
			// 如果设备时间与当前时间相差超过十分钟，则将数据添加到待处理列表中
			if (isMoreThanTenMinutesApart(currentDateTimeLong, deviceDateTimeLong)) {
				pendingJsonObjectList.add(jsonObject);
				// 在Redis中记录该设备，有效期为5分钟，以防止重复处理
				bladeRedis.setEx(redisKey, "1", Duration.ofMinutes(5));
			}
		} finally {
			// 解锁
			redisLock.unlock();
		}
	}

	/**
	 * 检查两个时间点是否相差超过10分钟
	 *
	 * @param currentDateTimeLong 当前时间
	 * @param deviceDateTimeLong  设备时间
	 * @return 如果两个时间点相差超过10分钟，则返回true；否则返回false
	 */
	private boolean isMoreThanTenMinutesApart(long currentDateTimeLong, long deviceDateTimeLong) {
		// 设备时区
		ZoneId deviceZoneId = ZoneId.of(CommonConstant.COMMON_DEFAULT_TIME_ZONE);
		// 将 currentDateTimeLong 转换为目标时区的 LocalDateTime
		LocalDateTime currentDateTime = Instant.ofEpochMilli(currentDateTimeLong)
			.atZone(deviceZoneId)
			.toLocalDateTime();
		log.info("isMoreThanTenMinutesApart -> currentDateTime======================>" + currentDateTime);
		// 系统时区
		ZoneId systemZoneId = ZoneId.of(CommonConstant.COMMON_UTC_TIME_ZONE);
		// 将 deviceDateTimeLong 转换为目标时区的 LocalDateTime
		LocalDateTime deviceDateTime = Instant.ofEpochMilli(deviceDateTimeLong)
			.atZone(systemZoneId)
			.toLocalDateTime();
		log.info("isMoreThanTenMinutesApart -> deviceDateTime======================>" + deviceDateTime);
		// 计算两个时间之间的分钟差
		long minutesDifference = ChronoUnit.MINUTES.between(currentDateTime,deviceDateTime);
		log.info("isMoreThanTenMinutesApart -> minutesDifference======================>" + minutesDifference);
		return Math.abs(minutesDifference) >= 10;
	}


	/**
	 * 更新设备时间
	 * 遍历待调整的设备列表，为每个设备发送时间调整请求
	 *
	 * @param jsonObjectList      包含待调整设备信息的JSON对象列表
	 */
	private void updateDeviceTime(List<JSONObject> jsonObjectList) {
		LocalDateTime deviceDateTimePush = LocalDateTime.now();
		// 遍历待调整的设备列表
		for (JSONObject jsonObject : jsonObjectList) {
			try {
				long plantId = jsonObject.getLong("plantID");
				String deviceSn = jsonObject.getString("deviceSn");
				sendAdjustmentRequest(deviceSn, plantId, deviceDateTimePush);
			} catch (Exception e) {
				log.error("Error processing device time adjustment: {}", e.getMessage(), e);
			}
		}
	}


	/**
	 * 发送设备时间调整请求
	 * 构建时间调整请求的JSON对象，并调用服务客户端发送请求
	 *
	 * @param deviceSn           设备序列号
	 * @param plantId            站点ID
	 * @param deviceDateTimePush 要设置的设备时间
	 */
	private void sendAdjustmentRequest(String deviceSn, long plantId, LocalDateTime deviceDateTimePush) {
		JSONObject jsonObjectPush = new JSONObject();
		jsonObjectPush.put("deviceSerialNumber", deviceSn);
		jsonObjectPush.put("plantId", plantId);
		jsonObjectPush.put("issueSetupType", "advancedSetup");
		jsonObjectPush.put("timeZone", CommonConstant.COMMON_DEFAULT_TIME_ZONE);
		jsonObjectPush.put("issueSource", CommonConstant.AUTO_ADJUST);
		JSONArray jsonArray = new JSONArray();
		JSONObject jsonItemContent = new JSONObject();
		String formattedTime = DateUtil.parseLocalDateTimeToStringDate(deviceDateTimePush, DateUtil.PATTERN_DATETIME);
		jsonItemContent.put("definition", "dateTime");
		jsonItemContent.put("data", formattedTime);
		jsonItemContent.put("definitionDesc", "Date and Time(From Backend)");
		jsonItemContent.put("dataDesc", formattedTime);
		jsonArray.add(jsonItemContent);
		jsonObjectPush.put("setupItems", jsonArray);
		timeZoneDeviceServiceClient.adjustEquipmentTime(jsonObjectPush);
	}

	/**
	 * 验证 JSON 对象的有效性
	 *
	 * @param jsonObject JSON 对象
	 * @return 如果 JSON 对象有效，则返回 true；否则返回 false
	 */
	private boolean isValidJsonObject(JSONObject jsonObject) {
		try {
			jsonObject.getLong("plantID");
			jsonObject.getLong("timeStamp");
			jsonObject.getString("deviceSn");
			return true;
		} catch (JSONException e) {
			return false;
		}
	}

}
