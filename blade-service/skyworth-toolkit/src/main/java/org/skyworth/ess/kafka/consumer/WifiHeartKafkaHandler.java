package org.skyworth.ess.kafka.consumer;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.device.client.IWifiStickPlantClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springblade.common.mq.kafka.config.KafkaDataHandler;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;


/**
 * 储能数据清洗公共类
 * <AUTHOR>
 */
@Component
@Slf4j
@AllArgsConstructor
public  class WifiHeartKafkaHandler implements KafkaDataHandler {
    private final Logger logger = LoggerFactory.getLogger(WifiHeartKafkaHandler.class);
    private static final String LINE = "line";


	private IWifiStickPlantClient wifiStickPlantClient;

    @Override
    public int handlerData(List<JSONObject> list) {

			// 用于存储已经处理过的key值
			Set<Object> seen = new HashSet<>();
			//过滤重复的数据
			List<JSONObject> filteredList = list.parallelStream()
				.filter(jsonObject -> isUnique(jsonObject, seen))
				.collect(Collectors.toList());
			List<String> deviceSnList=new ArrayList<>();
			filteredList.stream().parallel().forEach(jsonObject -> {
				try {
					JSONObject content = jsonObject.getJSONObject(LINE);
					String deviceSn=content.getString("deviceSn");
					deviceSnList.add(deviceSn);
				} catch (Exception e) {
					logger.warn("脏数据：" + jsonObject.get(LINE));
				}
			});
		   wifiStickPlantClient.updateHeartStatus(deviceSnList);

           return 0;
    }


	private static boolean isUnique(JSONObject jsonObject, Set<Object> seen) {
		if (!jsonObject.containsKey(WifiHeartKafkaHandler.LINE)) {
			return true;
		}

		Object value = jsonObject.get(WifiHeartKafkaHandler.LINE);

		if (value instanceof JSONObject) {
			JSONObject nestedObject = (JSONObject) value;
			return seen.add(nestedObject.toString());
		} else {
			return seen.add(value);
		}
	}


}
