package org.skyworth.ess.kafka.consumer;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.device.client.ITimeZoneDeviceServiceClient;
import org.skyworth.ess.device.handler.DeviceLog22Handler;
import org.skyworth.ess.log.mapper.OriginalDataRecordMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.mq.kafka.config.KafkaDataHandler;
import org.springblade.common.utils.tool.StringUtils;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 储能数据清洗公共类
 * <AUTHOR>
 */
@Component
@Slf4j
@AllArgsConstructor
public  class ErrorInfoKafkaHandler implements KafkaDataHandler {
    private final Logger logger = LoggerFactory.getLogger(ErrorInfoKafkaHandler.class);
    private static final String LINE = "line";

	private DeviceLog22Handler deviceLog22Handler;

	private OriginalDataRecordMapper originalDataRecordMapper;

	private ITimeZoneDeviceServiceClient timeZoneDeviceServiceClient;


    @Override
    public int handlerData(List<JSONObject> list) {
		logger.info("errorInfoKafkaHandler handlerData start,list : ========================>{}", JSONObject.toJSONString(list));
		// 用于存储已经处理过的key值
		Set<Object> seen = new HashSet<>();
		//过滤重复的数据
		List<JSONObject> filteredList = list.parallelStream()
			.filter(jsonObject -> isUnique(jsonObject, seen))
			.collect(Collectors.toList());
		// 提取 plantIds 并获取对应的时区映射
		List<Long> plantIds = list.stream()
			.map(json -> json.getJSONObject(LINE))
			.filter(Objects::nonNull)
			.map(line -> line.getLong("plantID"))
			.filter(Objects::nonNull)
			.collect(Collectors.toList());
		Map<String, String> timeZoneMap = timeZoneDeviceServiceClient.getMapFromCacheByPlantIdList(plantIds).getData();
		filteredList.stream().parallel().forEach(jsonObject -> {
				try {
					JSONObject content = jsonObject.getJSONObject(LINE);
					//内容体数据处理 分发到具体的处理类
					Map<String,Map<String,Object>> errorResult=new HashMap<>(8);
					String deviceSn=content.getString("deviceSn");
					String plantId=content.getString("plantID");
					String deviceDateTime=String.valueOf(content.getLong("timeStamp"));
					String error1=binaryTransfer(content.getInteger("error_code_table1"));
					String error2=binaryTransfer(content.getInteger("error_code_table2"));
					String error3=binaryTransfer(content.getInteger("error_code_table3"));
					String error4=binaryTransfer(content.getInteger("error_code_table4"));
					Integer errorCodeTable5 = content.getInteger("error_code_table5");
					String error5 = "";
					if(errorCodeTable5 != null) {
						error5 = binaryTransfer(errorCodeTable5);
					}
					//储能状态
					String batteryCurrent=binaryTransfer(content.getInteger("system_Work_table"));
					StringBuilder sb=new StringBuilder(batteryCurrent);
					sb.reverse();
					StringBuilder batteryStatusTmp=new StringBuilder(sb.substring(4,6));
					String batteryStatus=batteryStatusTmp.reverse().toString();
					//设备时区
					String timeZone = timeZoneMap.getOrDefault(plantId, CommonConstant.COMMON_DEFAULT_TIME_ZONE);
					if("01".equals(batteryStatus)){
						//充电
						batteryStatus="1";
					}else if("10".equals(batteryStatus)){
						//放电
						batteryStatus="2";
					}else if("00".equals(batteryStatus)){
						//不存在 离线
						batteryStatus="5";
					}else if("11".equals(batteryStatus)){
						//满充 为 11 更新为在线
						batteryStatus="3";
					} else {
						//其他情况 默认充电
						batteryStatus="1";
					}
					List<Map<String,Object>> existPlant=originalDataRecordMapper.isExistPlant(Long.parseLong(plantId),deviceSn);
					if(ValidationUtil.isNotEmpty(existPlant)&&!existPlant.isEmpty()){
						//刷新设备,站点状态
						deviceLog22Handler.updateDeviceStatus(error1,deviceSn,deviceDateTime,errorResult,error2,error3,error4
							,error5,batteryStatus,Long.parseLong(plantId),timeZone);
					}
				} catch (Exception e) {
					logger.warn("脏数据：" + jsonObject.get(LINE));
				}
			});

           return 0;
    }

	private static String binaryTransfer(int decimal){
		String binary = Integer.toBinaryString(decimal);
		int length = binary.length();
		StringBuilder sb = new StringBuilder(binary);
		while (length < 16) {
			sb.insert(0, '0');
			length++;
		}
		return sb.toString();
	}

	private static boolean isUnique(JSONObject jsonObject, Set<Object> seen) {
		if (!jsonObject.containsKey(ErrorInfoKafkaHandler.LINE)) {
			return true;
		}

		Object value = jsonObject.get(ErrorInfoKafkaHandler.LINE);

		if (value instanceof JSONObject) {
			JSONObject nestedObject = (JSONObject) value;
			return seen.add(nestedObject.toString());
		} else {
			return seen.add(value);
		}
	}


}
